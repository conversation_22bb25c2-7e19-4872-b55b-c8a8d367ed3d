var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./TrailMaker.js", "./Options/Classes/Trail.js", "./Options/Interfaces/ITrail.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadExternalTrailInteraction = loadExternalTrailInteraction;
    const TrailMaker_js_1 = require("./TrailMaker.js");
    async function loadExternalTrailInteraction(engine, refresh = true) {
        engine.checkVersion("3.8.1");
        await engine.addInteractor("externalTrail", container => {
            return Promise.resolve(new TrailMaker_js_1.TrailMaker(container));
        }, refresh);
    }
    __exportStar(require("./Options/Classes/Trail.js"), exports);
    __exportStar(require("./Options/Interfaces/ITrail.js"), exports);
});
