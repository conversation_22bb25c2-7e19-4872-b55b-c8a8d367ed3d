var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./EmittersPlugin.js", "./ShapeManager.js", "./EmitterContainer.js", "./EmitterShapeBase.js", "./EmittersEngine.js", "./IEmitterShape.js", "./IEmitterShapeGenerator.js", "./Enums/EmitterClickMode.js", "./IRandomPositionData.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.loadEmittersPlugin = loadEmittersPlugin;
    const EmittersPlugin_js_1 = require("./EmittersPlugin.js");
    const ShapeManager_js_1 = require("./ShapeManager.js");
    async function loadEmittersPlugin(engine, refresh = true) {
        engine.checkVersion("3.8.1");
        if (!engine.emitterShapeManager) {
            engine.emitterShapeManager = new ShapeManager_js_1.ShapeManager(engine);
        }
        if (!engine.addEmitterShapeGenerator) {
            engine.addEmitterShapeGenerator = (name, generator) => {
                engine.emitterShapeManager?.addShapeGenerator(name, generator);
            };
        }
        const plugin = new EmittersPlugin_js_1.EmittersPlugin(engine);
        await engine.addPlugin(plugin, refresh);
    }
    __exportStar(require("./EmitterContainer.js"), exports);
    __exportStar(require("./EmitterShapeBase.js"), exports);
    __exportStar(require("./EmittersEngine.js"), exports);
    __exportStar(require("./IEmitterShape.js"), exports);
    __exportStar(require("./IEmitterShapeGenerator.js"), exports);
    __exportStar(require("./Enums/EmitterClickMode.js"), exports);
    __exportStar(require("./IRandomPositionData.js"), exports);
});
