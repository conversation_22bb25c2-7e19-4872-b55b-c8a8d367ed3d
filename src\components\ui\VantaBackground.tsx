'use client';

import { useEffect, useRef } from 'react';

declare global {
  interface Window {
    VANTA: any;
    THREE: any;
  }
}

interface VantaBackgroundProps {
  effect?: 'waves' | 'net' | 'fog' | 'birds';
  className?: string;
}

export default function VantaBackground({ effect = 'net', className = '' }: VantaBackgroundProps) {
  const vantaRef = useRef<HTMLDivElement>(null);
  const vantaEffect = useRef<any>(null);

  useEffect(() => {
    // Load Three.js and Vanta.js scripts
    const loadScripts = async () => {
      // Load Three.js
      if (!window.THREE) {
        const threeScript = document.createElement('script');
        threeScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js';
        document.head.appendChild(threeScript);
        
        await new Promise((resolve) => {
          threeScript.onload = resolve;
        });
      }

      // Load Vanta.js effect
      if (!window.VANTA) {
        const vantaScript = document.createElement('script');
        vantaScript.src = `https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.${effect}.min.js`;
        document.head.appendChild(vantaScript);
        
        await new Promise((resolve) => {
          vantaScript.onload = resolve;
        });
      }

      // Initialize Vanta effect
      if (vantaRef.current && window.VANTA && window.THREE) {
        vantaEffect.current = window.VANTA[effect.toUpperCase()]({
          el: vantaRef.current,
          THREE: window.THREE,
          mouseControls: true,
          touchControls: true,
          gyroControls: false,
          minHeight: 200.00,
          minWidth: 200.00,
          scale: 1.00,
          scaleMobile: 1.00,
          // Net-specific options
          ...(effect === 'net' && {
            color: 0x00d4ff,
            backgroundColor: 0x0a0a0a,
            points: 8.00,
            maxDistance: 25.00,
            spacing: 20.00
          }),
          // Waves-specific options
          ...(effect === 'waves' && {
            color: 0x00ff88,
            waveHeight: 15.00,
            waveSpeed: 0.75,
            zoom: 0.65
          }),
          // Fog-specific options
          ...(effect === 'fog' && {
            highlightColor: 0x8b5cf6,
            midtoneColor: 0x00d4ff,
            lowlightColor: 0x00ff88,
            baseColor: 0x0a0a0a,
            blurFactor: 0.6,
            speed: 1.60,
            zoom: 1.00
          })
        });
      }
    };

    loadScripts();

    return () => {
      if (vantaEffect.current) {
        vantaEffect.current.destroy();
      }
    };
  }, [effect]);

  return (
    <div
      ref={vantaRef}
      className={`fixed inset-0 -z-10 bg-gradient-to-br from-black via-gray-900 to-black ${className}`}
      style={{ zIndex: -1 }}
    >
      {/* Fallback animated background */}
      <div className="absolute inset-0 opacity-30">
        {[...Array(100)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-neon-blue rounded-full animate-pulse"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${2 + Math.random() * 2}s`,
            }}
          />
        ))}
      </div>
    </div>
  );
}
