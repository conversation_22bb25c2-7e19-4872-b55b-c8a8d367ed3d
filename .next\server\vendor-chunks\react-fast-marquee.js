"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-fast-marquee";
exports.ids = ["vendor-chunks/react-fast-marquee"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-fast-marquee/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-fast-marquee/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nfunction ___$insertStyle(css) {\n    if (!css || \"undefined\" === 'undefined') {\n        return;\n    }\n    const style = document.createElement('style');\n    style.setAttribute('type', 'text/css');\n    style.innerHTML = css;\n    document.head.appendChild(style);\n    return css;\n}\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nvar React = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === 'object' && 'default' in e ? e : {\n        'default': e\n    };\n}\nvar React__default = /*#__PURE__*/ _interopDefaultLegacy(React);\n___$insertStyle(\".rfm-marquee-container {\\n  overflow-x: hidden;\\n  display: flex;\\n  flex-direction: row;\\n  position: relative;\\n  width: var(--width);\\n  transform: var(--transform);\\n}\\n.rfm-marquee-container:hover div {\\n  animation-play-state: var(--pause-on-hover);\\n}\\n.rfm-marquee-container:active div {\\n  animation-play-state: var(--pause-on-click);\\n}\\n\\n.rfm-overlay {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n}\\n.rfm-overlay::before, .rfm-overlay::after {\\n  background: linear-gradient(to right, var(--gradient-color), rgba(255, 255, 255, 0));\\n  content: \\\"\\\";\\n  height: 100%;\\n  position: absolute;\\n  width: var(--gradient-width);\\n  z-index: 2;\\n  pointer-events: none;\\n  touch-action: none;\\n}\\n.rfm-overlay::after {\\n  right: 0;\\n  top: 0;\\n  transform: rotateZ(180deg);\\n}\\n.rfm-overlay::before {\\n  left: 0;\\n  top: 0;\\n}\\n\\n.rfm-marquee {\\n  flex: 0 0 auto;\\n  min-width: var(--min-width);\\n  z-index: 1;\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  animation: scroll var(--duration) linear var(--delay) var(--iteration-count);\\n  animation-play-state: var(--play);\\n  animation-delay: var(--delay);\\n  animation-direction: var(--direction);\\n}\\n@keyframes scroll {\\n  0% {\\n    transform: translateX(0%);\\n  }\\n  100% {\\n    transform: translateX(-100%);\\n  }\\n}\\n\\n.rfm-initial-child-container {\\n  flex: 0 0 auto;\\n  display: flex;\\n  min-width: auto;\\n  flex-direction: row;\\n  align-items: center;\\n}\\n\\n.rfm-child {\\n  transform: var(--transform);\\n}\");\nconst Marquee = React.forwardRef(function Marquee({ style = {}, className = \"\", autoFill = false, play = true, pauseOnHover = false, pauseOnClick = false, direction = \"left\", speed = 50, delay = 0, loop = 0, gradient = false, gradientColor = \"white\", gradientWidth = 200, onFinish, onCycleComplete, onMount, children }, ref) {\n    // React Hooks\n    const [containerWidth, setContainerWidth] = React.useState(0);\n    const [marqueeWidth, setMarqueeWidth] = React.useState(0);\n    const [multiplier, setMultiplier] = React.useState(1);\n    const [isMounted, setIsMounted] = React.useState(false);\n    const rootRef = React.useRef(null);\n    const containerRef = ref || rootRef;\n    const marqueeRef = React.useRef(null);\n    // Calculate width of container and marquee and set multiplier\n    const calculateWidth = React.useCallback({\n        \"Marquee.Marquee.useCallback[calculateWidth]\": ()=>{\n            if (marqueeRef.current && containerRef.current) {\n                const containerRect = containerRef.current.getBoundingClientRect();\n                const marqueeRect = marqueeRef.current.getBoundingClientRect();\n                let containerWidth = containerRect.width;\n                let marqueeWidth = marqueeRect.width;\n                // Swap width and height if direction is up or down\n                if (direction === \"up\" || direction === \"down\") {\n                    containerWidth = containerRect.height;\n                    marqueeWidth = marqueeRect.height;\n                }\n                if (autoFill && containerWidth && marqueeWidth) {\n                    setMultiplier(marqueeWidth < containerWidth ? Math.ceil(containerWidth / marqueeWidth) : 1);\n                } else {\n                    setMultiplier(1);\n                }\n                setContainerWidth(containerWidth);\n                setMarqueeWidth(marqueeWidth);\n            }\n        }\n    }[\"Marquee.Marquee.useCallback[calculateWidth]\"], [\n        autoFill,\n        containerRef,\n        direction\n    ]);\n    // Calculate width and multiplier on mount and on window resize\n    React.useEffect({\n        \"Marquee.Marquee.useEffect\": ()=>{\n            if (!isMounted) return;\n            calculateWidth();\n            if (marqueeRef.current && containerRef.current) {\n                const resizeObserver = new ResizeObserver({\n                    \"Marquee.Marquee.useEffect\": ()=>calculateWidth()\n                }[\"Marquee.Marquee.useEffect\"]);\n                resizeObserver.observe(containerRef.current);\n                resizeObserver.observe(marqueeRef.current);\n                return ({\n                    \"Marquee.Marquee.useEffect\": ()=>{\n                        if (!resizeObserver) return;\n                        resizeObserver.disconnect();\n                    }\n                })[\"Marquee.Marquee.useEffect\"];\n            }\n        }\n    }[\"Marquee.Marquee.useEffect\"], [\n        calculateWidth,\n        containerRef,\n        isMounted\n    ]);\n    // Recalculate width when children change\n    React.useEffect({\n        \"Marquee.Marquee.useEffect\": ()=>{\n            calculateWidth();\n        }\n    }[\"Marquee.Marquee.useEffect\"], [\n        calculateWidth,\n        children\n    ]);\n    React.useEffect({\n        \"Marquee.Marquee.useEffect\": ()=>{\n            setIsMounted(true);\n        }\n    }[\"Marquee.Marquee.useEffect\"], []);\n    // Runs the onMount callback, if it is a function, when Marquee is mounted.\n    React.useEffect({\n        \"Marquee.Marquee.useEffect\": ()=>{\n            if (typeof onMount === \"function\") {\n                onMount();\n            }\n        }\n    }[\"Marquee.Marquee.useEffect\"], []);\n    // Animation duration\n    const duration = React.useMemo({\n        \"Marquee.Marquee.useMemo[duration]\": ()=>{\n            if (autoFill) {\n                return marqueeWidth * multiplier / speed;\n            } else {\n                return marqueeWidth < containerWidth ? containerWidth / speed : marqueeWidth / speed;\n            }\n        }\n    }[\"Marquee.Marquee.useMemo[duration]\"], [\n        autoFill,\n        containerWidth,\n        marqueeWidth,\n        multiplier,\n        speed\n    ]);\n    const containerStyle = React.useMemo({\n        \"Marquee.Marquee.useMemo[containerStyle]\": ()=>Object.assign(Object.assign({}, style), {\n                [\"--pause-on-hover\"]: !play || pauseOnHover ? \"paused\" : \"running\",\n                [\"--pause-on-click\"]: !play || pauseOnHover && !pauseOnClick || pauseOnClick ? \"paused\" : \"running\",\n                [\"--width\"]: direction === \"up\" || direction === \"down\" ? `100vh` : \"100%\",\n                [\"--transform\"]: direction === \"up\" ? \"rotate(-90deg)\" : direction === \"down\" ? \"rotate(90deg)\" : \"none\"\n            })\n    }[\"Marquee.Marquee.useMemo[containerStyle]\"], [\n        style,\n        play,\n        pauseOnHover,\n        pauseOnClick,\n        direction\n    ]);\n    const gradientStyle = React.useMemo({\n        \"Marquee.Marquee.useMemo[gradientStyle]\": ()=>({\n                [\"--gradient-color\"]: gradientColor,\n                [\"--gradient-width\"]: typeof gradientWidth === \"number\" ? `${gradientWidth}px` : gradientWidth\n            })\n    }[\"Marquee.Marquee.useMemo[gradientStyle]\"], [\n        gradientColor,\n        gradientWidth\n    ]);\n    const marqueeStyle = React.useMemo({\n        \"Marquee.Marquee.useMemo[marqueeStyle]\": ()=>({\n                [\"--play\"]: play ? \"running\" : \"paused\",\n                [\"--direction\"]: direction === \"left\" ? \"normal\" : \"reverse\",\n                [\"--duration\"]: `${duration}s`,\n                [\"--delay\"]: `${delay}s`,\n                [\"--iteration-count\"]: !!loop ? `${loop}` : \"infinite\",\n                [\"--min-width\"]: autoFill ? `auto` : \"100%\"\n            })\n    }[\"Marquee.Marquee.useMemo[marqueeStyle]\"], [\n        play,\n        direction,\n        duration,\n        delay,\n        loop,\n        autoFill\n    ]);\n    const childStyle = React.useMemo({\n        \"Marquee.Marquee.useMemo[childStyle]\": ()=>({\n                [\"--transform\"]: direction === \"up\" ? \"rotate(90deg)\" : direction === \"down\" ? \"rotate(-90deg)\" : \"none\"\n            })\n    }[\"Marquee.Marquee.useMemo[childStyle]\"], [\n        direction\n    ]);\n    // Render {multiplier} number of children\n    const multiplyChildren = React.useCallback({\n        \"Marquee.Marquee.useCallback[multiplyChildren]\": (multiplier)=>{\n            return [\n                ...Array(Number.isFinite(multiplier) && multiplier >= 0 ? multiplier : 0)\n            ].map({\n                \"Marquee.Marquee.useCallback[multiplyChildren]\": (_, i)=>React__default['default'].createElement(React.Fragment, {\n                        key: i\n                    }, React.Children.map(children, {\n                        \"Marquee.Marquee.useCallback[multiplyChildren]\": (child)=>{\n                            return React__default['default'].createElement(\"div\", {\n                                style: childStyle,\n                                className: \"rfm-child\"\n                            }, child);\n                        }\n                    }[\"Marquee.Marquee.useCallback[multiplyChildren]\"]))\n            }[\"Marquee.Marquee.useCallback[multiplyChildren]\"]);\n        }\n    }[\"Marquee.Marquee.useCallback[multiplyChildren]\"], [\n        childStyle,\n        children\n    ]);\n    return !isMounted ? null : React__default['default'].createElement(\"div\", {\n        ref: containerRef,\n        style: containerStyle,\n        className: \"rfm-marquee-container \" + className\n    }, gradient && React__default['default'].createElement(\"div\", {\n        style: gradientStyle,\n        className: \"rfm-overlay\"\n    }), React__default['default'].createElement(\"div\", {\n        className: \"rfm-marquee\",\n        style: marqueeStyle,\n        onAnimationIteration: onCycleComplete,\n        onAnimationEnd: onFinish\n    }, React__default['default'].createElement(\"div\", {\n        className: \"rfm-initial-child-container\",\n        ref: marqueeRef\n    }, React.Children.map(children, (child)=>{\n        return React__default['default'].createElement(\"div\", {\n            style: childStyle,\n            className: \"rfm-child\"\n        }, child);\n    })), multiplyChildren(multiplier - 1)), React__default['default'].createElement(\"div\", {\n        className: \"rfm-marquee\",\n        style: marqueeStyle\n    }, multiplyChildren(multiplier)));\n});\nexports[\"default\"] = Marquee; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-fast-marquee/dist/index.js\n");

/***/ })

};
;