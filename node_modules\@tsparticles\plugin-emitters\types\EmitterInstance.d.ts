import { type Container, type ICoordinates, type IDelta, type IDimension, type IHsl, type RecursivePartial } from "@tsparticles/engine";
import { Emitter } from "./Options/Classes/Emitter.js";
import type { Emitters } from "./Emitters.js";
import type { EmittersEngine } from "./EmittersEngine.js";
import type { IEmitter } from "./Options/Interfaces/IEmitter.js";
export declare class EmitterInstance {
    private readonly emitters;
    private readonly container;
    fill: boolean;
    readonly name?: string;
    options: Emitter;
    position: ICoordinates;
    size: IDimension;
    spawnColor?: IHsl;
    private _currentDuration;
    private _currentEmitDelay;
    private _currentSpawnDelay;
    private _duration?;
    private _emitDelay?;
    private readonly _engine;
    private _firstSpawn;
    private readonly _immortal;
    private readonly _initialPosition?;
    private _lifeCount;
    private _mutationObserver?;
    private readonly _particlesOptions;
    private _paused;
    private _resizeObserver?;
    private readonly _shape?;
    private _size;
    private _spawnDelay?;
    private _startParticlesAdded;
    constructor(engine: EmittersEngine, emitters: Emitters, container: Container, options: Emitter | RecursivePartial<IEmitter>, position?: ICoordinates);
    externalPause(): void;
    externalPlay(): void;
    init(): Promise<void>;
    pause(): void;
    play(): void;
    resize(): void;
    update(delta: IDelta): void;
    private _calcPosition;
    private _calcSize;
    private readonly _destroy;
    private _emit;
    private _emitParticles;
    private readonly _prepareToDie;
    private readonly _setColorAnimation;
}
