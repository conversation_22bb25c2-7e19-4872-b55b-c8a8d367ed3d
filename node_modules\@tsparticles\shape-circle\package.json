{"name": "@tsparticles/shape-circle", "version": "3.8.1", "description": "tsParticles circle shape", "homepage": "https://particles.js.org", "repository": {"type": "git", "url": "git+https://github.com/tsparticles/tsparticles.git", "directory": "shapes/circle"}, "keywords": ["front-end", "frontend", "tsparticles", "particles", "particle", "canvas", "jsparticles", "xparticles", "particles-js", "particles.js", "particles-ts", "particles.ts", "typescript", "javascript", "animation", "web", "html5", "web-design", "webdesign", "css", "html", "css3", "animated", "background", "tsparticles-shape"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/tsparticles/tsparticles/issues"}, "sideEffects": false, "jsdelivr": "tsparticles.shape.circle.min.js", "unpkg": "tsparticles.shape.circle.min.js", "browser": "browser/index.js", "main": "cjs/index.js", "module": "esm/index.js", "types": "types/index.d.ts", "exports": {".": {"types": "./types/index.d.ts", "browser": "./browser/index.js", "import": "./esm/index.js", "require": "./cjs/index.js", "umd": "./umd/index.js", "default": "./cjs/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@tsparticles/engine": "3.8.1"}, "publishConfig": {"access": "public"}}