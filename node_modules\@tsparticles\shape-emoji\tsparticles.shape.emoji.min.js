/*! For license information please see tsparticles.shape.emoji.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var o="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var i in o)("object"==typeof exports?exports:e)[i]=o[i]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},o={};function i(e){var n=o[e];if(void 0!==n)return n.exports;var a=o[e]={exports:{}};return t[e](a,a.exports,i),a.exports}i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};i.r(n),i.d(n,{loadEmojiShape:()=>l});var a=i(303);const r='"Twemoji Mozilla", Apple Color Emoji, "Segoe UI Emoji", "Noto Color Emoji", "EmojiOne Color"';class s{constructor(){this.validTypes=["emoji"],this._emojiShapeDict=new Map}destroy(){for(const[e,t]of this._emojiShapeDict)t instanceof ImageBitmap&&t?.close(),this._emojiShapeDict.delete(e)}draw(e){const t=e.particle.emojiDataKey;if(!t)return;const o=this._emojiShapeDict.get(t);o&&function(e,t){const{context:o,opacity:i}=e,n=o.globalAlpha;if(!t)return;const a=t.width,r=.5*a;o.globalAlpha=i,o.drawImage(t,-r,-r,a,a),o.globalAlpha=n}(e,o)}async init(e){const t=e.actualOptions,{validTypes:o}=this;if(!o.find((e=>(0,a.isInArray)(e,t.particles.shape.type))))return;const i=[(0,a.loadFont)(r)],n=o.map((e=>t.particles.shape.options[e])).find((e=>!!e));n&&(0,a.executeOnSingleOrMultiple)(n,(e=>{e.font&&i.push((0,a.loadFont)(e.font))})),await Promise.all(i)}particleDestroy(e){e.emojiDataKey=void 0}particleInit(e,t){const o=t.shapeData;if(!o?.value)return;const i=(0,a.itemFromSingleOrMultiple)(o.value,t.randomIndexData);if(!i)return;const n="string"==typeof i?{font:o.font??r,padding:o.padding??0,value:i}:{font:r,padding:0,...o,...i},s=n.font,l=n.value,p=`${l}_${s}`;if(this._emojiShapeDict.has(p))return void(t.emojiDataKey=p);const c=2*n.padding,f=(0,a.getRangeMax)(t.size.value),d=f+c,u=2*d;let m;if("undefined"!=typeof OffscreenCanvas){const e=new OffscreenCanvas(u,u),t=e.getContext("2d");if(!t)return;t.font=`400 ${2*f}px ${s}`,t.textBaseline="middle",t.textAlign="center",t.fillText(l,d,d),m=e.transferToImageBitmap()}else{const e=document.createElement("canvas");e.width=u,e.height=u;const t=e.getContext("2d");if(!t)return;t.font=`400 ${2*f}px ${s}`,t.textBaseline="middle",t.textAlign="center",t.fillText(l,d,d),m=e}this._emojiShapeDict.set(p,m),t.emojiDataKey=p}}async function l(e,t=!0){e.checkVersion("3.8.1"),await e.addShape(new s,t)}return n})()));