'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SplashScreenProps {
  onComplete: () => void;
}

export default function SplashScreen({ onComplete }: SplashScreenProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [showIAm, setShowIAm] = useState(false);
  const [showName, setShowName] = useState(false);
  const [showTitle, setShowTitle] = useState(false);
  const [fadeOutText, setFadeOutText] = useState(false);

  useEffect(() => {
    const sequence = async () => {
      // Step 1: "Hello World" - 2.5s
      await new Promise(resolve => setTimeout(resolve, 2500));
      setCurrentStep(1);

      // Step 2: "I am" appears - 1s
      await new Promise(resolve => setTimeout(resolve, 1000));
      setShowIAm(true);

      // Step 3: "<PERSON><PERSON><PERSON><PERSON>" appears below - 1.5s
      await new Promise(resolve => setTimeout(resolve, 1500));
      setShowName(true);

      // Step 4: "Software Developer" appears below - 1.5s
      await new Promise(resolve => setTimeout(resolve, 1500));
      setShowTitle(true);

      // Step 5: Wait 2s, then fade out "I am" and "Software Developer" - 2s
      await new Promise(resolve => setTimeout(resolve, 2000));
      setFadeOutText(true);

      // Step 6: Wait 1s, then complete (name will float to navbar) - 1s
      await new Promise(resolve => setTimeout(resolve, 1000));
      onComplete();
    };

    sequence();
  }, [onComplete]);

  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{
        background: 'rgba(0, 0, 0, 0.95)',
        backdropFilter: 'blur(20px)',
      }}
      initial={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 1, ease: "easeInOut" }}
    >
      {/* Subtle background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10" />
      </div>

      <div className="text-center relative z-10 space-y-6">
        {/* Step 1: Hello World */}
        {currentStep === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-6xl font-light text-white font-space"
          >
            <motion.span
              initial={{ width: 0 }}
              animate={{ width: "auto" }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
              className="inline-block overflow-hidden whitespace-nowrap border-r-2 border-blue-400"
              style={{ borderRight: currentStep === 0 ? '2px solid #60a5fa' : 'none' }}
            >
              Hello World
            </motion.span>
          </motion.div>
        )}

        {/* Step 2: I am */}
        {currentStep >= 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: fadeOutText ? 0 : 1,
              y: 0,
              transition: { duration: 0.8 }
            }}
            className="text-3xl md:text-4xl font-light text-gray-300 font-space"
          >
            I am
          </motion.div>
        )}

        {/* Step 3: Saurabh Dahariya */}
        {showName && (
          <motion.div
            layoutId="main-name"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-600 bg-clip-text text-transparent font-orbitron"
          >
            Saurabh Dahariya
          </motion.div>
        )}

        {/* Step 4: Software Developer */}
        {showTitle && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{
              opacity: fadeOutText ? 0 : 1,
              y: 0,
              transition: { duration: 0.8 }
            }}
            className="text-2xl md:text-3xl font-light text-gray-400 font-space"
          >
            Software Developer
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}
