'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';

interface SplashScreenProps {
  onComplete: () => void;
}

export default function SplashScreen({ onComplete }: SplashScreenProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [animationComplete, setAnimationComplete] = useState(false);

  const steps = [
    "Hello World",
    "I am <PERSON><PERSON><PERSON><PERSON>",
    "A Full Stack Software Developer"
  ];

  useEffect(() => {
    const timeline = gsap.timeline();

    // Step 1: Hello World (2s)
    timeline
      .to({}, { duration: 2 })
      .call(() => setCurrentStep(1))
      // Step 2: I am <PERSON><PERSON><PERSON><PERSON> (2.5s)
      .to({}, { duration: 2.5 })
      .call(() => setCurrentStep(2))
      // Step 3: A Full Stack Software Developer (2.5s)
      .to({}, { duration: 2.5 })
      .call(() => {
        setAnimationComplete(true);
        setTimeout(onComplete, 800);
      });

    return () => {
      timeline.kill();
    };
  }, [onComplete]);

  const typewriterVariants = {
    hidden: { width: 0 },
    visible: {
      width: "100%",
      transition: {
        duration: 1.5,
        ease: "easeInOut",
        delay: 0.3
      }
    }
  };

  const glowVariants = {
    initial: {
      textShadow: "0 0 10px rgba(0, 212, 255, 0.5)"
    },
    animate: {
      textShadow: [
        "0 0 10px rgba(0, 212, 255, 0.5)",
        "0 0 20px rgba(0, 212, 255, 0.8)",
        "0 0 30px rgba(0, 212, 255, 1)",
        "0 0 20px rgba(0, 212, 255, 0.8)",
        "0 0 10px rgba(0, 212, 255, 0.5)"
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <AnimatePresence>
      {!animationComplete && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black"
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.8 }}
        >
          {/* Animated background particles */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(50)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-neon-blue rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  delay: Math.random() * 3,
                }}
              />
            ))}
          </div>

          <div className="text-center relative z-10">
            <AnimatePresence mode="wait">
              {currentStep === 0 && (
                <motion.div
                  key="hello"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -30 }}
                  transition={{ duration: 0.8 }}
                  className="relative"
                >
                  <motion.div
                    className="text-4xl md:text-6xl font-bold text-neon-blue font-space overflow-hidden whitespace-nowrap border-r-2 border-neon-blue"
                    variants={typewriterVariants}
                    initial="hidden"
                    animate="visible"
                  >
                    Hello World
                  </motion.div>
                </motion.div>
              )}

              {currentStep === 1 && (
                <motion.div
                  key="name"
                  layoutId="main-name"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 1.2 }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  className="relative"
                >
                  <motion.div
                    className="text-5xl md:text-7xl font-bold gradient-text font-orbitron"
                    variants={glowVariants}
                    initial="initial"
                    animate="animate"
                  >
                    I am Saurabh Dahariya
                  </motion.div>
                </motion.div>
              )}

              {currentStep === 2 && (
                <motion.div
                  key="title"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -50 }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  className="space-y-4"
                >
                  <motion.div
                    className="text-5xl md:text-7xl font-bold gradient-text font-orbitron"
                    variants={glowVariants}
                    initial="initial"
                    animate="animate"
                  >
                    I am Saurabh Dahariya
                  </motion.div>
                  <motion.div
                    className="text-2xl md:text-3xl text-neon-green font-space"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5, duration: 1 }}
                  >
                    A Full Stack Software Developer
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
