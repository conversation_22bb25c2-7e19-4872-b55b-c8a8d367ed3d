'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';

interface SplashScreenProps {
  onComplete: () => void;
}

export default function SplashScreen({ onComplete }: SplashScreenProps) {
  const [showHi, setShowHi] = useState(true);
  const [showName, setShowName] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);

  useEffect(() => {
    const timeline = gsap.timeline();

    // Show "Hi." for 1.5 seconds
    timeline
      .to({}, { duration: 1.5 })
      .call(() => {
        setShowHi(false);
        setShowName(true);
      })
      .to({}, { duration: 2 })
      .call(() => {
        setAnimationComplete(true);
        setTimeout(onComplete, 500);
      });

    return () => {
      timeline.kill();
    };
  }, [onComplete]);

  return (
    <AnimatePresence>
      {!animationComplete && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black"
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="text-center">
            <AnimatePresence mode="wait">
              {showHi && (
                <motion.div
                  key="hi"
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 1.2 }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                  className="text-8xl md:text-9xl font-bold text-neon-blue glow-text"
                >
                  Hi.
                </motion.div>
              )}
              
              {showName && (
                <motion.div
                  key="name"
                  layoutId="main-name"
                  initial={{ opacity: 0, y: 50 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  className="text-6xl md:text-7xl font-bold gradient-text font-orbitron"
                >
                  Saurabh Dahariya
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
