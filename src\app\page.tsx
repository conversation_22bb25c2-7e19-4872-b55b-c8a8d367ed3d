'use client';

import { useState } from 'react';
import SplashScreen from '@/components/ui/SplashScreen';
import HeroSection from '@/components/sections/HeroSection';
import AboutSection from '@/components/sections/AboutSection';
import TechStackSection from '@/components/sections/TechStackSection';

export default function Home() {
  const [showSplash, setShowSplash] = useState(true);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  return (
    <>
      {showSplash && <SplashScreen onComplete={handleSplashComplete} />}

      {!showSplash && (
        <div className="min-h-screen">
          <HeroSection />
          <AboutSection />
          <TechStackSection />
        </div>
      )}
    </>
  );
}
