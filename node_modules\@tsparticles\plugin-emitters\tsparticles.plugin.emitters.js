/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/EmitterInstance.js":
/*!*****************************************!*\
  !*** ./dist/browser/EmitterInstance.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterInstance: () => (/* binding */ EmitterInstance)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Emitter.js */ \"./dist/browser/Options/Classes/Emitter.js\");\n/* harmony import */ var _Options_Classes_EmitterSize_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/EmitterSize.js */ \"./dist/browser/Options/Classes/EmitterSize.js\");\n\n\n\nconst half = 0.5,\n  defaultLifeDelay = 0,\n  minLifeCount = 0,\n  defaultSpawnDelay = 0,\n  defaultEmitDelay = 0,\n  defaultLifeCount = -1,\n  defaultColorAnimationFactor = 1;\nfunction setParticlesOptionsColor(particlesOptions, color) {\n  if (particlesOptions.color) {\n    particlesOptions.color.value = color;\n  } else {\n    particlesOptions.color = {\n      value: color\n    };\n  }\n}\nclass EmitterInstance {\n  constructor(engine, emitters, container, options, position) {\n    this.emitters = emitters;\n    this.container = container;\n    this._destroy = () => {\n      this._mutationObserver?.disconnect();\n      this._mutationObserver = undefined;\n      this._resizeObserver?.disconnect();\n      this._resizeObserver = undefined;\n      this.emitters.removeEmitter(this);\n      this._engine.dispatchEvent(\"emitterDestroyed\", {\n        container: this.container,\n        data: {\n          emitter: this\n        }\n      });\n    };\n    this._prepareToDie = () => {\n      if (this._paused) {\n        return;\n      }\n      const duration = this.options.life?.duration !== undefined ? (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(this.options.life.duration) : undefined,\n        minDuration = 0,\n        minLifeCount = 0;\n      if (this.container.retina.reduceFactor && (this._lifeCount > minLifeCount || this._immortal) && duration !== undefined && duration > minDuration) {\n        this._duration = duration * _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds;\n      }\n    };\n    this._setColorAnimation = (animation, initValue, maxValue, factor = defaultColorAnimationFactor) => {\n      const container = this.container;\n      if (!animation.enable) {\n        return initValue;\n      }\n      const colorOffset = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.randomInRange)(animation.offset),\n        delay = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(this.options.rate.delay),\n        emitFactor = delay * _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds / container.retina.reduceFactor,\n        defaultColorSpeed = 0,\n        colorSpeed = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(animation.speed ?? defaultColorSpeed);\n      return (initValue + colorSpeed * container.fpsLimit / emitFactor + colorOffset * factor) % maxValue;\n    };\n    this._engine = engine;\n    this._currentDuration = 0;\n    this._currentEmitDelay = 0;\n    this._currentSpawnDelay = 0;\n    this._initialPosition = position;\n    if (options instanceof _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_1__.Emitter) {\n      this.options = options;\n    } else {\n      this.options = new _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_1__.Emitter();\n      this.options.load(options);\n    }\n    this._spawnDelay = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(this.options.life.delay ?? defaultLifeDelay) * _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds / this.container.retina.reduceFactor;\n    this.position = this._initialPosition ?? this._calcPosition();\n    this.name = this.options.name;\n    this.fill = this.options.fill;\n    this._firstSpawn = !this.options.life.wait;\n    this._startParticlesAdded = false;\n    let particlesOptions = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.deepExtend)({}, this.options.particles);\n    particlesOptions ??= {};\n    particlesOptions.move ??= {};\n    particlesOptions.move.direction ??= this.options.direction;\n    if (this.options.spawnColor) {\n      this.spawnColor = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.rangeColorToHsl)(this._engine, this.options.spawnColor);\n    }\n    this._paused = !this.options.autoPlay;\n    this._particlesOptions = particlesOptions;\n    this._size = this._calcSize();\n    this.size = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getSize)(this._size, this.container.canvas.size);\n    this._lifeCount = this.options.life.count ?? defaultLifeCount;\n    this._immortal = this._lifeCount <= minLifeCount;\n    if (this.options.domId) {\n      const element = document.getElementById(this.options.domId);\n      if (element) {\n        this._mutationObserver = new MutationObserver(() => {\n          this.resize();\n        });\n        this._resizeObserver = new ResizeObserver(() => {\n          this.resize();\n        });\n        this._mutationObserver.observe(element, {\n          attributes: true,\n          attributeFilter: [\"style\", \"width\", \"height\"]\n        });\n        this._resizeObserver.observe(element);\n      }\n    }\n    const shapeOptions = this.options.shape,\n      shapeGenerator = this._engine.emitterShapeManager?.getShapeGenerator(shapeOptions.type);\n    if (shapeGenerator) {\n      this._shape = shapeGenerator.generate(this.position, this.size, this.fill, shapeOptions.options);\n    }\n    this._engine.dispatchEvent(\"emitterCreated\", {\n      container,\n      data: {\n        emitter: this\n      }\n    });\n    this.play();\n  }\n  externalPause() {\n    this._paused = true;\n    this.pause();\n  }\n  externalPlay() {\n    this._paused = false;\n    this.play();\n  }\n  async init() {\n    await this._shape?.init();\n  }\n  pause() {\n    if (this._paused) {\n      return;\n    }\n    delete this._emitDelay;\n  }\n  play() {\n    if (this._paused) {\n      return;\n    }\n    if (!(this.container.retina.reduceFactor && (this._lifeCount > minLifeCount || this._immortal || !this.options.life.count) && (this._firstSpawn || this._currentSpawnDelay >= (this._spawnDelay ?? defaultSpawnDelay)))) {\n      return;\n    }\n    if (this._emitDelay === undefined) {\n      const delay = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(this.options.rate.delay);\n      this._emitDelay = delay * _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds / this.container.retina.reduceFactor;\n    }\n    if (this._lifeCount > minLifeCount || this._immortal) {\n      this._prepareToDie();\n    }\n  }\n  resize() {\n    const initialPosition = this._initialPosition;\n    this.position = initialPosition && (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isPointInside)(initialPosition, this.container.canvas.size, _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.Vector.origin) ? initialPosition : this._calcPosition();\n    this._size = this._calcSize();\n    this.size = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getSize)(this._size, this.container.canvas.size);\n    this._shape?.resize(this.position, this.size);\n  }\n  update(delta) {\n    if (this._paused) {\n      return;\n    }\n    if (this._firstSpawn) {\n      this._firstSpawn = false;\n      this._currentSpawnDelay = this._spawnDelay ?? defaultSpawnDelay;\n      this._currentEmitDelay = this._emitDelay ?? defaultEmitDelay;\n    }\n    if (!this._startParticlesAdded) {\n      this._startParticlesAdded = true;\n      this._emitParticles(this.options.startCount);\n    }\n    if (this._duration !== undefined) {\n      this._currentDuration += delta.value;\n      if (this._currentDuration >= this._duration) {\n        this.pause();\n        if (this._spawnDelay !== undefined) {\n          delete this._spawnDelay;\n        }\n        if (!this._immortal) {\n          this._lifeCount--;\n        }\n        if (this._lifeCount > minLifeCount || this._immortal) {\n          this.position = this._calcPosition();\n          this._shape?.resize(this.position, this.size);\n          this._spawnDelay = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(this.options.life.delay ?? defaultLifeDelay) * _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.millisecondsToSeconds / this.container.retina.reduceFactor;\n        } else {\n          this._destroy();\n        }\n        this._currentDuration -= this._duration;\n        delete this._duration;\n      }\n    }\n    if (this._spawnDelay !== undefined) {\n      this._currentSpawnDelay += delta.value;\n      if (this._currentSpawnDelay >= this._spawnDelay) {\n        this._engine.dispatchEvent(\"emitterPlay\", {\n          container: this.container\n        });\n        this.play();\n        this._currentSpawnDelay -= this._currentSpawnDelay;\n        delete this._spawnDelay;\n      }\n    }\n    if (this._emitDelay !== undefined) {\n      this._currentEmitDelay += delta.value;\n      if (this._currentEmitDelay >= this._emitDelay) {\n        this._emit();\n        this._currentEmitDelay -= this._emitDelay;\n      }\n    }\n  }\n  _calcPosition() {\n    if (this.options.domId) {\n      const element = document.getElementById(this.options.domId);\n      if (element) {\n        const elRect = element.getBoundingClientRect(),\n          pxRatio = this.container.retina.pixelRatio;\n        return {\n          x: (elRect.x + elRect.width * half) * pxRatio,\n          y: (elRect.y + elRect.height * half) * pxRatio\n        };\n      }\n    }\n    return (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.calcPositionOrRandomFromSizeRanged)({\n      size: this.container.canvas.size,\n      position: this.options.position\n    });\n  }\n  _calcSize() {\n    const container = this.container;\n    if (this.options.domId) {\n      const element = document.getElementById(this.options.domId);\n      if (element) {\n        const elRect = element.getBoundingClientRect();\n        return {\n          width: elRect.width * container.retina.pixelRatio,\n          height: elRect.height * container.retina.pixelRatio,\n          mode: _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.PixelMode.precise\n        };\n      }\n    }\n    return this.options.size ?? (() => {\n      const size = new _Options_Classes_EmitterSize_js__WEBPACK_IMPORTED_MODULE_2__.EmitterSize();\n      size.load({\n        height: 0,\n        mode: _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.PixelMode.percent,\n        width: 0\n      });\n      return size;\n    })();\n  }\n  _emit() {\n    if (this._paused) {\n      return;\n    }\n    const quantity = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeValue)(this.options.rate.quantity);\n    this._emitParticles(quantity);\n  }\n  _emitParticles(quantity) {\n    const singleParticlesOptions = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(this._particlesOptions);\n    for (let i = 0; i < quantity; i++) {\n      const particlesOptions = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.deepExtend)({}, singleParticlesOptions);\n      if (this.spawnColor) {\n        const hslAnimation = this.options.spawnColor?.animation;\n        if (hslAnimation) {\n          const maxValues = {\n              h: 360,\n              s: 100,\n              l: 100\n            },\n            colorFactor = 3.6;\n          this.spawnColor.h = this._setColorAnimation(hslAnimation.h, this.spawnColor.h, maxValues.h, colorFactor);\n          this.spawnColor.s = this._setColorAnimation(hslAnimation.s, this.spawnColor.s, maxValues.s);\n          this.spawnColor.l = this._setColorAnimation(hslAnimation.l, this.spawnColor.l, maxValues.l);\n        }\n        setParticlesOptionsColor(particlesOptions, this.spawnColor);\n      }\n      const shapeOptions = this.options.shape;\n      let position = this.position;\n      if (this._shape) {\n        const shapePosData = this._shape.randomPosition();\n        if (shapePosData) {\n          position = shapePosData.position;\n          const replaceData = shapeOptions.replace;\n          if (replaceData.color && shapePosData.color) {\n            setParticlesOptionsColor(particlesOptions, shapePosData.color);\n          }\n          if (replaceData.opacity) {\n            if (particlesOptions.opacity) {\n              particlesOptions.opacity.value = shapePosData.opacity;\n            } else {\n              particlesOptions.opacity = {\n                value: shapePosData.opacity\n              };\n            }\n          }\n        } else {\n          position = null;\n        }\n      }\n      if (position) {\n        this.container.particles.addParticle(position, particlesOptions);\n      }\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/EmitterInstance.js?");

/***/ }),

/***/ "./dist/browser/EmitterShapeBase.js":
/*!******************************************!*\
  !*** ./dist/browser/EmitterShapeBase.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterShapeBase: () => (/* binding */ EmitterShapeBase)\n/* harmony export */ });\nclass EmitterShapeBase {\n  constructor(position, size, fill, options) {\n    this.position = position;\n    this.size = size;\n    this.fill = fill;\n    this.options = options;\n  }\n  resize(position, size) {\n    this.position = position;\n    this.size = size;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/EmitterShapeBase.js?");

/***/ }),

/***/ "./dist/browser/Emitters.js":
/*!**********************************!*\
  !*** ./dist/browser/Emitters.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitters: () => (/* binding */ Emitters)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Options/Classes/Emitter.js */ \"./dist/browser/Options/Classes/Emitter.js\");\n/* harmony import */ var _Enums_EmitterClickMode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Enums/EmitterClickMode.js */ \"./dist/browser/Enums/EmitterClickMode.js\");\n/* harmony import */ var _EmitterInstance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EmitterInstance.js */ \"./dist/browser/EmitterInstance.js\");\n\n\n\n\nclass Emitters {\n  constructor(engine, container) {\n    this.container = container;\n    this._engine = engine;\n    this.array = [];\n    this.emitters = [];\n    this.interactivityEmitters = {\n      random: {\n        count: 1,\n        enable: false\n      },\n      value: []\n    };\n    const defaultIndex = 0;\n    container.getEmitter = idxOrName => idxOrName === undefined || (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNumber)(idxOrName) ? this.array[idxOrName ?? defaultIndex] : this.array.find(t => t.name === idxOrName);\n    container.addEmitter = async (options, position) => this.addEmitter(options, position);\n    container.removeEmitter = idxOrName => {\n      const emitter = container.getEmitter(idxOrName);\n      if (emitter) {\n        this.removeEmitter(emitter);\n      }\n    };\n    container.playEmitter = idxOrName => {\n      const emitter = container.getEmitter(idxOrName);\n      if (emitter) {\n        emitter.externalPlay();\n      }\n    };\n    container.pauseEmitter = idxOrName => {\n      const emitter = container.getEmitter(idxOrName);\n      if (emitter) {\n        emitter.externalPause();\n      }\n    };\n  }\n  async addEmitter(options, position) {\n    const emitterOptions = new _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_1__.Emitter();\n    emitterOptions.load(options);\n    const emitter = new _EmitterInstance_js__WEBPACK_IMPORTED_MODULE_2__.EmitterInstance(this._engine, this, this.container, emitterOptions, position);\n    await emitter.init();\n    this.array.push(emitter);\n    return emitter;\n  }\n  handleClickMode(mode) {\n    const emitterOptions = this.emitters,\n      modeEmitters = this.interactivityEmitters;\n    if (mode !== _Enums_EmitterClickMode_js__WEBPACK_IMPORTED_MODULE_3__.EmitterClickMode.emitter) {\n      return;\n    }\n    let emittersModeOptions;\n    if (modeEmitters && (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isArray)(modeEmitters.value)) {\n      const minLength = 0;\n      if (modeEmitters.value.length > minLength && modeEmitters.random.enable) {\n        emittersModeOptions = [];\n        const usedIndexes = [];\n        for (let i = 0; i < modeEmitters.random.count; i++) {\n          const idx = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.arrayRandomIndex)(modeEmitters.value);\n          if (usedIndexes.includes(idx) && usedIndexes.length < modeEmitters.value.length) {\n            i--;\n            continue;\n          }\n          usedIndexes.push(idx);\n          emittersModeOptions.push((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.itemFromArray)(modeEmitters.value, idx));\n        }\n      } else {\n        emittersModeOptions = modeEmitters.value;\n      }\n    } else {\n      emittersModeOptions = modeEmitters?.value;\n    }\n    const emittersOptions = emittersModeOptions ?? emitterOptions,\n      ePosition = this.container.interactivity.mouse.clickPosition;\n    void (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.executeOnSingleOrMultiple)(emittersOptions, async emitter => {\n      await this.addEmitter(emitter, ePosition);\n    });\n  }\n  async init() {\n    this.emitters = this.container.actualOptions.emitters;\n    this.interactivityEmitters = this.container.actualOptions.interactivity.modes.emitters;\n    if (!this.emitters) {\n      return;\n    }\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isArray)(this.emitters)) {\n      for (const emitterOptions of this.emitters) {\n        await this.addEmitter(emitterOptions);\n      }\n    } else {\n      await this.addEmitter(this.emitters);\n    }\n  }\n  pause() {\n    for (const emitter of this.array) {\n      emitter.pause();\n    }\n  }\n  play() {\n    for (const emitter of this.array) {\n      emitter.play();\n    }\n  }\n  removeEmitter(emitter) {\n    const index = this.array.indexOf(emitter),\n      minIndex = 0,\n      deleteCount = 1;\n    if (index >= minIndex) {\n      this.array.splice(index, deleteCount);\n    }\n  }\n  resize() {\n    for (const emitter of this.array) {\n      emitter.resize();\n    }\n  }\n  stop() {\n    this.array = [];\n  }\n  update(delta) {\n    for (const emitter of this.array) {\n      emitter.update(delta);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/Emitters.js?");

/***/ }),

/***/ "./dist/browser/EmittersPlugin.js":
/*!****************************************!*\
  !*** ./dist/browser/EmittersPlugin.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmittersPlugin: () => (/* binding */ EmittersPlugin)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Options/Classes/Emitter.js */ \"./dist/browser/Options/Classes/Emitter.js\");\n/* harmony import */ var _Enums_EmitterClickMode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Enums/EmitterClickMode.js */ \"./dist/browser/Enums/EmitterClickMode.js\");\n/* harmony import */ var _Emitters_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Emitters.js */ \"./dist/browser/Emitters.js\");\n\n\n\n\nclass EmittersPlugin {\n  constructor(engine) {\n    this._engine = engine;\n    this.id = \"emitters\";\n  }\n  getPlugin(container) {\n    return Promise.resolve(new _Emitters_js__WEBPACK_IMPORTED_MODULE_1__.Emitters(this._engine, container));\n  }\n  loadOptions(options, source) {\n    if (!this.needsPlugin(options) && !this.needsPlugin(source)) {\n      return;\n    }\n    if (source?.emitters) {\n      options.emitters = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.executeOnSingleOrMultiple)(source.emitters, emitter => {\n        const tmp = new _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_2__.Emitter();\n        tmp.load(emitter);\n        return tmp;\n      });\n    }\n    const interactivityEmitters = source?.interactivity?.modes?.emitters;\n    if (interactivityEmitters) {\n      if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isArray)(interactivityEmitters)) {\n        options.interactivity.modes.emitters = {\n          random: {\n            count: 1,\n            enable: true\n          },\n          value: interactivityEmitters.map(s => {\n            const tmp = new _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_2__.Emitter();\n            tmp.load(s);\n            return tmp;\n          })\n        };\n      } else {\n        const emitterMode = interactivityEmitters;\n        if (emitterMode.value !== undefined) {\n          const defaultCount = 1;\n          if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isArray)(emitterMode.value)) {\n            options.interactivity.modes.emitters = {\n              random: {\n                count: emitterMode.random.count ?? defaultCount,\n                enable: emitterMode.random.enable ?? false\n              },\n              value: emitterMode.value.map(s => {\n                const tmp = new _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_2__.Emitter();\n                tmp.load(s);\n                return tmp;\n              })\n            };\n          } else {\n            const tmp = new _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_2__.Emitter();\n            tmp.load(emitterMode.value);\n            options.interactivity.modes.emitters = {\n              random: {\n                count: emitterMode.random.count ?? defaultCount,\n                enable: emitterMode.random.enable ?? false\n              },\n              value: tmp\n            };\n          }\n        } else {\n          const emitterOptions = options.interactivity.modes.emitters = {\n            random: {\n              count: 1,\n              enable: false\n            },\n            value: new _Options_Classes_Emitter_js__WEBPACK_IMPORTED_MODULE_2__.Emitter()\n          };\n          emitterOptions.value.load(interactivityEmitters);\n        }\n      }\n    }\n  }\n  needsPlugin(options) {\n    if (!options) {\n      return false;\n    }\n    const emitters = options.emitters;\n    return (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isArray)(emitters) && !!emitters.length || emitters !== undefined || !!options.interactivity?.events?.onClick?.mode && (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(_Enums_EmitterClickMode_js__WEBPACK_IMPORTED_MODULE_3__.EmitterClickMode.emitter, options.interactivity.events.onClick.mode);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/EmittersPlugin.js?");

/***/ }),

/***/ "./dist/browser/Enums/EmitterClickMode.js":
/*!************************************************!*\
  !*** ./dist/browser/Enums/EmitterClickMode.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterClickMode: () => (/* binding */ EmitterClickMode)\n/* harmony export */ });\nvar EmitterClickMode;\n(function (EmitterClickMode) {\n  EmitterClickMode[\"emitter\"] = \"emitter\";\n})(EmitterClickMode || (EmitterClickMode = {}));\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/Enums/EmitterClickMode.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Emitter.js":
/*!*************************************************!*\
  !*** ./dist/browser/Options/Classes/Emitter.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Emitter: () => (/* binding */ Emitter)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _EmitterLife_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EmitterLife.js */ \"./dist/browser/Options/Classes/EmitterLife.js\");\n/* harmony import */ var _EmitterRate_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EmitterRate.js */ \"./dist/browser/Options/Classes/EmitterRate.js\");\n/* harmony import */ var _EmitterShape_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./EmitterShape.js */ \"./dist/browser/Options/Classes/EmitterShape.js\");\n/* harmony import */ var _EmitterSize_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./EmitterSize.js */ \"./dist/browser/Options/Classes/EmitterSize.js\");\n\n\n\n\n\nclass Emitter {\n  constructor() {\n    this.autoPlay = true;\n    this.fill = true;\n    this.life = new _EmitterLife_js__WEBPACK_IMPORTED_MODULE_1__.EmitterLife();\n    this.rate = new _EmitterRate_js__WEBPACK_IMPORTED_MODULE_2__.EmitterRate();\n    this.shape = new _EmitterShape_js__WEBPACK_IMPORTED_MODULE_3__.EmitterShape();\n    this.startCount = 0;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.autoPlay !== undefined) {\n      this.autoPlay = data.autoPlay;\n    }\n    if (data.size !== undefined) {\n      if (!this.size) {\n        this.size = new _EmitterSize_js__WEBPACK_IMPORTED_MODULE_4__.EmitterSize();\n      }\n      this.size.load(data.size);\n    }\n    if (data.direction !== undefined) {\n      this.direction = data.direction;\n    }\n    this.domId = data.domId;\n    if (data.fill !== undefined) {\n      this.fill = data.fill;\n    }\n    this.life.load(data.life);\n    this.name = data.name;\n    this.particles = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.executeOnSingleOrMultiple)(data.particles, particles => {\n      return (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.deepExtend)({}, particles);\n    });\n    this.rate.load(data.rate);\n    this.shape.load(data.shape);\n    if (data.position !== undefined) {\n      this.position = {};\n      if (data.position.x !== undefined) {\n        this.position.x = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.position.x);\n      }\n      if (data.position.y !== undefined) {\n        this.position.y = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.position.y);\n      }\n    }\n    if (data.spawnColor !== undefined) {\n      if (this.spawnColor === undefined) {\n        this.spawnColor = new _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.AnimatableColor();\n      }\n      this.spawnColor.load(data.spawnColor);\n    }\n    if (data.startCount !== undefined) {\n      this.startCount = data.startCount;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/Options/Classes/Emitter.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/EmitterLife.js":
/*!*****************************************************!*\
  !*** ./dist/browser/Options/Classes/EmitterLife.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterLife: () => (/* binding */ EmitterLife)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass EmitterLife {\n  constructor() {\n    this.wait = false;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.count !== undefined) {\n      this.count = data.count;\n    }\n    if (data.delay !== undefined) {\n      this.delay = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.delay);\n    }\n    if (data.duration !== undefined) {\n      this.duration = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.duration);\n    }\n    if (data.wait !== undefined) {\n      this.wait = data.wait;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/Options/Classes/EmitterLife.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/EmitterRate.js":
/*!*****************************************************!*\
  !*** ./dist/browser/Options/Classes/EmitterRate.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterRate: () => (/* binding */ EmitterRate)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass EmitterRate {\n  constructor() {\n    this.quantity = 1;\n    this.delay = 0.1;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.quantity !== undefined) {\n      this.quantity = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.quantity);\n    }\n    if (data.delay !== undefined) {\n      this.delay = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.setRangeValue)(data.delay);\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/Options/Classes/EmitterRate.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/EmitterShape.js":
/*!******************************************************!*\
  !*** ./dist/browser/Options/Classes/EmitterShape.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterShape: () => (/* binding */ EmitterShape)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _EmitterShapeReplace_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EmitterShapeReplace.js */ \"./dist/browser/Options/Classes/EmitterShapeReplace.js\");\n\n\nclass EmitterShape {\n  constructor() {\n    this.options = {};\n    this.replace = new _EmitterShapeReplace_js__WEBPACK_IMPORTED_MODULE_1__.EmitterShapeReplace();\n    this.type = \"square\";\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.options !== undefined) {\n      this.options = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.deepExtend)({}, data.options ?? {});\n    }\n    this.replace.load(data.replace);\n    if (data.type !== undefined) {\n      this.type = data.type;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/Options/Classes/EmitterShape.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/EmitterShapeReplace.js":
/*!*************************************************************!*\
  !*** ./dist/browser/Options/Classes/EmitterShapeReplace.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterShapeReplace: () => (/* binding */ EmitterShapeReplace)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass EmitterShapeReplace {\n  constructor() {\n    this.color = false;\n    this.opacity = false;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.color !== undefined) {\n      this.color = data.color;\n    }\n    if (data.opacity !== undefined) {\n      this.opacity = data.opacity;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/Options/Classes/EmitterShapeReplace.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/EmitterSize.js":
/*!*****************************************************!*\
  !*** ./dist/browser/Options/Classes/EmitterSize.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterSize: () => (/* binding */ EmitterSize)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass EmitterSize {\n  constructor() {\n    this.mode = _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.PixelMode.percent;\n    this.height = 0;\n    this.width = 0;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.mode !== undefined) {\n      this.mode = data.mode;\n    }\n    if (data.height !== undefined) {\n      this.height = data.height;\n    }\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/Options/Classes/EmitterSize.js?");

/***/ }),

/***/ "./dist/browser/ShapeManager.js":
/*!**************************************!*\
  !*** ./dist/browser/ShapeManager.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShapeManager: () => (/* binding */ ShapeManager)\n/* harmony export */ });\nconst shapeGeneratorss = new Map();\nclass ShapeManager {\n  constructor(engine) {\n    this._engine = engine;\n  }\n  addShapeGenerator(name, generator) {\n    if (!this.getShapeGenerator(name)) {\n      shapeGeneratorss.set(name, generator);\n    }\n  }\n  getShapeGenerator(name) {\n    return shapeGeneratorss.get(name);\n  }\n  getSupportedShapeGenerators() {\n    return shapeGeneratorss.keys();\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/ShapeManager.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmitterClickMode: () => (/* reexport safe */ _Enums_EmitterClickMode_js__WEBPACK_IMPORTED_MODULE_3__.EmitterClickMode),\n/* harmony export */   EmitterShapeBase: () => (/* reexport safe */ _EmitterShapeBase_js__WEBPACK_IMPORTED_MODULE_2__.EmitterShapeBase),\n/* harmony export */   loadEmittersPlugin: () => (/* binding */ loadEmittersPlugin)\n/* harmony export */ });\n/* harmony import */ var _EmittersPlugin_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./EmittersPlugin.js */ \"./dist/browser/EmittersPlugin.js\");\n/* harmony import */ var _ShapeManager_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ShapeManager.js */ \"./dist/browser/ShapeManager.js\");\n/* harmony import */ var _EmitterShapeBase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./EmitterShapeBase.js */ \"./dist/browser/EmitterShapeBase.js\");\n/* harmony import */ var _Enums_EmitterClickMode_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Enums/EmitterClickMode.js */ \"./dist/browser/Enums/EmitterClickMode.js\");\n\n\nasync function loadEmittersPlugin(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  if (!engine.emitterShapeManager) {\n    engine.emitterShapeManager = new _ShapeManager_js__WEBPACK_IMPORTED_MODULE_0__.ShapeManager(engine);\n  }\n  if (!engine.addEmitterShapeGenerator) {\n    engine.addEmitterShapeGenerator = (name, generator) => {\n      engine.emitterShapeManager?.addShapeGenerator(name, generator);\n    };\n  }\n  const plugin = new _EmittersPlugin_js__WEBPACK_IMPORTED_MODULE_1__.EmittersPlugin(engine);\n  await engine.addPlugin(plugin, refresh);\n}\n\n\n\n\n\n\n\n\n//# sourceURL=webpack://@tsparticles/plugin-emitters/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});