/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/EmojiDrawer.js":
/*!*************************************!*\
  !*** ./dist/browser/EmojiDrawer.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmojiDrawer: () => (/* binding */ EmojiDrawer)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n\n\nconst defaultFont = '\"Twemoji Mozilla\", Apple Color Emoji, \"Segoe UI Emoji\", \"Noto Color Emoji\", \"EmojiOne Color\"',\n  noPadding = 0;\nclass EmojiDrawer {\n  constructor() {\n    this.validTypes = [\"emoji\"];\n    this._emojiShapeDict = new Map();\n  }\n  destroy() {\n    for (const [key, data] of this._emojiShapeDict) {\n      if (data instanceof ImageBitmap) {\n        data?.close();\n      }\n      this._emojiShapeDict.delete(key);\n    }\n  }\n  draw(data) {\n    const key = data.particle.emojiDataKey;\n    if (!key) {\n      return;\n    }\n    const image = this._emojiShapeDict.get(key);\n    if (!image) {\n      return;\n    }\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.drawEmoji)(data, image);\n  }\n  async init(container) {\n    const options = container.actualOptions,\n      {\n        validTypes\n      } = this;\n    if (!validTypes.find(t => (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(t, options.particles.shape.type))) {\n      return;\n    }\n    const promises = [(0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.loadFont)(defaultFont)],\n      shapeOptions = validTypes.map(t => options.particles.shape.options[t]).find(t => !!t);\n    if (shapeOptions) {\n      (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.executeOnSingleOrMultiple)(shapeOptions, shape => {\n        if (shape.font) {\n          promises.push((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.loadFont)(shape.font));\n        }\n      });\n    }\n    await Promise.all(promises);\n  }\n  particleDestroy(particle) {\n    particle.emojiDataKey = undefined;\n  }\n  particleInit(_container, particle) {\n    const double = 2,\n      shapeData = particle.shapeData;\n    if (!shapeData?.value) {\n      return;\n    }\n    const emoji = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(shapeData.value, particle.randomIndexData);\n    if (!emoji) {\n      return;\n    }\n    const emojiOptions = typeof emoji === \"string\" ? {\n        font: shapeData.font ?? defaultFont,\n        padding: shapeData.padding ?? noPadding,\n        value: emoji\n      } : {\n        font: defaultFont,\n        padding: noPadding,\n        ...shapeData,\n        ...emoji\n      },\n      font = emojiOptions.font,\n      value = emojiOptions.value;\n    const key = `${value}_${font}`;\n    if (this._emojiShapeDict.has(key)) {\n      particle.emojiDataKey = key;\n      return;\n    }\n    const padding = emojiOptions.padding * double,\n      maxSize = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getRangeMax)(particle.size.value),\n      fullSize = maxSize + padding,\n      canvasSize = fullSize * double;\n    let image;\n    if (typeof OffscreenCanvas !== \"undefined\") {\n      const canvas = new OffscreenCanvas(canvasSize, canvasSize),\n        context = canvas.getContext(\"2d\");\n      if (!context) {\n        return;\n      }\n      context.font = `400 ${maxSize * double}px ${font}`;\n      context.textBaseline = \"middle\";\n      context.textAlign = \"center\";\n      context.fillText(value, fullSize, fullSize);\n      image = canvas.transferToImageBitmap();\n    } else {\n      const canvas = document.createElement(\"canvas\");\n      canvas.width = canvasSize;\n      canvas.height = canvasSize;\n      const context = canvas.getContext(\"2d\");\n      if (!context) {\n        return;\n      }\n      context.font = `400 ${maxSize * double}px ${font}`;\n      context.textBaseline = \"middle\";\n      context.textAlign = \"center\";\n      context.fillText(value, fullSize, fullSize);\n      image = canvas;\n    }\n    this._emojiShapeDict.set(key, image);\n    particle.emojiDataKey = key;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/shape-emoji/./dist/browser/EmojiDrawer.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawEmoji: () => (/* binding */ drawEmoji)\n/* harmony export */ });\nfunction drawEmoji(data, image) {\n  const {\n      context,\n      opacity\n    } = data,\n    half = 0.5,\n    previousAlpha = context.globalAlpha;\n  if (!image) {\n    return;\n  }\n  const diameter = image.width,\n    radius = diameter * half;\n  context.globalAlpha = opacity;\n  context.drawImage(image, -radius, -radius, diameter, diameter);\n  context.globalAlpha = previousAlpha;\n}\n\n//# sourceURL=webpack://@tsparticles/shape-emoji/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadEmojiShape: () => (/* binding */ loadEmojiShape)\n/* harmony export */ });\n/* harmony import */ var _EmojiDrawer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./EmojiDrawer.js */ \"./dist/browser/EmojiDrawer.js\");\n\nasync function loadEmojiShape(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addShape(new _EmojiDrawer_js__WEBPACK_IMPORTED_MODULE_0__.EmojiDrawer(), refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/shape-emoji/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});