/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/projects/page";
exports.ids = ["app/projects/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2Fpage&page=%2Fprojects%2Fpage&appPaths=%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2Fpage&page=%2Fprojects%2Fpage&appPaths=%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/page.tsx */ \"(rsc)/./src/app/projects/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'projects',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/projects/page\",\n        pathname: \"/projects\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2Fpage&page=%2Fprojects%2Fpage&appPaths=%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-themes/dist/index.mjs */ \"(rsc)/./node_modules/next-themes/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/AIAssistant.tsx */ \"(rsc)/./src/components/ui/AIAssistant.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Navigation.tsx */ \"(rsc)/./src/components/ui/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwb3J0Zm9saW8lNUMlNUNtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC10aGVtZXMlNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcG9ydGZvbGlvJTVDJTVDbW9yZGVybi1wb3J0Zm9saW8tc2F1cmFiaCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwb3J0Zm9saW8lNUMlNUNtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDQUlBc3Npc3RhbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcG9ydGZvbGlvJTVDJTVDbW9yZGVybi1wb3J0Zm9saW8tc2F1cmFiaCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q05hdmlnYXRpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQTRKO0FBQzVKO0FBQ0Esa0xBQWdKO0FBQ2hKO0FBQ0EsZ0xBQStJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiRDpcXFxccG9ydGZvbGlvXFxcXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHQtdGhlbWVzXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxccG9ydGZvbGlvXFxcXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcQUlBc3Npc3RhbnQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxccG9ydGZvbGlvXFxcXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcTmF2aWdhdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cprojects%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cprojects%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/page.tsx */ \"(rsc)/./src/app/projects/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwb3J0Zm9saW8lNUMlNUNtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcHJvamVjdHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxwb3J0Zm9saW9cXFxcbW9yZGVybi1wb3J0Zm9saW8tc2F1cmFiaFxcXFxzcmNcXFxcYXBwXFxcXHByb2plY3RzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cprojects%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwb3J0Zm9saW9cXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2bd6f81f6135\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccG9ydGZvbGlvXFxtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyYmQ2ZjgxZjYxMzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(rsc)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _components_ui_Navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Navigation */ \"(rsc)/./src/components/ui/Navigation.tsx\");\n/* harmony import */ var _components_ui_AIAssistant__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/AIAssistant */ \"(rsc)/./src/components/ui/AIAssistant.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Saurabh Dahariya - Full Stack Developer\",\n    description: \"AI-powered portfolio of Saurabh Dahariya, a passionate Full Stack Developer from Bengaluru specializing in React, Node.js, and modern web technologies.\",\n    keywords: \"Saurabh Dahariya, Full Stack Developer, React, Node.js, Portfolio, Web Developer, Bengaluru\",\n    authors: [\n        {\n            name: \"Saurabh Dahariya\"\n        }\n    ],\n    openGraph: {\n        title: \"Saurabh Dahariya - Full Stack Developer\",\n        description: \"AI-powered portfolio showcasing modern web development projects and skills\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased bg-black text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative min-h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-gradient-to-br from-black via-gray-900 to-black -z-20\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent -z-10\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"relative z-10\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AIAssistant__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/projects/page.tsx":
/*!***********************************!*\
  !*** ./src/app/projects/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\portfolio\\mordern-portfolio-saurabh\\src\\app\\projects\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/AIAssistant.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/AIAssistant.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\portfolio\\mordern-portfolio-saurabh\\src\\components\\ui\\AIAssistant.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/Navigation.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/Navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\portfolio\\mordern-portfolio-saurabh\\src\\components\\ui\\Navigation.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-themes/dist/index.mjs */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/AIAssistant.tsx */ \"(ssr)/./src/components/ui/AIAssistant.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Navigation.tsx */ \"(ssr)/./src/components/ui/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwb3J0Zm9saW8lNUMlNUNtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC10aGVtZXMlNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcG9ydGZvbGlvJTVDJTVDbW9yZGVybi1wb3J0Zm9saW8tc2F1cmFiaCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwb3J0Zm9saW8lNUMlNUNtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDQUlBc3Npc3RhbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDcG9ydGZvbGlvJTVDJTVDbW9yZGVybi1wb3J0Zm9saW8tc2F1cmFiaCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN1aSU1QyU1Q05hdmlnYXRpb24udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOExBQTRKO0FBQzVKO0FBQ0Esa0xBQWdKO0FBQ2hKO0FBQ0EsZ0xBQStJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUaGVtZVByb3ZpZGVyXCJdICovIFwiRDpcXFxccG9ydGZvbGlvXFxcXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHQtdGhlbWVzXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxccG9ydGZvbGlvXFxcXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcQUlBc3Npc3RhbnQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxccG9ydGZvbGlvXFxcXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcdWlcXFxcTmF2aWdhdGlvbi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cprojects%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cprojects%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/projects/page.tsx */ \"(ssr)/./src/app/projects/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwb3J0Zm9saW8lNUMlNUNtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcHJvamVjdHMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0tBQTJHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxwb3J0Zm9saW9cXFxcbW9yZGVybi1wb3J0Zm9saW8tc2F1cmFiaFxcXFxzcmNcXFxcYXBwXFxcXHByb2plY3RzXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cprojects%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/projects/page.tsx":
/*!***********************************!*\
  !*** ./src/app/projects/page.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProjectsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(ssr)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(ssr)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Filter_Github_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Filter,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Filter_Github_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Filter,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Filter_Github_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Filter,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var react_parallax_tilt__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-parallax-tilt */ \"(ssr)/./node_modules/react-parallax-tilt/dist/modern/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nfunction ProjectsPage() {\n    const [selectedFilter, setSelectedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [filteredProjects, setFilteredProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const projects = [\n        {\n            id: 1,\n            title: 'Route Tracker',\n            description: 'Real-time vehicle tracking and movement visualization system',\n            longDescription: 'Developed a comprehensive web-based application focused on visualizing and managing vehicle movement, leveraging the power of React and Redux for efficient state management and seamless user experience. Implemented real-time data visualization to track and display vehicle movement, providing users with instant access to critical information.',\n            technologies: [\n                'React.js',\n                'Redux',\n                'JavaScript',\n                'CSS3',\n                'Real-time APIs'\n            ],\n            liveUrl: 'https://saurabhd.vercel.app/map',\n            githubUrl: '#',\n            image: '/api/placeholder/600/400',\n            category: 'web-app',\n            featured: true\n        },\n        {\n            id: 2,\n            title: 'Camping Grounds',\n            description: 'Full-stack camping site management platform with secure authentication',\n            longDescription: 'Developed \"CampingGrounds\" website with MongoDB/Mongoose for data, Express.js for backend, and EJS for dynamic views. Ensured secure user authentication for campground creation and author-exclusive deletion rights. Utilized Bootstrap for a visually appealing, responsive interface. Demonstrated proficiency in full-stack web development, prioritizing security and usability.',\n            technologies: [\n                'MongoDB',\n                'Express.js',\n                'EJS',\n                'Node.js',\n                'Bootstrap',\n                'Authentication'\n            ],\n            liveUrl: 'https://campinggrounds.onrender.com/',\n            githubUrl: '#',\n            image: '/api/placeholder/600/400',\n            category: 'full-stack',\n            featured: true\n        }\n    ];\n    const categories = [\n        {\n            id: 'all',\n            name: 'All Projects'\n        },\n        {\n            id: 'web-app',\n            name: 'Web Apps'\n        },\n        {\n            id: 'full-stack',\n            name: 'Full Stack'\n        },\n        {\n            id: 'frontend',\n            name: 'Frontend'\n        },\n        {\n            id: 'backend',\n            name: 'Backend'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            if (selectedFilter === 'all') {\n                setFilteredProjects(projects);\n            } else {\n                setFilteredProjects(projects.filter({\n                    \"ProjectsPage.useEffect\": (project)=>project.category === selectedFilter\n                }[\"ProjectsPage.useEffect\"]));\n            }\n        }\n    }[\"ProjectsPage.useEffect\"], [\n        selectedFilter\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsPage.useEffect\": ()=>{\n            const ctx = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.context({\n                \"ProjectsPage.useEffect.ctx\": ()=>{\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo('.project-card', {\n                        opacity: 0,\n                        y: 50,\n                        scale: 0.9\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1,\n                        duration: 0.8,\n                        stagger: 0.2,\n                        ease: 'power2.out',\n                        scrollTrigger: {\n                            trigger: '.projects-grid',\n                            start: 'top 80%',\n                            toggleActions: 'play none none reverse'\n                        }\n                    });\n                }\n            }[\"ProjectsPage.useEffect.ctx\"], sectionRef);\n            return ({\n                \"ProjectsPage.useEffect\": ()=>ctx.revert()\n            })[\"ProjectsPage.useEffect\"];\n        }\n    }[\"ProjectsPage.useEffect\"], [\n        filteredProjects\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: sectionRef,\n        className: \"min-h-screen px-6 lg:px-8\",\n        style: {\n            paddingTop: '120px',\n            paddingBottom: '80px'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-600 bg-clip-text text-transparent font-orbitron mb-8\",\n                            children: \"My Projects\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"A showcase of my work, featuring web applications and full-stack solutions built with modern technologies\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"flex flex-wrap justify-center gap-4 mb-12\",\n                    initial: {\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setSelectedFilter(category.id),\n                            className: `glass px-6 py-3 rounded-full font-medium transition-all duration-300 ${selectedFilter === category.id ? 'text-neon-blue glow-box' : 'text-gray-300 hover:text-neon-green'}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Filter_Github_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 16,\n                                    className: \"inline mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, this),\n                                category.name\n                            ]\n                        }, category.id, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"projects-grid grid md:grid-cols-2 gap-8\",\n                    children: filteredProjects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_parallax_tilt__WEBPACK_IMPORTED_MODULE_6__.Tilt, {\n                            tiltMaxAngleX: 10,\n                            tiltMaxAngleY: 10,\n                            perspective: 1000,\n                            scale: 1.02,\n                            transitionSpeed: 1000,\n                            className: \"project-card\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"glass-strong rounded-2xl overflow-hidden hover:glow-box transition-all duration-500 h-full\",\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: index * 0.1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-64 bg-gradient-to-br from-neon-blue/20 to-neon-purple/20 flex items-center justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-6xl font-bold text-white/20 font-orbitron\",\n                                                children: project.title.split(' ').map((word)=>word[0]).join('')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this),\n                                            project.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-4 right-4 bg-neon-green text-black px-3 py-1 rounded-full text-sm font-semibold\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-white mb-3 font-space\",\n                                                children: project.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-300 mb-4 leading-relaxed\",\n                                                children: project.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-400 text-sm mb-6 leading-relaxed\",\n                                                children: project.longDescription\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2 mb-6\",\n                                                children: project.technologies.map((tech, techIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"glass px-3 py-1 rounded-full text-xs text-neon-blue font-medium\",\n                                                        children: tech\n                                                    }, techIndex, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                                        href: project.liveUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"flex-1 glass px-4 py-3 rounded-lg text-center text-neon-green hover:text-white hover:glow-box transition-all duration-300 font-medium\",\n                                                        whileHover: {\n                                                            scale: 1.02\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.98\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Filter_Github_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                size: 16,\n                                                                className: \"inline mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                                lineNumber: 205,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Live Demo\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    project.githubUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                                        href: project.githubUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"glass px-4 py-3 rounded-lg text-neon-blue hover:text-white hover:glow-box transition-all duration-300\",\n                                                        whileHover: {\n                                                            scale: 1.02\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.98\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Filter_Github_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        }, project.id, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"text-center mt-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-strong p-8 rounded-2xl max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-neon-blue mb-4 font-space\",\n                                children: \"Want to see more?\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-300 mb-6\",\n                                children: \"I'm constantly working on new projects and learning new technologies. Check out my GitHub for more repositories and contributions.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                href: \"https://github.com/saurabhdahariya\",\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"inline-flex items-center glass px-8 py-4 rounded-full text-neon-green hover:text-white hover:glow-box transition-all duration-300 font-semibold\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Filter_Github_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 20,\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View GitHub Profile\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\projects\\\\page.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/projects/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/AIAssistant.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/AIAssistant.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// Using inline SVG icons to avoid chunk loading issues\n\nfunction AIAssistant() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const addMessage = (text, isUser)=>{\n        const newMessage = {\n            id: Date.now().toString(),\n            text,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    const handleSendMessage = async ()=>{\n        if (!inputText.trim()) return;\n        const userMessage = inputText.trim();\n        setInputText('');\n        addMessage(userMessage, true);\n        setIsLoading(true);\n        try {\n            // Simulate AI response and navigation logic\n            const response = await processAIQuery(userMessage);\n            addMessage(response.text, false);\n            if (response.navigate) {\n                setTimeout(()=>{\n                    router.push(response.navigate);\n                }, 1000);\n            }\n        } catch (error) {\n            addMessage('Sorry, I encountered an error. Please try again.', false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const processAIQuery = async (query)=>{\n        try {\n            const response = await fetch('/api/ai-chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: query\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get AI response');\n            }\n            const data = await response.json();\n            return {\n                text: data.response,\n                navigate: data.navigate\n            };\n        } catch (error) {\n            console.error('AI query error:', error);\n            return {\n                text: \"I'm sorry, I'm having trouble processing your request right now. Please try again or feel free to explore the site manually!\"\n            };\n        }\n    };\n    const toggleListening = ()=>{\n        setIsListening(!isListening);\n    // Voice recognition would be implemented here\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIAssistant.useEffect\": ()=>{\n            if (isOpen && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"AIAssistant.useEffect\"], [\n        isOpen\n    ]);\n    // Wave animation component\n    const WaveAnimation = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-1\",\n            children: [\n                ...Array(4)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"w-1 bg-gradient-to-t from-blue-400 to-purple-400 rounded-full\",\n                    animate: {\n                        height: isListening ? [\n                            4,\n                            16,\n                            4\n                        ] : [\n                            4,\n                            8,\n                            4\n                        ]\n                    },\n                    transition: {\n                        duration: 0.8,\n                        repeat: Infinity,\n                        delay: i * 0.1,\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n            lineNumber: 99,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: \"fixed bottom-8 left-1/2 transform -translate-x-1/2 z-[9999]\",\n            initial: {\n                y: 100,\n                opacity: 0\n            },\n            animate: {\n                y: 0,\n                opacity: 1\n            },\n            transition: {\n                delay: 10,\n                duration: 1,\n                ease: \"easeOut\"\n            },\n            children: !isOpen ? // Collapsed state - Modern floating assistant\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"group cursor-pointer\",\n                onClick: ()=>setIsOpen(true),\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-full p-4 shadow-2xl backdrop-blur-lg border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"24\",\n                                                height: \"24\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                className: \"text-white\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                className: \"absolute -top-1 -right-1\",\n                                                animate: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 3,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"12\",\n                                                    height: \"12\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    className: \"text-yellow-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                        points: \"12,2 15.09,8.26 22,9 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9 8.91,8.26\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, this),\n                                    isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WaveAnimation, {}, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 35\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            toggleListening();\n                                        },\n                                        className: `p-2 rounded-full transition-all duration-300 ${isListening ? 'bg-red-500 shadow-lg shadow-red-500/50' : 'bg-white/20 hover:bg-white/30'}`,\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            className: \"text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"1\",\n                                                    y1: \"1\",\n                                                    x2: \"23\",\n                                                    y2: \"23\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"m9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"12\",\n                                                    y1: \"19\",\n                                                    x2: \"12\",\n                                                    y2: \"23\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"8\",\n                                                    y1: \"23\",\n                                                    x2: \"16\",\n                                                    y2: \"23\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 23\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"16\",\n                                            height: \"16\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            className: \"text-white\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M19 10v2a7 7 0 0 1-14 0v-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"12\",\n                                                    y1: \"19\",\n                                                    x2: \"12\",\n                                                    y2: \"23\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 25\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                    x1: \"8\",\n                                                    y1: \"23\",\n                                                    x2: \"16\",\n                                                    y2: \"23\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 25\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-sm px-3 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            initial: {\n                                y: 10\n                            },\n                            whileHover: {\n                                y: 0\n                            },\n                            children: [\n                                \"Ask me anything!\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                lineNumber: 129,\n                columnNumber: 11\n            }, this) : // Expanded state - Modern chat interface\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"bg-black/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/10 w-96 max-w-[90vw] overflow-hidden\",\n                initial: {\n                    scale: 0.8,\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.4,\n                    ease: \"easeOut\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-600/20 to-purple-600/20 p-4 border-b border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        className: \"text-white\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-black\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"AI Assistant\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"Online • Ask about Saurabh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsOpen(false),\n                                    className: \"text-gray-400 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        width: \"18\",\n                                        height: \"18\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"m18 6-12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"m6 6 12 12\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-80 overflow-y-auto p-4 space-y-4\",\n                        children: [\n                            messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        animate: {\n                                            scale: [\n                                                1,\n                                                1.05,\n                                                1\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            width: \"24\",\n                                            height: \"24\",\n                                            viewBox: \"0 0 24 24\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"2\",\n                                            className: \"text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                d: \"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Hi! I'm Saurabh's AI assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm mt-1\",\n                                        children: \"Ask me about his projects, skills, or experience!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 17\n                            }, this),\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: `flex ${message.isUser ? 'justify-end' : 'justify-start'}`,\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `max-w-[80%] p-3 rounded-2xl text-sm ${message.isUser ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg' : 'bg-white/10 text-white border border-white/20 backdrop-blur-sm'}`,\n                                        children: message.text\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 17\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"flex justify-start\",\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 border border-white/20 text-white p-4 rounded-2xl backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    ...Array(3)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                        className: \"w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full\",\n                                                        animate: {\n                                                            scale: [\n                                                                1,\n                                                                1.5,\n                                                                1\n                                                            ],\n                                                            opacity: [\n                                                                0.5,\n                                                                1,\n                                                                0.5\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 1,\n                                                            repeat: Infinity,\n                                                            delay: i * 0.2\n                                                        }\n                                                    }, i, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"AI is thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-white/5 backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            onClick: toggleListening,\n                                            className: `p-3 rounded-full transition-all duration-300 ${isListening ? 'bg-red-500 shadow-lg shadow-red-500/50' : 'bg-white/10 hover:bg-white/20 border border-white/20'}`,\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            children: isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"18\",\n                                                height: \"18\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                className: \"text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: \"1\",\n                                                        y1: \"1\",\n                                                        x2: \"23\",\n                                                        y2: \"23\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"m9 9v3a3 3 0 0 0 5.12 2.12M15 9.34V4a3 3 0 0 0-5.94-.6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M17 16.95A7 7 0 0 1 5 12v-2m14 0v2a7 7 0 0 1-.11 1.23\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: \"12\",\n                                                        y1: \"19\",\n                                                        x2: \"12\",\n                                                        y2: \"23\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: \"8\",\n                                                        y1: \"23\",\n                                                        x2: \"16\",\n                                                        y2: \"23\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"18\",\n                                                height: \"18\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                className: \"text-white\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M19 10v2a7 7 0 0 1-14 0v-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: \"12\",\n                                                        y1: \"19\",\n                                                        x2: \"12\",\n                                                        y2: \"23\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                                        x1: \"8\",\n                                                        y1: \"23\",\n                                                        x2: \"16\",\n                                                        y2: \"23\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, this),\n                                        isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            className: \"absolute inset-0 rounded-full border-2 border-red-400\",\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.5,\n                                                    1\n                                                ],\n                                                opacity: [\n                                                    1,\n                                                    0,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 1.5,\n                                                repeat: Infinity\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: inputRef,\n                                            type: \"text\",\n                                            value: inputText,\n                                            onChange: (e)=>setInputText(e.target.value),\n                                            onKeyPress: (e)=>e.key === 'Enter' && handleSendMessage(),\n                                            placeholder: \"Type your message...\",\n                                            className: \"w-full bg-white/10 border border-white/20 text-white rounded-full px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400 backdrop-blur-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            onClick: handleSendMessage,\n                                            disabled: !inputText.trim() || isLoading,\n                                            className: \"absolute right-2 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                width: \"16\",\n                                                height: \"16\",\n                                                viewBox: \"0 0 24 24\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"m22 2-7 20-4-9-9-4Z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M22 2 11 13\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                lineNumber: 205,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/AIAssistant.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Navigation.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/Navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst navItems = [\n    {\n        href: '/',\n        label: 'Home'\n    },\n    {\n        href: '/projects',\n        label: 'Projects'\n    },\n    {\n        href: '/contact',\n        label: 'Contact'\n    }\n];\nfunction Navigation() {\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 50);\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.nav, {\n        className: `fixed top-0 left-0 right-0 z-40 transition-all duration-500 ${scrolled ? 'bg-black/80 backdrop-blur-xl border-b border-white/10' : 'bg-transparent'}`,\n        initial: {\n            y: -100,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        transition: {\n            delay: 9,\n            duration: 1,\n            ease: \"easeOut\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            layoutId: \"main-name\",\n                            className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-600 bg-clip-text text-transparent font-orbitron cursor-pointer\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                children: \"Saurabh Dahariya\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-12\",\n                            children: navItems.map((item, index)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: \"relative group\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 9.5 + index * 0.1,\n                                            duration: 0.5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-lg font-medium transition-all duration-300 ${isActive ? 'text-white' : 'text-gray-300 hover:text-white'}`,\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute -bottom-2 left-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full\",\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: {\n                                                    width: isActive ? '100%' : 0\n                                                },\n                                                whileHover: {\n                                                    width: '100%'\n                                                },\n                                                transition: {\n                                                    duration: 0.3,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-lg -z-10\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                whileHover: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.href, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"md:hidden p-2 text-gray-300 hover:text-white transition-colors\",\n                            onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M4 6h16M4 12h16M4 18h16\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: `md:hidden overflow-hidden ${mobileMenuOpen ? 'max-h-64' : 'max-h-0'}`,\n                    initial: false,\n                    animate: {\n                        maxHeight: mobileMenuOpen ? 256 : 0,\n                        opacity: mobileMenuOpen ? 1 : 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeInOut\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-4 space-y-2 bg-black/50 backdrop-blur-lg rounded-2xl mt-4 border border-white/10\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: `block px-6 py-3 mx-2 rounded-xl font-medium transition-all duration-300 ${isActive ? 'text-white bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30' : 'text-gray-300 hover:text-white hover:bg-white/10'}`,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    whileHover: {\n                                        x: 5\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: item.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Navigation.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/gsap","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/react-parallax-tilt"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fprojects%2Fpage&page=%2Fprojects%2Fpage&appPaths=%2Fprojects%2Fpage&pagePath=private-next-app-dir%2Fprojects%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();