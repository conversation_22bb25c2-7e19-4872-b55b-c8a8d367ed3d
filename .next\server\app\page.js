/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCustomCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCustomCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-themes/dist/index.mjs */ \"(rsc)/./node_modules/next-themes/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/AIAssistant.tsx */ \"(rsc)/./src/components/ui/AIAssistant.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/CustomCursor.tsx */ \"(rsc)/./src/components/ui/CustomCursor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Navigation.tsx */ \"(rsc)/./src/components/ui/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCustomCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwb3J0Zm9saW8lNUMlNUNtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFpRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccG9ydGZvbGlvXFxcXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwb3J0Zm9saW9cXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"102d1e415482\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccG9ydGZvbGlvXFxtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMDJkMWU0MTU0ODJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(rsc)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_ui_Navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Navigation */ \"(rsc)/./src/components/ui/Navigation.tsx\");\n/* harmony import */ var _components_ui_CustomCursor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/CustomCursor */ \"(rsc)/./src/components/ui/CustomCursor.tsx\");\n/* harmony import */ var _components_ui_AIAssistant__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/AIAssistant */ \"(rsc)/./src/components/ui/AIAssistant.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Saurabh Dahariya - Full Stack Developer\",\n    description: \"AI-powered portfolio of Saurabh Dahariya, a passionate Full Stack Developer from Bengaluru specializing in React, Node.js, and modern web technologies.\",\n    keywords: \"Saurabh Dahariya, Full Stack Developer, React, Node.js, Portfolio, Web Developer, Bengaluru\",\n    authors: [\n        {\n            name: \"Saurabh Dahariya\"\n        }\n    ],\n    openGraph: {\n        title: \"Saurabh Dahariya - Full Stack Developer\",\n        description: \"AI-powered portfolio showcasing modern web development projects and skills\",\n        type: \"website\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"antialiased bg-black text-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"dark\",\n                enableSystem: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative min-h-screen\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-gradient-to-br from-black via-gray-900 to-black -z-20\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent -z-10\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomCursor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Navigation__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: \"relative z-10\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AIAssistant__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\portfolio\\mordern-portfolio-saurabh\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/AIAssistant.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/AIAssistant.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\portfolio\\mordern-portfolio-saurabh\\src\\components\\ui\\AIAssistant.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/CustomCursor.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/CustomCursor.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\CustomCursor.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\portfolio\\mordern-portfolio-saurabh\\src\\components\\ui\\CustomCursor.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/Navigation.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/Navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\portfolio\\mordern-portfolio-saurabh\\src\\components\\ui\\Navigation.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCustomCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCustomCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-themes/dist/index.mjs */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/AIAssistant.tsx */ \"(ssr)/./src/components/ui/AIAssistant.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/CustomCursor.tsx */ \"(ssr)/./src/components/ui/CustomCursor.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/Navigation.tsx */ \"(ssr)/./src/components/ui/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Cnode_modules%5C%5Cnext-themes%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CAIAssistant.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CCustomCursor.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwb3J0Zm9saW8lNUMlNUNtb3JkZXJuLXBvcnRmb2xpby1zYXVyYWJoJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUFpRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccG9ydGZvbGlvXFxcXG1vcmRlcm4tcG9ydGZvbGlvLXNhdXJhYmhcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cportfolio%5C%5Cmordern-portfolio-saurabh%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_SplashScreen__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/SplashScreen */ \"(ssr)/./src/components/ui/SplashScreen.tsx\");\n/* harmony import */ var _components_sections_HeroSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/HeroSection */ \"(ssr)/./src/components/sections/HeroSection.tsx\");\n/* harmony import */ var _components_sections_AboutSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/AboutSection */ \"(ssr)/./src/components/sections/AboutSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Home() {\n    const [showSplash, setShowSplash] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const handleSplashComplete = ()=>{\n        setShowSplash(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            showSplash && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SplashScreen__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onComplete: handleSplashComplete\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 17,\n                columnNumber: 22\n            }, this),\n            !showSplash && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_HeroSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_AboutSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUVpQztBQUN1QjtBQUNJO0FBQ0U7QUFFL0MsU0FBU0k7SUFDdEIsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdOLCtDQUFRQSxDQUFDO0lBRTdDLE1BQU1PLHVCQUF1QjtRQUMzQkQsY0FBYztJQUNoQjtJQUVBLHFCQUNFOztZQUNHRCw0QkFBYyw4REFBQ0osbUVBQVlBO2dCQUFDTyxZQUFZRDs7Ozs7O1lBRXhDLENBQUNGLDRCQUNBLDhEQUFDSTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNSLHdFQUFXQTs7Ozs7a0NBQ1osOERBQUNDLHlFQUFZQTs7Ozs7Ozs7Ozs7OztBQUt2QiIsInNvdXJjZXMiOlsiRDpcXHBvcnRmb2xpb1xcbW9yZGVybi1wb3J0Zm9saW8tc2F1cmFiaFxcc3JjXFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgU3BsYXNoU2NyZWVuIGZyb20gJ0AvY29tcG9uZW50cy91aS9TcGxhc2hTY3JlZW4nO1xuaW1wb3J0IEhlcm9TZWN0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9zZWN0aW9ucy9IZXJvU2VjdGlvbic7XG5pbXBvcnQgQWJvdXRTZWN0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9zZWN0aW9ucy9BYm91dFNlY3Rpb24nO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICBjb25zdCBbc2hvd1NwbGFzaCwgc2V0U2hvd1NwbGFzaF0gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICBjb25zdCBoYW5kbGVTcGxhc2hDb21wbGV0ZSA9ICgpID0+IHtcbiAgICBzZXRTaG93U3BsYXNoKGZhbHNlKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICB7c2hvd1NwbGFzaCAmJiA8U3BsYXNoU2NyZWVuIG9uQ29tcGxldGU9e2hhbmRsZVNwbGFzaENvbXBsZXRlfSAvPn1cblxuICAgICAgeyFzaG93U3BsYXNoICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW5cIj5cbiAgICAgICAgICA8SGVyb1NlY3Rpb24gLz5cbiAgICAgICAgICA8QWJvdXRTZWN0aW9uIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8Lz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIlNwbGFzaFNjcmVlbiIsIkhlcm9TZWN0aW9uIiwiQWJvdXRTZWN0aW9uIiwiSG9tZSIsInNob3dTcGxhc2giLCJzZXRTaG93U3BsYXNoIiwiaGFuZGxlU3BsYXNoQ29tcGxldGUiLCJvbkNvbXBsZXRlIiwiZGl2IiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(ssr)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(ssr)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nfunction AboutSection() {\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const ctx = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.context({\n                \"AboutSection.useEffect.ctx\": ()=>{\n                    // Animate cards on scroll\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo('.about-card', {\n                        opacity: 0,\n                        y: 50,\n                        scale: 0.9\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1,\n                        duration: 0.8,\n                        stagger: 0.2,\n                        ease: 'power2.out',\n                        scrollTrigger: {\n                            trigger: '.about-cards',\n                            start: 'top 80%',\n                            end: 'bottom 20%',\n                            toggleActions: 'play none none reverse'\n                        }\n                    });\n                    // Animate text elements\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo('.about-text', {\n                        opacity: 0,\n                        x: -50\n                    }, {\n                        opacity: 1,\n                        x: 0,\n                        duration: 1,\n                        ease: 'power2.out',\n                        scrollTrigger: {\n                            trigger: '.about-content',\n                            start: 'top 80%',\n                            toggleActions: 'play none none reverse'\n                        }\n                    });\n                }\n            }[\"AboutSection.useEffect.ctx\"], sectionRef);\n            return ({\n                \"AboutSection.useEffect\": ()=>ctx.revert()\n            })[\"AboutSection.useEffect\"];\n        }\n    }[\"AboutSection.useEffect\"], []);\n    const personalInfo = [\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: 'Location',\n            value: 'Bengaluru, India'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'Phone',\n            value: '+91 8319130513'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'Email',\n            value: '<EMAIL>'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Education',\n            value: 'B.Tech (IT) - Bhilai Institute Of Technology'\n        }\n    ];\n    const highlights = [\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: 'Full Stack Development',\n            description: 'Expertise in React.js, Node.js, Express.js, and MongoDB for building complete web applications.'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: 'Passionate Learner',\n            description: 'Always eager to learn new technologies and stay updated with the latest industry trends.'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: 'Fresh Graduate',\n            description: 'Recent B.Tech IT graduate with hands-on experience through projects and training.'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        ref: sectionRef,\n        className: \"min-h-screen py-20 px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6\",\n                            children: \"About Me\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                            children: \"Get to know more about my background, skills, and passion for technology\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center about-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"about-text\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-3xl font-bold text-neon-blue mb-6 font-space\",\n                                    children: \"Hello! I'm Saurabh Dahariya\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 mb-8 leading-relaxed\",\n                                    children: \"I'm a passionate Full Stack Developer with a strong foundation in modern web technologies. As a recent B.Tech IT graduate from Bhilai Institute Of Technology, I bring fresh perspectives and enthusiasm to every project I work on.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 mb-8 leading-relaxed\",\n                                    children: \"My journey in web development started during my college years, and I've been continuously learning and building projects to enhance my skills. I'm particularly passionate about creating responsive, user-friendly applications that solve real-world problems.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid sm:grid-cols-2 gap-4\",\n                                    children: personalInfo.map((info, index)=>{\n                                        const Icon = info.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            className: \"glass p-4 rounded-lg hover:glow-box transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"text-neon-blue\",\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: info.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: info.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"about-cards\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-neon-green mb-8 font-space\",\n                                    children: \"What Makes Me Unique\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: highlights.map((highlight, index)=>{\n                                        const Icon = highlight.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            className: \"about-card glass-strong p-6 rounded-xl hover:glow-box transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.02,\n                                                y: -5\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-gradient-to-r from-neon-blue to-neon-green rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"text-black\",\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                                children: highlight.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 leading-relaxed\",\n                                                                children: highlight.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"mt-16 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-strong p-8 rounded-2xl max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-neon-purple mb-4 font-space\",\n                                children: \"Current Training\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-300 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"MERN Stack Development\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" at JSpider BTM Layout, Bengaluru\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"September 2024 - February 2025\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mt-4\",\n                                children: \"Intensive training program focusing on MongoDB, Express.js, React.js, and Node.js to build full-stack web applications.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/AboutSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/HeroSection.tsx":
/*!*************************************************!*\
  !*** ./src/components/sections/HeroSection.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var typewriter_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! typewriter-effect */ \"(ssr)/./node_modules/typewriter-effect/dist/react.js\");\n/* harmony import */ var typewriter_effect__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(typewriter_effect__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_TechLogos__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/TechLogos */ \"(ssr)/./src/components/ui/TechLogos.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction HeroSection() {\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToNext = ()=>{\n        const nextSection = document.getElementById('about');\n        if (nextSection) {\n            nextSection.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: heroRef,\n        className: \"min-h-screen flex items-center justify-center relative px-6 lg:px-8\",\n        style: {\n            paddingTop: '120px'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-6xl mx-auto text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.5,\n                            duration: 1\n                        },\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                className: \"text-xl md:text-2xl text-gray-400 font-light\",\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 1,\n                                    duration: 0.8\n                                },\n                                children: \"Hello, I'm\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h1, {\n                                className: \"text-5xl md:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-600 bg-clip-text text-transparent font-orbitron leading-tight\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 1.2,\n                                    duration: 1\n                                },\n                                children: \"Saurabh Dahariya\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"text-2xl md:text-4xl lg:text-5xl text-white font-light h-20 flex items-center justify-center\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 1.5,\n                                    duration: 0.8\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((typewriter_effect__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    options: {\n                                        strings: [\n                                            'Full Stack Software Developer',\n                                            'React.js Specialist',\n                                            'MERN Stack Developer',\n                                            'Problem Solver'\n                                        ],\n                                        autoStart: true,\n                                        loop: true,\n                                        delay: 100,\n                                        deleteSpeed: 50\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                className: \"text-lg md:text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 1.8,\n                                    duration: 0.8\n                                },\n                                children: \"Passionate B.Tech IT graduate from Bengaluru with expertise in modern web technologies. I build responsive, scalable applications and love turning ideas into digital reality.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"flex flex-col sm:flex-row gap-6 justify-center items-center pt-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 2.1,\n                                    duration: 0.8\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        className: \"group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            y: -2\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        onClick: ()=>document.getElementById('projects')?.scrollIntoView({\n                                                behavior: 'smooth'\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"View My Work\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-blue-700 to-purple-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                        className: \"px-8 py-4 border-2 border-white/20 text-white rounded-full font-semibold hover:border-blue-400 hover:text-blue-400 transition-all duration-300 backdrop-blur-sm\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            y: -2\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        onClick: ()=>document.getElementById('contact')?.scrollIntoView({\n                                                behavior: 'smooth'\n                                            }),\n                                        children: \"Get In Touch\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"flex justify-center space-x-6 pt-8\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 2.4,\n                                    duration: 0.8\n                                },\n                                children: [\n                                    {\n                                        icon: _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                        href: 'https://github.com/saurabhdahariya',\n                                        label: 'GitHub'\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                        href: '#',\n                                        label: 'LinkedIn'\n                                    },\n                                    {\n                                        icon: _barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                        href: 'mailto:<EMAIL>',\n                                        label: 'Email'\n                                    }\n                                ].map((social, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.a, {\n                                        href: social.href,\n                                        target: social.href.startsWith('http') ? '_blank' : '_self',\n                                        rel: social.href.startsWith('http') ? 'noopener noreferrer' : '',\n                                        className: \"p-3 rounded-full bg-white/10 text-gray-400 hover:text-white hover:bg-white/20 transition-all duration-300 backdrop-blur-sm\",\n                                        whileHover: {\n                                            scale: 1.1,\n                                            y: -2\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(social.icon, {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, social.label, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"pt-16\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 2.7,\n                            duration: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 text-lg mb-8 font-light\",\n                                children: \"Technologies I work with\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TechLogos__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                speed: 50\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute bottom-12 left-1/2 transform -translate-x-1/2 cursor-pointer\",\n                        onClick: scrollToNext,\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 3,\n                            duration: 0.8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"flex flex-col items-center text-gray-400 hover:text-white transition-colors group\",\n                            animate: {\n                                y: [\n                                    0,\n                                    10,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: 'easeInOut'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm mb-3 font-light\",\n                                    children: \"Scroll to explore\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"p-3 rounded-full bg-white/10 backdrop-blur-sm group-hover:bg-white/20 transition-all duration-300\",\n                                    whileHover: {\n                                        scale: 1.1\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute top-1/4 left-1/4 w-4 h-4 bg-neon-blue rounded-full opacity-60\",\n                        animate: {\n                            y: [\n                                0,\n                                -20,\n                                0\n                            ],\n                            opacity: [\n                                0.6,\n                                1,\n                                0.6\n                            ]\n                        },\n                        transition: {\n                            duration: 4,\n                            repeat: Infinity,\n                            ease: 'easeInOut'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute top-1/3 right-1/4 w-6 h-6 bg-neon-green rounded-full opacity-40\",\n                        animate: {\n                            y: [\n                                0,\n                                30,\n                                0\n                            ],\n                            opacity: [\n                                0.4,\n                                0.8,\n                                0.4\n                            ]\n                        },\n                        transition: {\n                            duration: 5,\n                            repeat: Infinity,\n                            ease: 'easeInOut',\n                            delay: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute bottom-1/3 left-1/3 w-3 h-3 bg-neon-purple rounded-full opacity-50\",\n                        animate: {\n                            y: [\n                                0,\n                                -15,\n                                0\n                            ],\n                            opacity: [\n                                0.5,\n                                1,\n                                0.5\n                            ]\n                        },\n                        transition: {\n                            duration: 3,\n                            repeat: Infinity,\n                            ease: 'easeInOut',\n                            delay: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\HeroSection.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/HeroSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/AIAssistant.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/AIAssistant.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIAssistant)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,Send,Sparkles,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,Send,Sparkles,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,Send,Sparkles,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,Send,Sparkles,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,Send,Sparkles,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle,Mic,MicOff,Send,Sparkles,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AIAssistant() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isListening, setIsListening] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const addMessage = (text, isUser)=>{\n        const newMessage = {\n            id: Date.now().toString(),\n            text,\n            isUser,\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    const handleSendMessage = async ()=>{\n        if (!inputText.trim()) return;\n        const userMessage = inputText.trim();\n        setInputText('');\n        addMessage(userMessage, true);\n        setIsLoading(true);\n        try {\n            // Simulate AI response and navigation logic\n            const response = await processAIQuery(userMessage);\n            addMessage(response.text, false);\n            if (response.navigate) {\n                setTimeout(()=>{\n                    router.push(response.navigate);\n                }, 1000);\n            }\n        } catch (error) {\n            addMessage('Sorry, I encountered an error. Please try again.', false);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const processAIQuery = async (query)=>{\n        try {\n            const response = await fetch('/api/ai-chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: query\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get AI response');\n            }\n            const data = await response.json();\n            return {\n                text: data.response,\n                navigate: data.navigate\n            };\n        } catch (error) {\n            console.error('AI query error:', error);\n            return {\n                text: \"I'm sorry, I'm having trouble processing your request right now. Please try again or feel free to explore the site manually!\"\n            };\n        }\n    };\n    const toggleListening = ()=>{\n        setIsListening(!isListening);\n    // Voice recognition would be implemented here\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AIAssistant.useEffect\": ()=>{\n            if (isOpen && inputRef.current) {\n                inputRef.current.focus();\n            }\n        }\n    }[\"AIAssistant.useEffect\"], [\n        isOpen\n    ]);\n    // Wave animation component\n    const WaveAnimation = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-1\",\n            children: [\n                ...Array(4)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"w-1 bg-gradient-to-t from-blue-400 to-purple-400 rounded-full\",\n                    animate: {\n                        height: isListening ? [\n                            4,\n                            16,\n                            4\n                        ] : [\n                            4,\n                            8,\n                            4\n                        ]\n                    },\n                    transition: {\n                        duration: 0.8,\n                        repeat: Infinity,\n                        delay: i * 0.1,\n                        ease: \"easeInOut\"\n                    }\n                }, i, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n            lineNumber: 99,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: \"fixed bottom-8 left-1/2 transform -translate-x-1/2 z-[9999]\",\n            initial: {\n                y: 100,\n                opacity: 0\n            },\n            animate: {\n                y: 0,\n                opacity: 1\n            },\n            transition: {\n                delay: 10,\n                duration: 1,\n                ease: \"easeOut\"\n            },\n            children: !isOpen ? // Collapsed state - Modern floating assistant\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"group cursor-pointer\",\n                onClick: ()=>setIsOpen(true),\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-full p-4 shadow-2xl backdrop-blur-lg border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                size: 24,\n                                                className: \"text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                className: \"absolute -top-1 -right-1\",\n                                                animate: {\n                                                    rotate: 360\n                                                },\n                                                transition: {\n                                                    duration: 3,\n                                                    repeat: Infinity,\n                                                    ease: \"linear\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    size: 12,\n                                                    className: \"text-yellow-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 19\n                                    }, this),\n                                    isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WaveAnimation, {}, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 35\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            toggleListening();\n                                        },\n                                        className: `p-2 rounded-full transition-all duration-300 ${isListening ? 'bg-red-500 shadow-lg shadow-red-500/50' : 'bg-white/20 hover:bg-white/30'}`,\n                                        whileHover: {\n                                            scale: 1.1\n                                        },\n                                        whileTap: {\n                                            scale: 0.9\n                                        },\n                                        children: isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 16,\n                                            className: \"text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 36\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16,\n                                            className: \"text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 82\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-sm px-3 py-1 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                            initial: {\n                                y: 10\n                            },\n                            whileHover: {\n                                y: 0\n                            },\n                            children: [\n                                \"Ask me anything!\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                lineNumber: 129,\n                columnNumber: 11\n            }, this) : // Expanded state - Modern chat interface\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"bg-black/90 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/10 w-96 max-w-[90vw] overflow-hidden\",\n                initial: {\n                    scale: 0.8,\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    scale: 1,\n                    opacity: 1,\n                    y: 0\n                },\n                transition: {\n                    duration: 0.4,\n                    ease: \"easeOut\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-600/20 to-purple-600/20 p-4 border-b border-white/10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        size: 20,\n                                                        className: \"text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-black\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-white\",\n                                                    children: \"AI Assistant\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"Online • Ask about Saurabh\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsOpen(false),\n                                    className: \"text-gray-400 hover:text-white transition-colors p-2 hover:bg-white/10 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-80 overflow-y-auto p-4 space-y-4\",\n                        children: [\n                            messages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                        animate: {\n                                            scale: [\n                                                1,\n                                                1.05,\n                                                1\n                                            ]\n                                        },\n                                        transition: {\n                                            duration: 2,\n                                            repeat: Infinity\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            size: 24,\n                                            className: \"text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white font-medium\",\n                                        children: \"Hi! I'm Saurabh's AI assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 text-sm mt-1\",\n                                        children: \"Ask me about his projects, skills, or experience!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 17\n                            }, this),\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: `flex ${message.isUser ? 'justify-end' : 'justify-start'}`,\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20,\n                                        scale: 0.9\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `max-w-[80%] p-3 rounded-2xl text-sm ${message.isUser ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg' : 'bg-white/10 text-white border border-white/20 backdrop-blur-sm'}`,\n                                        children: message.text\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, this)\n                                }, message.id, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 17\n                                }, this)),\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"flex justify-start\",\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 border border-white/20 text-white p-4 rounded-2xl backdrop-blur-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1\",\n                                                children: [\n                                                    ...Array(3)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                        className: \"w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full\",\n                                                        animate: {\n                                                            scale: [\n                                                                1,\n                                                                1.5,\n                                                                1\n                                                            ],\n                                                            opacity: [\n                                                                0.5,\n                                                                1,\n                                                                0.5\n                                                            ]\n                                                        },\n                                                        transition: {\n                                                            duration: 1,\n                                                            repeat: Infinity,\n                                                            delay: i * 0.2\n                                                        }\n                                                    }, i, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-gray-400\",\n                                                children: \"AI is thinking...\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 bg-white/5 backdrop-blur-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            onClick: toggleListening,\n                                            className: `p-3 rounded-full transition-all duration-300 ${isListening ? 'bg-red-500 shadow-lg shadow-red-500/50' : 'bg-white/10 hover:bg-white/20 border border-white/20'}`,\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            children: isListening ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                size: 18,\n                                                className: \"text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 36\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                size: 18,\n                                                className: \"text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 82\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, this),\n                                        isListening && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            className: \"absolute inset-0 rounded-full border-2 border-red-400\",\n                                            animate: {\n                                                scale: [\n                                                    1,\n                                                    1.5,\n                                                    1\n                                                ],\n                                                opacity: [\n                                                    1,\n                                                    0,\n                                                    1\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 1.5,\n                                                repeat: Infinity\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            ref: inputRef,\n                                            type: \"text\",\n                                            value: inputText,\n                                            onChange: (e)=>setInputText(e.target.value),\n                                            onKeyPress: (e)=>e.key === 'Enter' && handleSendMessage(),\n                                            placeholder: \"Type your message...\",\n                                            className: \"w-full bg-white/10 border border-white/20 text-white rounded-full px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 placeholder-gray-400 backdrop-blur-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                            onClick: handleSendMessage,\n                                            disabled: !inputText.trim() || isLoading,\n                                            className: \"absolute right-2 top-1/2 transform -translate-y-1/2 p-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            whileHover: {\n                                                scale: 1.1\n                                            },\n                                            whileTap: {\n                                                scale: 0.9\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_Mic_MicOff_Send_Sparkles_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n                lineNumber: 186,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\AIAssistant.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/AIAssistant.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/CustomCursor.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/CustomCursor.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CustomCursor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CustomCursor() {\n    const [mousePosition, setMousePosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [isHovering, setIsHovering] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CustomCursor.useEffect\": ()=>{\n            const updateMousePosition = {\n                \"CustomCursor.useEffect.updateMousePosition\": (e)=>{\n                    setMousePosition({\n                        x: e.clientX,\n                        y: e.clientY\n                    });\n                }\n            }[\"CustomCursor.useEffect.updateMousePosition\"];\n            const handleMouseEnter = {\n                \"CustomCursor.useEffect.handleMouseEnter\": ()=>setIsHovering(true)\n            }[\"CustomCursor.useEffect.handleMouseEnter\"];\n            const handleMouseLeave = {\n                \"CustomCursor.useEffect.handleMouseLeave\": ()=>setIsHovering(false)\n            }[\"CustomCursor.useEffect.handleMouseLeave\"];\n            // Add event listeners for mouse movement\n            window.addEventListener('mousemove', updateMousePosition);\n            // Add hover detection for interactive elements\n            const interactiveElements = document.querySelectorAll('button, a, [role=\"button\"]');\n            interactiveElements.forEach({\n                \"CustomCursor.useEffect\": (el)=>{\n                    el.addEventListener('mouseenter', handleMouseEnter);\n                    el.addEventListener('mouseleave', handleMouseLeave);\n                }\n            }[\"CustomCursor.useEffect\"]);\n            return ({\n                \"CustomCursor.useEffect\": ()=>{\n                    window.removeEventListener('mousemove', updateMousePosition);\n                    interactiveElements.forEach({\n                        \"CustomCursor.useEffect\": (el)=>{\n                            el.removeEventListener('mouseenter', handleMouseEnter);\n                            el.removeEventListener('mouseleave', handleMouseLeave);\n                        }\n                    }[\"CustomCursor.useEffect\"]);\n                }\n            })[\"CustomCursor.useEffect\"];\n        }\n    }[\"CustomCursor.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"custom-cursor\",\n                style: {\n                    left: mousePosition.x - 10,\n                    top: mousePosition.y - 10\n                },\n                animate: {\n                    scale: isHovering ? 1.5 : 1,\n                    backgroundColor: isHovering ? '#00ff88' : '#00d4ff'\n                },\n                transition: {\n                    type: 'spring',\n                    stiffness: 500,\n                    damping: 28\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\CustomCursor.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                className: \"custom-cursor-trail\",\n                style: {\n                    left: mousePosition.x - 4,\n                    top: mousePosition.y - 4\n                },\n                animate: {\n                    scale: isHovering ? 2 : 1,\n                    opacity: isHovering ? 0.8 : 0.6\n                },\n                transition: {\n                    type: 'spring',\n                    stiffness: 300,\n                    damping: 30,\n                    delay: 0.05\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\CustomCursor.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/CustomCursor.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Navigation.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/Navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst navItems = [\n    {\n        href: '/',\n        label: 'Home'\n    },\n    {\n        href: '/projects',\n        label: 'Projects'\n    },\n    {\n        href: '/contact',\n        label: 'Contact'\n    }\n];\nfunction Navigation() {\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 50);\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.nav, {\n        className: `fixed top-0 left-0 right-0 z-40 transition-all duration-500 ${scrolled ? 'bg-black/80 backdrop-blur-xl border-b border-white/10' : 'bg-transparent'}`,\n        initial: {\n            y: -100,\n            opacity: 0\n        },\n        animate: {\n            y: 0,\n            opacity: 1\n        },\n        transition: {\n            delay: 9,\n            duration: 1,\n            ease: \"easeOut\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            layoutId: \"main-name\",\n                            className: \"text-2xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-600 bg-clip-text text-transparent font-orbitron cursor-pointer\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                children: \"Saurabh Dahariya\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-12\",\n                            children: navItems.map((item, index)=>{\n                                const isActive = pathname === item.href;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: \"relative group\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: -20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 9.5 + index * 0.1,\n                                            duration: 0.5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: `text-lg font-medium transition-all duration-300 ${isActive ? 'text-white' : 'text-gray-300 hover:text-white'}`,\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute -bottom-2 left-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full\",\n                                                initial: {\n                                                    width: 0\n                                                },\n                                                animate: {\n                                                    width: isActive ? '100%' : 0\n                                                },\n                                                whileHover: {\n                                                    width: '100%'\n                                                },\n                                                transition: {\n                                                    duration: 0.3,\n                                                    ease: \"easeInOut\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-lg -z-10\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                whileHover: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 19\n                                    }, this)\n                                }, item.href, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                            className: \"md:hidden p-2 text-gray-300 hover:text-white transition-colors\",\n                            onClick: ()=>setMobileMenuOpen(!mobileMenuOpen),\n                            whileHover: {\n                                scale: 1.1\n                            },\n                            whileTap: {\n                                scale: 0.9\n                            },\n                            children: mobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 31\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                size: 24\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: `md:hidden overflow-hidden ${mobileMenuOpen ? 'max-h-64' : 'max-h-0'}`,\n                    initial: false,\n                    animate: {\n                        maxHeight: mobileMenuOpen ? 256 : 0,\n                        opacity: mobileMenuOpen ? 1 : 0\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: \"easeInOut\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-4 space-y-2 bg-black/50 backdrop-blur-lg rounded-2xl mt-4 border border-white/10\",\n                        children: navItems.map((item)=>{\n                            const isActive = pathname === item.href;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: `block px-6 py-3 mx-2 rounded-xl font-medium transition-all duration-300 ${isActive ? 'text-white bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30' : 'text-gray-300 hover:text-white hover:bg-white/10'}`,\n                                    onClick: ()=>setMobileMenuOpen(false),\n                                    whileHover: {\n                                        x: 5\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: item.label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\Navigation.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/SplashScreen.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/SplashScreen.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SplashScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SplashScreen({ onComplete }) {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showIAm, setShowIAm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showName, setShowName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTitle, setShowTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fadeOutText, setFadeOutText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SplashScreen.useEffect\": ()=>{\n            const sequence = {\n                \"SplashScreen.useEffect.sequence\": async ()=>{\n                    // Step 1: \"Hello World\" - 2.5s\n                    await new Promise({\n                        \"SplashScreen.useEffect.sequence\": (resolve)=>setTimeout(resolve, 2500)\n                    }[\"SplashScreen.useEffect.sequence\"]);\n                    setCurrentStep(1);\n                    // Step 2: \"I am\" appears - 1s\n                    await new Promise({\n                        \"SplashScreen.useEffect.sequence\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"SplashScreen.useEffect.sequence\"]);\n                    setShowIAm(true);\n                    // Step 3: \"Saurabh Dahariya\" appears below - 1.5s\n                    await new Promise({\n                        \"SplashScreen.useEffect.sequence\": (resolve)=>setTimeout(resolve, 1500)\n                    }[\"SplashScreen.useEffect.sequence\"]);\n                    setShowName(true);\n                    // Step 4: \"Software Developer\" appears below - 1.5s\n                    await new Promise({\n                        \"SplashScreen.useEffect.sequence\": (resolve)=>setTimeout(resolve, 1500)\n                    }[\"SplashScreen.useEffect.sequence\"]);\n                    setShowTitle(true);\n                    // Step 5: Wait 2s, then fade out \"I am\" and \"Software Developer\" - 2s\n                    await new Promise({\n                        \"SplashScreen.useEffect.sequence\": (resolve)=>setTimeout(resolve, 2000)\n                    }[\"SplashScreen.useEffect.sequence\"]);\n                    setFadeOutText(true);\n                    // Step 6: Wait 1s, then complete (name will float to navbar) - 1s\n                    await new Promise({\n                        \"SplashScreen.useEffect.sequence\": (resolve)=>setTimeout(resolve, 1000)\n                    }[\"SplashScreen.useEffect.sequence\"]);\n                    onComplete();\n                }\n            }[\"SplashScreen.useEffect.sequence\"];\n            sequence();\n        }\n    }[\"SplashScreen.useEffect\"], [\n        onComplete\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n        className: \"fixed inset-0 z-50 flex items-center justify-center\",\n        style: {\n            background: 'rgba(0, 0, 0, 0.95)',\n            backdropFilter: 'blur(20px)'\n        },\n        initial: {\n            opacity: 1\n        },\n        exit: {\n            opacity: 0\n        },\n        transition: {\n            duration: 1,\n            ease: \"easeInOut\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center relative z-10 space-y-6\",\n                children: [\n                    currentStep === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        exit: {\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        className: \"text-4xl md:text-6xl font-light text-white font-space\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.span, {\n                            initial: {\n                                width: 0\n                            },\n                            animate: {\n                                width: \"auto\"\n                            },\n                            transition: {\n                                duration: 1.5,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"inline-block overflow-hidden whitespace-nowrap border-r-2 border-blue-400\",\n                            style: {\n                                borderRight: currentStep === 0 ? '2px solid #60a5fa' : 'none'\n                            },\n                            children: \"Hello World\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    currentStep >= 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: fadeOutText ? 0 : 1,\n                            y: 0,\n                            transition: {\n                                duration: 0.8\n                            }\n                        },\n                        className: \"text-3xl md:text-4xl font-light text-gray-300 font-space\",\n                        children: \"I am\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    showName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        layoutId: \"main-name\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1\n                        },\n                        transition: {\n                            duration: 1,\n                            ease: \"easeOut\"\n                        },\n                        className: \"text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-600 bg-clip-text text-transparent font-orbitron\",\n                        children: \"Saurabh Dahariya\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this),\n                    showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: {\n                            opacity: fadeOutText ? 0 : 1,\n                            y: 0,\n                            transition: {\n                                duration: 0.8\n                            }\n                        },\n                        className: \"text-2xl md:text-3xl font-light text-gray-400 font-space\",\n                        children: \"Software Developer\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\SplashScreen.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/SplashScreen.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/TechLogos.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/TechLogos.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TechLogos)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react_fast_marquee__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-fast-marquee */ \"(ssr)/./node_modules/react-fast-marquee/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst techLogos = [\n    {\n        name: 'React',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"2\",\n                    fill: \"#61DAFB\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4Z\",\n                    fill: \"#61DAFB\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    rx: \"8\",\n                    ry: \"3\",\n                    fill: \"none\",\n                    stroke: \"#61DAFB\",\n                    strokeWidth: \"1\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    rx: \"8\",\n                    ry: \"3\",\n                    fill: \"none\",\n                    stroke: \"#61DAFB\",\n                    strokeWidth: \"1\",\n                    transform: \"rotate(60 12 12)\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ellipse\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    rx: \"8\",\n                    ry: \"3\",\n                    fill: \"none\",\n                    stroke: \"#61DAFB\",\n                    strokeWidth: \"1\",\n                    transform: \"rotate(120 12 12)\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, undefined),\n        color: '#61DAFB'\n    },\n    {\n        name: 'Node.js',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12,1.85C11.73,1.85 11.45,1.92 11.22,2.05L3.78,6.35C3.32,6.61 3.05,7.11 3.05,7.65V16.35C3.05,16.89 3.32,17.39 3.78,17.65L11.22,21.95C11.45,22.08 11.73,22.15 12,22.15C12.27,22.15 12.55,22.08 12.78,21.95L20.22,17.65C20.68,17.39 20.95,16.89 20.95,16.35V7.65C20.95,7.11 20.68,6.61 20.22,6.35L12.78,2.05C12.55,1.92 12.27,1.85 12,1.85M12,3.05L19,7V17L12,21L5,17V7L12,3.05Z\",\n                fill: \"#339933\"\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined),\n        color: '#339933'\n    },\n    {\n        name: 'JavaScript',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"rect\", {\n                    width: \"24\",\n                    height: \"24\",\n                    rx: \"3\",\n                    fill: \"#F7DF1E\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M7.5,16.5V14.5H9V16.5C9,17.33 9.67,18 10.5,18C11.33,18 12,17.33 12,16.5V10H13.5V16.5C13.5,18.16 12.16,19.5 10.5,19.5C8.84,19.5 7.5,18.16 7.5,16.5M15,14.5H16.5V15.5H17.5V14H15V12.5H19V16H17.5V15H16.5V16.5H19V18H15V14.5Z\",\n                    fill: \"#000\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, undefined),\n        color: '#F7DF1E'\n    },\n    {\n        name: 'MongoDB',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12.5,17V19H11.5V17C10.9,16.9 10.4,16.6 10,16.2L11.1,15.1C11.4,15.4 11.7,15.5 12,15.5C12.6,15.5 13,15.1 13,14.5V9.5C13,8.9 12.6,8.5 12,8.5C11.4,8.5 11,8.9 11,9.5V14.5C11,15.9 12.1,17 13.5,17H12.5Z\",\n                fill: \"#47A248\"\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined),\n        color: '#47A248'\n    },\n    {\n        name: 'HTML5',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3.5,2L4.8,20L12,22L19.2,20L20.5,2H3.5M17.5,8H8.5L8.8,11H17.2L16.5,17L12,18.5L7.5,17L7.2,14H10.2L10.4,15.5L12,16L13.6,15.5L13.8,13H7L6.5,8H17.5V8Z\",\n                fill: \"#E34F26\"\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                lineNumber: 52,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, undefined),\n        color: '#E34F26'\n    },\n    {\n        name: 'CSS3',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M3.5,2L4.8,20L12,22L19.2,20L20.5,2H3.5M16.5,8H8.5L8.8,11H16.2L15.5,17L12,18.5L8.5,17L8.2,14H11.2L11.4,15.5L12,16L12.6,15.5L12.8,13H8L7.5,8H16.5V8Z\",\n                fill: \"#1572B6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined),\n        color: '#1572B6'\n    },\n    {\n        name: 'Git',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M21.62,11.13L12.87,2.38C12.5,2 12,2 11.62,2.38L9.87,4.13L12.5,6.75C13,6.5 13.62,6.5 14.12,7C14.62,7.5 14.62,8.12 14.12,8.62C13.62,9.12 13,9.12 12.5,8.62L10,6.12V17.87C10.5,18.12 11,18.62 11,19.25C11,20.12 10.37,20.75 9.5,20.75C8.62,20.75 8,20.12 8,19.25C8,18.62 8.5,18.12 9,17.87V6.12L7.5,4.62L2.38,9.75C2,10.12 2,10.62 2.38,11L11.13,19.75C11.5,20.12 12,20.12 12.38,19.75L21.62,11.5C22,11.12 22,10.62 21.62,11.13Z\",\n                fill: \"#F05032\"\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, undefined),\n        color: '#F05032'\n    },\n    {\n        name: 'Express.js',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M24,18.588A1.529,1.529,0,0,1,22.471,20.117H1.529A1.529,1.529,0,0,1,0,18.588V5.412A1.529,1.529,0,0,1,1.529,3.883H22.471A1.529,1.529,0,0,1,24,5.412ZM22.471,5.412H1.529V18.588H22.471Z\",\n                    fill: \"#000\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M5.764,13.588L8.235,8.412H9.765L6.706,14.824H4.824L1.765,8.412H3.294Z\",\n                    fill: \"#000\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M13.588,8.412V14.824H12.059V8.412Z\",\n                    fill: \"#000\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M18.824,8.412V14.824H17.294V8.412Z\",\n                    fill: \"#000\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, undefined),\n        color: '#000000'\n    },\n    {\n        name: 'Bootstrap',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6A2,2 0 0,0 20,4M20,18H4V6H20V18M13.5,8H10V16H13.5A2.5,2.5 0 0,0 16,13.5A2.5,2.5 0 0,0 13.5,11A2.5,2.5 0 0,0 16,8.5A2.5,2.5 0 0,0 13.5,8M13.5,9.5A1,1 0 0,1 14.5,10.5A1,1 0 0,1 13.5,11.5H11.5V9.5H13.5M13.5,13A1,1 0 0,1 14.5,14A1,1 0 0,1 13.5,15H11.5V13H13.5Z\",\n                fill: \"#7952B3\"\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, undefined),\n        color: '#7952B3'\n    },\n    {\n        name: 'Tailwind CSS',\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            viewBox: \"0 0 24 24\",\n            className: \"w-12 h-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M12,2C8,2 5.5,4 4.5,8C6,6 7.75,5.25 9.75,5.75C10.6,5.95 11.2,6.55 11.85,7.2C12.95,8.3 14.25,9.5 17,9.5C21,9.5 23.5,7.5 24.5,3.5C23,5.5 21.25,6.25 19.25,5.75C18.4,5.55 17.8,4.95 17.15,4.3C16.05,3.2 14.75,2 12,2M4.5,9.5C0.5,9.5 -2,11.5 -3,15.5C-1.5,13.5 0.25,12.75 2.25,13.25C3.1,13.45 3.7,14.05 4.35,14.7C5.45,15.8 6.75,17 9.5,17C13.5,17 16,15 17,11C15.5,13 13.75,13.75 11.75,13.25C10.9,13.05 10.3,12.45 9.65,11.8C8.55,10.7 7.25,9.5 4.5,9.5Z\",\n                fill: \"#06B6D4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined),\n        color: '#06B6D4'\n    }\n];\nfunction TechLogos({ direction = 'left', speed = 50, className = '' }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `py-8 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_fast_marquee__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            direction: direction,\n            speed: speed,\n            gradient: false,\n            pauseOnHover: true,\n            className: \"overflow-hidden\",\n            children: techLogos.map((tech, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"mx-8 flex flex-col items-center group cursor-pointer\",\n                    whileHover: {\n                        scale: 1.1,\n                        y: -5\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 glass rounded-xl group-hover:glow-box transition-all duration-300 grayscale group-hover:grayscale-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    color: tech.color\n                                },\n                                children: tech.icon\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mt-2 text-sm font-medium text-gray-400 group-hover:text-white transition-colors duration-300\",\n                            children: tech.name\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\ui\\\\TechLogos.tsx\",\n        lineNumber: 115,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/TechLogos.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/lucide-react","vendor-chunks/@swc","vendor-chunks/gsap","vendor-chunks/next-themes","vendor-chunks/typewriter-effect","vendor-chunks/react-fast-marquee"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cportfolio%5Cmordern-portfolio-saurabh&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();