'use client';

import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Download, MapPin, Phone, Mail, Calendar, ExternalLink } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

export default function ResumePage() {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.fromTo(
        '.timeline-item',
        { opacity: 0, x: -50 },
        {
          opacity: 1,
          x: 0,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.timeline-container',
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          },
        }
      );

      gsap.fromTo(
        '.skill-item',
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.skills-section',
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const personalInfo = {
    name: 'Saurabh Dahariya',
    title: 'Full Stack Developer',
    location: 'Bengaluru',
    phone: '8319130513',
    email: '<EMAIL>',
    github: 'https://github.com/saurabhdahariya',
  };

  const summary = `B.Tech (IT) graduate with strong skills in React.js, JavaScript, Node.js, and web development. 
  Quick learner, team player, and passionate about building responsive web apps. Eager to contribute and grow as a fresher developer.`;

  const skills = {
    frontend: ['ReactJs', 'HTML5', 'CSS', 'Bootstrap'],
    backend: ['Javascript', 'Node.Js', 'Express.js', 'MongoDB', 'mongoose'],
    tools: ['Git', 'GitHub', 'JIRA', 'Postman', 'Swagger'],
    familiar: ['CI/CD Pipelines', 'Docker', 'AWS'],
  };

  const projects = [
    {
      title: 'ROUTE TRACKER',
      url: 'https://saurabhd.vercel.app/map',
      description: [
        'Developed a comprehensive web-based application focused on visualizing and managing vehicle movement, leveraging the power of React and Redux for efficient state management and seamless user experience.',
        'Implemented real-time data visualization to track and display vehicle movement, providing users with instant access to critical information.',
      ],
    },
    {
      title: 'CAMPING GROUNDS',
      url: 'https://campinggrounds.onrender.com/',
      description: [
        'Developed "CampingGrounds" website with MongoDB/Mongoose for data, Express.js for backend, and EJS for dynamic views.',
        'Ensured secure user authentication for campground creation and author-exclusive deletion rights.',
        'Utilized Bootstrap for a visually appealing, responsive interface.',
        'Demonstrated proficiency in full-stack web development, prioritizing security and usability.',
      ],
    },
  ];

  const education = {
    degree: 'Bachelor of Technology (Information Technology)',
    institution: 'Bhilai Institute Of Technology, Durg',
    period: 'Sep 2019 - Jun 2023',
  };

  const training = {
    course: 'MERN STACK DEVELOPMENT',
    institution: 'JSpider BTM Layout, Bengaluru',
    period: 'Sep 2024 - Feb 2025',
  };

  return (
    <div ref={sectionRef} className="min-h-screen pt-20 pb-10 px-4 md:px-10 space-y-8">
      <div className="max-w-[90vw] mx-auto">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6">
            Resume
          </h1>
          <motion.button
            className="glass-strong px-8 py-4 rounded-full text-neon-blue hover:text-neon-green transition-colors duration-300 glow-box magnetic font-semibold"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => window.print()}
          >
            <Download size={20} className="inline mr-2" />
            Download PDF
          </motion.button>
        </motion.div>

        {/* Personal Information */}
        <motion.div
          className="glass-strong p-8 rounded-2xl mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <div className="text-center mb-6">
            <h2 className="text-3xl font-bold text-white font-orbitron mb-2">
              {personalInfo.name}
            </h2>
            <p className="text-xl text-neon-blue font-space">{personalInfo.title}</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-4 text-center">
            <div className="flex items-center justify-center space-x-2">
              <MapPin className="text-neon-green" size={16} />
              <span className="text-gray-300">{personalInfo.location}</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <Phone className="text-neon-green" size={16} />
              <span className="text-gray-300">{personalInfo.phone}</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <Mail className="text-neon-green" size={16} />
              <span className="text-gray-300">{personalInfo.email}</span>
            </div>
          </div>
        </motion.div>

        {/* Summary */}
        <motion.div
          className="glass p-6 rounded-xl mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          <h3 className="text-2xl font-bold text-neon-blue mb-4 font-space">Summary</h3>
          <p className="text-gray-300 leading-relaxed">{summary}</p>
        </motion.div>

        {/* Skills */}
        <motion.div
          className="skills-section glass p-6 rounded-xl mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <h3 className="text-2xl font-bold text-neon-blue mb-6 font-space">Skills</h3>
          <div className="grid md:grid-cols-2 gap-6">
            {Object.entries(skills).map(([category, skillList]) => (
              <div key={category} className="skill-item">
                <h4 className="text-lg font-semibold text-neon-green mb-3 capitalize">
                  {category === 'familiar' ? 'Familiar With' : category}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {skillList.map((skill, index) => (
                    <span
                      key={index}
                      className="glass px-3 py-1 rounded-full text-sm text-white"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Projects */}
        <motion.div
          className="glass p-6 rounded-xl mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          <h3 className="text-2xl font-bold text-neon-blue mb-6 font-space">Projects</h3>
          <div className="space-y-6">
            {projects.map((project, index) => (
              <div key={index} className="timeline-item border-l-2 border-neon-green pl-6 relative">
                <div className="absolute -left-2 top-0 w-4 h-4 bg-neon-green rounded-full"></div>
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-lg font-semibold text-white">{project.title}</h4>
                  <a
                    href={project.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-neon-blue hover:text-neon-green transition-colors"
                  >
                    <ExternalLink size={16} />
                  </a>
                </div>
                <ul className="text-gray-300 space-y-2">
                  {project.description.map((desc, descIndex) => (
                    <li key={descIndex} className="text-sm leading-relaxed">
                      • {desc}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Education & Training */}
        <div className="timeline-container grid md:grid-cols-2 gap-8">
          <motion.div
            className="timeline-item glass p-6 rounded-xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <h3 className="text-2xl font-bold text-neon-blue mb-4 font-space">Education</h3>
            <div className="border-l-2 border-neon-purple pl-6 relative">
              <div className="absolute -left-2 top-0 w-4 h-4 bg-neon-purple rounded-full"></div>
              <h4 className="text-lg font-semibold text-white mb-1">{education.degree}</h4>
              <p className="text-neon-green font-medium mb-2">{education.institution}</p>
              <div className="flex items-center text-gray-400 text-sm">
                <Calendar size={14} className="mr-2" />
                {education.period}
              </div>
            </div>
          </motion.div>

          <motion.div
            className="timeline-item glass p-6 rounded-xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            <h3 className="text-2xl font-bold text-neon-blue mb-4 font-space">Training</h3>
            <div className="border-l-2 border-neon-pink pl-6 relative">
              <div className="absolute -left-2 top-0 w-4 h-4 bg-neon-pink rounded-full"></div>
              <h4 className="text-lg font-semibold text-white mb-1">{training.course}</h4>
              <p className="text-neon-green font-medium mb-2">{training.institution}</p>
              <div className="flex items-center text-gray-400 text-sm">
                <Calendar size={14} className="mr-2" />
                {training.period}
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
