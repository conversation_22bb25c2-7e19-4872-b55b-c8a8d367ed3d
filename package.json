{"name": "mordern-portfolio-<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@tsparticles/engine": "^3.8.1", "@tsparticles/react": "^3.0.0", "@types/three": "^0.177.0", "aos": "^2.3.4", "framer-motion": "^12.16.0", "gsap": "^3.13.0", "lucide-react": "^0.513.0", "next": "15.3.3", "next-themes": "^0.4.6", "openai": "^5.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.57.0", "react-parallax-tilt": "^1.7.297", "three": "^0.177.0", "tsparticles": "^3.8.1", "typewriter-effect": "^2.22.0", "vanta": "^0.5.24", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}