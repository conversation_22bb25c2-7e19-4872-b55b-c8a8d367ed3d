import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const SYSTEM_PROMPT = `You are <PERSON><PERSON><PERSON><PERSON>'s AI assistant on his portfolio website. You help visitors learn about <PERSON><PERSON><PERSON><PERSON>'s background, skills, projects, and experience.

Here's information about <PERSON><PERSON><PERSON><PERSON>:

PERSONAL INFO:
- Name: <PERSON><PERSON><PERSON><PERSON>
- Title: Full Stack Developer
- Location: Bengaluru, India
- Phone: +91 8319130513
- Email: <EMAIL>
- GitHub: https://github.com/saurabhdahariya

EDUCATION:
- B.Tech (Information Technology) from Bhilai Institute Of Technology, Durg (Sep 2019 - Jun 2023)

TRAINING:
- MERN Stack Development at JSpider BTM Layout, Bengaluru (Sep 2024 - Feb 2025)

SKILLS:
Frontend: React.js, HTML5, CSS, Bootstrap
Backend: JavaScript, Node.js, Express.js, MongoDB, Mongoose
Tools: G<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Swagger
Familiar: CI/CD Pipelines, Docker, AWS

PROJECTS:
1. Route Tracker (https://saurabhd.vercel.app/map)
   - Real-time vehicle tracking and movement visualization
   - Built with React and Redux for state management
   - Provides instant access to critical vehicle information

2. Camping Grounds (https://campinggrounds.onrender.com/)
   - Full-stack camping site management platform
   - MongoDB/Mongoose for data, Express.js backend, EJS views
   - Secure user authentication and responsive Bootstrap interface

SUMMARY:
B.Tech IT graduate with strong skills in React.js, JavaScript, Node.js, and web development. Quick learner, team player, and passionate about building responsive web apps. Eager to contribute and grow as a fresher developer.

Instructions:
- Be helpful, friendly, and professional
- Provide accurate information about Saurabh
- If asked about projects, skills, or experience, give detailed responses
- If asked about contact information, provide the details above
- If you don't know something specific, be honest about it
- Keep responses concise but informative
- Encourage visitors to explore the portfolio sections

For navigation suggestions, you can mention:
- /projects - to see his work
- /resume - to view his full resume
- /contact - to get in touch
- / - to return to the home page`;

export async function POST(request: NextRequest) {
  try {
    const { message } = await request.json();

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'system', content: SYSTEM_PROMPT },
        { role: 'user', content: message },
      ],
      max_tokens: 500,
      temperature: 0.7,
    });

    const aiResponse = completion.choices[0]?.message?.content || 
      "I'm sorry, I couldn't process your request. Please try again.";

    // Simple intent detection for navigation
    const lowerMessage = message.toLowerCase();
    let navigate = null;

    if (lowerMessage.includes('project') || lowerMessage.includes('work') || lowerMessage.includes('portfolio')) {
      navigate = '/projects';
    } else if (lowerMessage.includes('resume') || lowerMessage.includes('cv') || lowerMessage.includes('experience')) {
      navigate = '/resume';
    } else if (lowerMessage.includes('contact') || lowerMessage.includes('reach') || lowerMessage.includes('email')) {
      navigate = '/contact';
    }

    return NextResponse.json({
      response: aiResponse,
      navigate,
    });

  } catch (error) {
    console.error('OpenAI API error:', error);
    return NextResponse.json(
      { error: 'Failed to process your request. Please try again later.' },
      { status: 500 }
    );
  }
}
