{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/SplashScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { gsap } from 'gsap';\n\ninterface SplashScreenProps {\n  onComplete: () => void;\n}\n\nexport default function SplashScreen({ onComplete }: SplashScreenProps) {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [animationComplete, setAnimationComplete] = useState(false);\n\n  const steps = [\n    \"Hello World\",\n    \"I am <PERSON><PERSON><PERSON><PERSON>\",\n    \"A Full Stack Software Developer\"\n  ];\n\n  useEffect(() => {\n    const timeline = gsap.timeline();\n\n    // Step 1: Hello World (2s)\n    timeline\n      .to({}, { duration: 2 })\n      .call(() => setCurrentStep(1))\n      // Step 2: I am <PERSON><PERSON><PERSON><PERSON> (2.5s)\n      .to({}, { duration: 2.5 })\n      .call(() => setCurrentStep(2))\n      // Step 3: A Full Stack Software Developer (2.5s)\n      .to({}, { duration: 2.5 })\n      .call(() => {\n        setAnimationComplete(true);\n        setTimeout(onComplete, 800);\n      });\n\n    return () => {\n      timeline.kill();\n    };\n  }, [onComplete]);\n\n  const typewriterVariants = {\n    hidden: { width: 0 },\n    visible: {\n      width: \"100%\",\n      transition: {\n        duration: 1.5,\n        ease: \"easeInOut\",\n        delay: 0.3\n      }\n    }\n  };\n\n  const glowVariants = {\n    initial: {\n      textShadow: \"0 0 10px rgba(0, 212, 255, 0.5)\"\n    },\n    animate: {\n      textShadow: [\n        \"0 0 10px rgba(0, 212, 255, 0.5)\",\n        \"0 0 20px rgba(0, 212, 255, 0.8)\",\n        \"0 0 30px rgba(0, 212, 255, 1)\",\n        \"0 0 20px rgba(0, 212, 255, 0.8)\",\n        \"0 0 10px rgba(0, 212, 255, 0.5)\"\n      ],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {!animationComplete && (\n        <motion.div\n          className=\"fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black\"\n          initial={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          {/* Animated background particles */}\n          <div className=\"absolute inset-0 overflow-hidden\">\n            {[...Array(50)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-1 h-1 bg-neon-blue rounded-full\"\n                style={{\n                  left: `${Math.random() * 100}%`,\n                  top: `${Math.random() * 100}%`,\n                }}\n                animate={{\n                  opacity: [0, 1, 0],\n                  scale: [0, 1, 0],\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  delay: Math.random() * 3,\n                }}\n              />\n            ))}\n          </div>\n\n          <div className=\"text-center relative z-10\">\n            <AnimatePresence mode=\"wait\">\n              {currentStep === 0 && (\n                <motion.div\n                  key=\"hello\"\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -30 }}\n                  transition={{ duration: 0.8 }}\n                  className=\"relative\"\n                >\n                  <motion.div\n                    className=\"text-4xl md:text-6xl font-bold text-neon-blue font-space overflow-hidden whitespace-nowrap border-r-2 border-neon-blue\"\n                    variants={typewriterVariants}\n                    initial=\"hidden\"\n                    animate=\"visible\"\n                  >\n                    Hello World\n                  </motion.div>\n                </motion.div>\n              )}\n\n              {currentStep === 1 && (\n                <motion.div\n                  key=\"name\"\n                  layoutId=\"main-name\"\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 1.2 }}\n                  transition={{ duration: 1, ease: \"easeOut\" }}\n                  className=\"relative\"\n                >\n                  <motion.div\n                    className=\"text-5xl md:text-7xl font-bold gradient-text font-orbitron\"\n                    variants={glowVariants}\n                    initial=\"initial\"\n                    animate=\"animate\"\n                  >\n                    I am Saurabh Dahariya\n                  </motion.div>\n                </motion.div>\n              )}\n\n              {currentStep === 2 && (\n                <motion.div\n                  key=\"title\"\n                  initial={{ opacity: 0, y: 50 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -50 }}\n                  transition={{ duration: 1, ease: \"easeOut\" }}\n                  className=\"space-y-4\"\n                >\n                  <motion.div\n                    className=\"text-5xl md:text-7xl font-bold gradient-text font-orbitron\"\n                    variants={glowVariants}\n                    initial=\"initial\"\n                    animate=\"animate\"\n                  >\n                    I am Saurabh Dahariya\n                  </motion.div>\n                  <motion.div\n                    className=\"text-2xl md:text-3xl text-neon-green font-space\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    transition={{ delay: 0.5, duration: 1 }}\n                  >\n                    A Full Stack Software Developer\n                  </motion.div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAUe,SAAS,aAAa,EAAE,UAAU,EAAqB;;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,QAAQ;QACZ;QACA;QACA;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,WAAW,gJAAA,CAAA,OAAI,CAAC,QAAQ;YAE9B,2BAA2B;YAC3B,SACG,EAAE,CAAC,CAAC,GAAG;gBAAE,UAAU;YAAE,GACrB,IAAI;0CAAC,IAAM,eAAe;wCAC3B,uCAAuC;aACtC,EAAE,CAAC,CAAC,GAAG;gBAAE,UAAU;YAAI,GACvB,IAAI;0CAAC,IAAM,eAAe;wCAC3B,iDAAiD;aAChD,EAAE,CAAC,CAAC,GAAG;gBAAE,UAAU;YAAI,GACvB,IAAI;0CAAC;oBACJ,qBAAqB;oBACrB,WAAW,YAAY;gBACzB;;YAEF;0CAAO;oBACL,SAAS,IAAI;gBACf;;QACF;iCAAG;QAAC;KAAW;IAEf,MAAM,qBAAqB;QACzB,QAAQ;YAAE,OAAO;QAAE;QACnB,SAAS;YACP,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YACP,YAAY;QACd;QACA,SAAS;YACP,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,CAAC,mCACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAChC;4BACA,SAAS;gCACP,SAAS;oCAAC;oCAAG;oCAAG;iCAAE;gCAClB,OAAO;oCAAC;oCAAG;oCAAG;iCAAE;4BAClB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,OAAO,KAAK,MAAM,KAAK;4BACzB;2BAdK;;;;;;;;;;8BAmBX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;wBAAC,MAAK;;4BACnB,gBAAgB,mBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,UAAU;oCACV,SAAQ;oCACR,SAAQ;8CACT;;;;;;+BAZG;;;;;4BAkBP,gBAAgB,mBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAS;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAC/B,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAU;gCAC3C,WAAU;0CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,UAAU;oCACV,SAAQ;oCACR,SAAQ;8CACT;;;;;;+BAbG;;;;;4BAmBP,gBAAgB,mBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAU;gCAC3C,WAAU;;kDAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAU;wCACV,SAAQ;wCACR,SAAQ;kDACT;;;;;;kDAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAE;kDACvC;;;;;;;+BApBG;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BtB;GA5KwB;KAAA", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { gsap } from 'gsap';\nimport { ChevronDown } from 'lucide-react';\nimport Typewriter from 'typewriter-effect';\nimport TechLogos from '@/components/ui/TechLogos';\n\nexport default function HeroSection() {\n  const heroRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Animate hero elements after splash screen\n      gsap.fromTo(\n        '.hero-content',\n        { opacity: 0, y: 50 },\n        { opacity: 1, y: 0, duration: 1, delay: 0.5, ease: 'power2.out' }\n      );\n\n      gsap.fromTo(\n        '.hero-subtitle',\n        { opacity: 0, y: 30 },\n        { opacity: 1, y: 0, duration: 1, delay: 1, ease: 'power2.out' }\n      );\n\n      gsap.fromTo(\n        '.scroll-indicator',\n        { opacity: 0, y: 20 },\n        { opacity: 1, y: 0, duration: 1, delay: 1.5, ease: 'power2.out' }\n      );\n    }, heroRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const scrollToNext = () => {\n    const nextSection = document.getElementById('about');\n    if (nextSection) {\n      nextSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section\n      ref={heroRef}\n      className=\"min-h-screen flex items-center justify-center relative pt-20 pb-10 px-4 md:px-10\"\n    >\n      <div className=\"max-w-[90vw] mx-auto text-center space-y-8\">\n        <motion.div\n          className=\"hero-content\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.5, duration: 1 }}\n        >\n          {/* Main Title */}\n          <motion.h1\n            className=\"text-5xl md:text-7xl lg:text-8xl font-bold mb-6 gradient-text font-orbitron\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 1 }}\n          >\n            Saurabh Dahariya\n          </motion.h1>\n\n          {/* Animated Subtitle */}\n          <motion.div\n            className=\"hero-subtitle text-xl md:text-3xl lg:text-4xl text-neon-blue mb-8 font-space h-16 flex items-center justify-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.2, duration: 0.8 }}\n          >\n            <Typewriter\n              options={{\n                strings: [\n                  'Full Stack Software Developer',\n                  'React.js Specialist',\n                  'Node.js Expert',\n                  'MERN Stack Developer',\n                  'Problem Solver'\n                ],\n                autoStart: true,\n                loop: true,\n                delay: 75,\n                deleteSpeed: 50,\n              }}\n            />\n          </motion.div>\n\n          {/* Description */}\n          <motion.p\n            className=\"text-lg md:text-xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.5, duration: 0.8 }}\n          >\n            Passionate B.Tech IT graduate from Bengaluru with expertise in modern web technologies.\n            I build responsive, scalable applications and love turning ideas into digital reality.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.8, duration: 0.8 }}\n          >\n            <motion.button\n              className=\"glass-strong px-8 py-4 rounded-full text-neon-blue hover:text-neon-green transition-all duration-300 glow-box magnetic font-semibold border border-neon-blue/30 hover:border-neon-green/50\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}\n            >\n              View My Work\n            </motion.button>\n\n            <motion.button\n              className=\"glass px-8 py-4 rounded-full text-white hover:text-neon-blue transition-all duration-300 magnetic font-semibold border border-white/20 hover:border-neon-blue/50\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}\n            >\n              Get In Touch\n            </motion.button>\n          </motion.div>\n        </motion.div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          className=\"scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer\"\n          onClick={scrollToNext}\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}\n        >\n          <div className=\"flex flex-col items-center text-neon-blue hover:text-neon-green transition-colors\">\n            <span className=\"text-sm mb-2 font-medium\">Scroll Down</span>\n            <ChevronDown size={24} />\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Floating Elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <motion.div\n          className=\"absolute top-1/4 left-1/4 w-4 h-4 bg-neon-blue rounded-full opacity-60\"\n          animate={{\n            y: [0, -20, 0],\n            opacity: [0.6, 1, 0.6],\n          }}\n          transition={{\n            duration: 4,\n            repeat: Infinity,\n            ease: 'easeInOut',\n          }}\n        />\n        <motion.div\n          className=\"absolute top-1/3 right-1/4 w-6 h-6 bg-neon-green rounded-full opacity-40\"\n          animate={{\n            y: [0, 30, 0],\n            opacity: [0.4, 0.8, 0.4],\n          }}\n          transition={{\n            duration: 5,\n            repeat: Infinity,\n            ease: 'easeInOut',\n            delay: 1,\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-1/3 left-1/3 w-3 h-3 bg-neon-purple rounded-full opacity-50\"\n          animate={{\n            y: [0, -15, 0],\n            opacity: [0.5, 1, 0.5],\n          }}\n          transition={{\n            duration: 3,\n            repeat: Infinity,\n            ease: 'easeInOut',\n            delay: 2,\n          }}\n        />\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASe,SAAS;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;6CAAC;oBACvB,4CAA4C;oBAC5C,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,iBACA;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBAAE,SAAS;wBAAG,GAAG;wBAAG,UAAU;wBAAG,OAAO;wBAAK,MAAM;oBAAa;oBAGlE,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,kBACA;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBAAE,SAAS;wBAAG,GAAG;wBAAG,UAAU;wBAAG,OAAO;wBAAG,MAAM;oBAAa;oBAGhE,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,qBACA;wBAAE,SAAS;wBAAG,GAAG;oBAAG,GACpB;wBAAE,SAAS;wBAAG,GAAG;wBAAG,UAAU;wBAAG,OAAO;wBAAK,MAAM;oBAAa;gBAEpE;4CAAG;YAEH;yCAAO,IAAM,IAAI,MAAM;;QACzB;gCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,IAAI,aAAa;YACf,YAAY,cAAc,CAAC;gBAAE,UAAU;YAAS;QAClD;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAE;;0CAGtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAE;0CACvC;;;;;;0CAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;0CAExC,cAAA,6LAAC,wJAAA,CAAA,UAAU;oCACT,SAAS;wCACP,SAAS;4CACP;4CACA;4CACA;4CACA;4CACA;yCACD;wCACD,WAAW;wCACX,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;;;;;;;;;;;0CAKJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;0CACzC;;;;;;0CAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;;kDAExC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAM,GAAG,CAAC;wCAAE;wCACjC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,SAAS,cAAc,CAAC,aAAa,eAAe;gDAAE,UAAU;4CAAS;kDACzF;;;;;;kDAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAM,GAAG,CAAC;wCAAE;wCACjC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,SAAS,cAAc,CAAC,YAAY,eAAe;gDAAE,UAAU;4CAAS;kDACxF;;;;;;;;;;;;;;;;;;kCAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;wBACT,SAAS;4BAAE,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;wBAAC;wBACzB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;wBAAY;kCAE/D,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAA2B;;;;;;8CAC3C,6LAAC,uNAAA,CAAA,cAAW;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;0BAMzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;;;;;;;;;;;;;AAKV;GAhLwB;KAAA", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/sections/AboutSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { MapPin, Phone, Mail, GraduationCap, Code, Heart } from 'lucide-react';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function AboutSection() {\n  const sectionRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Animate cards on scroll\n      gsap.fromTo(\n        '.about-card',\n        { opacity: 0, y: 50, scale: 0.9 },\n        {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.8,\n          stagger: 0.2,\n          ease: 'power2.out',\n          scrollTrigger: {\n            trigger: '.about-cards',\n            start: 'top 80%',\n            end: 'bottom 20%',\n            toggleActions: 'play none none reverse',\n          },\n        }\n      );\n\n      // Animate text elements\n      gsap.fromTo(\n        '.about-text',\n        { opacity: 0, x: -50 },\n        {\n          opacity: 1,\n          x: 0,\n          duration: 1,\n          ease: 'power2.out',\n          scrollTrigger: {\n            trigger: '.about-content',\n            start: 'top 80%',\n            toggleActions: 'play none none reverse',\n          },\n        }\n      );\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const personalInfo = [\n    { icon: MapPin, label: 'Location', value: 'Bengaluru, India' },\n    { icon: Phone, label: 'Phone', value: '+91 8319130513' },\n    { icon: Mail, label: 'Email', value: '<EMAIL>' },\n    { icon: GraduationCap, label: 'Education', value: 'B.Tech (IT) - Bhilai Institute Of Technology' },\n  ];\n\n  const highlights = [\n    {\n      icon: Code,\n      title: 'Full Stack Development',\n      description: 'Expertise in React.js, Node.js, Express.js, and MongoDB for building complete web applications.',\n    },\n    {\n      icon: Heart,\n      title: 'Passionate Learner',\n      description: 'Always eager to learn new technologies and stay updated with the latest industry trends.',\n    },\n    {\n      icon: GraduationCap,\n      title: 'Fresh Graduate',\n      description: 'Recent B.Tech IT graduate with hands-on experience through projects and training.',\n    },\n  ];\n\n  return (\n    <section\n      id=\"about\"\n      ref={sectionRef}\n      className=\"min-h-screen py-20 px-4 sm:px-6 lg:px-8\"\n    >\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6\">\n            About Me\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Get to know more about my background, skills, and passion for technology\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center about-content\">\n          {/* Left Side - Personal Info */}\n          <div className=\"about-text\">\n            <h3 className=\"text-3xl font-bold text-neon-blue mb-6 font-space\">\n              Hello! I'm Saurabh Dahariya\n            </h3>\n            \n            <p className=\"text-lg text-gray-300 mb-8 leading-relaxed\">\n              I'm a passionate Full Stack Developer with a strong foundation in modern web technologies. \n              As a recent B.Tech IT graduate from Bhilai Institute Of Technology, I bring fresh perspectives \n              and enthusiasm to every project I work on.\n            </p>\n\n            <p className=\"text-lg text-gray-300 mb-8 leading-relaxed\">\n              My journey in web development started during my college years, and I've been continuously \n              learning and building projects to enhance my skills. I'm particularly passionate about \n              creating responsive, user-friendly applications that solve real-world problems.\n            </p>\n\n            {/* Personal Information Cards */}\n            <div className=\"grid sm:grid-cols-2 gap-4\">\n              {personalInfo.map((info, index) => {\n                const Icon = info.icon;\n                return (\n                  <motion.div\n                    key={index}\n                    className=\"glass p-4 rounded-lg hover:glow-box transition-all duration-300\"\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <Icon className=\"text-neon-blue\" size={20} />\n                      <div>\n                        <p className=\"text-sm text-gray-400\">{info.label}</p>\n                        <p className=\"text-white font-medium\">{info.value}</p>\n                      </div>\n                    </div>\n                  </motion.div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Right Side - Highlights */}\n          <div className=\"about-cards\">\n            <h3 className=\"text-2xl font-bold text-neon-green mb-8 font-space\">\n              What Makes Me Unique\n            </h3>\n            \n            <div className=\"space-y-6\">\n              {highlights.map((highlight, index) => {\n                const Icon = highlight.icon;\n                return (\n                  <motion.div\n                    key={index}\n                    className=\"about-card glass-strong p-6 rounded-xl hover:glow-box transition-all duration-300\"\n                    whileHover={{ scale: 1.02, y: -5 }}\n                  >\n                    <div className=\"flex items-start space-x-4\">\n                      <div className=\"p-3 bg-gradient-to-r from-neon-blue to-neon-green rounded-lg\">\n                        <Icon className=\"text-black\" size={24} />\n                      </div>\n                      <div>\n                        <h4 className=\"text-xl font-semibold text-white mb-2\">\n                          {highlight.title}\n                        </h4>\n                        <p className=\"text-gray-300 leading-relaxed\">\n                          {highlight.description}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Training Section */}\n        <motion.div\n          className=\"mt-16 text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"glass-strong p-8 rounded-2xl max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-neon-purple mb-4 font-space\">\n              Current Training\n            </h3>\n            <p className=\"text-lg text-gray-300 mb-4\">\n              <strong>MERN Stack Development</strong> at JSpider BTM Layout, Bengaluru\n            </p>\n            <p className=\"text-gray-400\">\n              September 2024 - February 2025\n            </p>\n            <p className=\"text-gray-300 mt-4\">\n              Intensive training program focusing on MongoDB, Express.js, React.js, and Node.js \n              to build full-stack web applications.\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAElB,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;8CAAC;oBACvB,0BAA0B;oBAC1B,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,eACA;wBAAE,SAAS;wBAAG,GAAG;wBAAI,OAAO;oBAAI,GAChC;wBACE,SAAS;wBACT,GAAG;wBACH,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,KAAK;4BACL,eAAe;wBACjB;oBACF;oBAGF,wBAAwB;oBACxB,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,eACA;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG,GACrB;wBACE,SAAS;wBACT,GAAG;wBACH,UAAU;wBACV,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;gBAEJ;6CAAG;YAEH;0CAAO,IAAM,IAAI,MAAM;;QACzB;iCAAG,EAAE;IAEL,MAAM,eAAe;QACnB;YAAE,MAAM,6MAAA,CAAA,SAAM;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC7D;YAAE,MAAM,uMAAA,CAAA,QAAK;YAAE,OAAO;YAAS,OAAO;QAAiB;QACvD;YAAE,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;YAAS,OAAO;QAAgC;QACrE;YAAE,MAAM,2NAAA,CAAA,gBAAa;YAAE,OAAO;YAAa,OAAO;QAA+C;KAClG;IAED,MAAM,aAAa;QACjB;YACE,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,2NAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QACC,IAAG;QACH,KAAK;QACL,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,6LAAC;4BAAG,WAAU;sCAAkE;;;;;;sCAGhF,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAIlE,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAM1D,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAO1D,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,MAAM;wCACvB,MAAM,OAAO,KAAK,IAAI;wCACtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;sDAE1B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;wDAAiB,MAAM;;;;;;kEACvC,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAyB,KAAK,KAAK;;;;;;0EAChD,6LAAC;gEAAE,WAAU;0EAA0B,KAAK,KAAK;;;;;;;;;;;;;;;;;;2CARhD;;;;;oCAaX;;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAInE,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW;wCAC1B,MAAM,OAAO,UAAU,IAAI;wCAC3B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,YAAY;gDAAE,OAAO;gDAAM,GAAG,CAAC;4CAAE;sDAEjC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;4DAAa,MAAM;;;;;;;;;;;kEAErC,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,UAAU,KAAK;;;;;;0EAElB,6LAAC;gEAAE,WAAU;0EACV,UAAU,WAAW;;;;;;;;;;;;;;;;;;2CAbvB;;;;;oCAmBX;;;;;;;;;;;;;;;;;;8BAMN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;kDAAO;;;;;;oCAA+B;;;;;;;0CAEzC,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAG7B,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C;GAtMwB;KAAA", "debugId": null}}, {"offset": {"line": 1132, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/sections/TechStackSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport Marquee from 'react-fast-marquee';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function TechStackSection() {\n  const sectionRef = useRef<HTMLDivElement>(null);\n\n  const techStack = {\n    frontend: [\n      { name: 'React.js', color: '#61DAFB', level: 90 },\n      { name: 'HTML5', color: '#E34F26', level: 95 },\n      { name: 'CSS3', color: '#1572B6', level: 90 },\n      { name: 'JavaScript', color: '#F7DF1E', level: 85 },\n      { name: 'Bootstrap', color: '#7952B3', level: 80 },\n    ],\n    backend: [\n      { name: 'Node.js', color: '#339933', level: 85 },\n      { name: 'Express.js', color: '#000000', level: 80 },\n      { name: 'MongoDB', color: '#47A248', level: 75 },\n      { name: 'Mongoose', color: '#880000', level: 75 },\n    ],\n    tools: [\n      { name: 'Git', color: '#F05032', level: 85 },\n      { name: 'GitHub', color: '#181717', level: 85 },\n      { name: 'JIRA', color: '#0052CC', level: 70 },\n      { name: 'Postman', color: '#FF6C37', level: 80 },\n      { name: 'Swagger', color: '#85EA2D', level: 70 },\n    ],\n    familiar: [\n      { name: 'CI/CD Pipelines', color: '#326CE5', level: 60 },\n      { name: 'Docker', color: '#2496ED', level: 55 },\n      { name: 'AWS', color: '#FF9900', level: 50 },\n    ],\n  };\n\n  const marqueeItems = [\n    'React.js', 'Node.js', 'Express.js', 'MongoDB', 'JavaScript', 'HTML5', 'CSS3', \n    'Bootstrap', 'Git', 'GitHub', 'JIRA', 'Postman', 'Swagger', 'Docker', 'AWS'\n  ];\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Animate skill bars\n      gsap.fromTo(\n        '.skill-bar-fill',\n        { width: '0%' },\n        {\n          width: (i, el) => el.getAttribute('data-level') + '%',\n          duration: 1.5,\n          ease: 'power2.out',\n          stagger: 0.1,\n          scrollTrigger: {\n            trigger: '.skills-container',\n            start: 'top 80%',\n            toggleActions: 'play none none reverse',\n          },\n        }\n      );\n\n      // Animate category cards\n      gsap.fromTo(\n        '.tech-category',\n        { opacity: 0, y: 50, scale: 0.9 },\n        {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.8,\n          stagger: 0.2,\n          ease: 'power2.out',\n          scrollTrigger: {\n            trigger: '.skills-container',\n            start: 'top 80%',\n            toggleActions: 'play none none reverse',\n          },\n        }\n      );\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const renderSkillCategory = (title: string, skills: typeof techStack.frontend, color: string) => (\n    <motion.div\n      className=\"tech-category glass-strong p-6 rounded-xl\"\n      whileHover={{ scale: 1.02, y: -5 }}\n      transition={{ duration: 0.3 }}\n    >\n      <h3 className=\"text-xl font-bold mb-6 font-space\" style={{ color }}>\n        {title}\n      </h3>\n      <div className=\"space-y-4\">\n        {skills.map((skill, index) => (\n          <div key={index} className=\"skill-item\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-white font-medium\">{skill.name}</span>\n              <span className=\"text-gray-400 text-sm\">{skill.level}%</span>\n            </div>\n            <div className=\"w-full bg-gray-700 rounded-full h-2 overflow-hidden\">\n              <div\n                className=\"skill-bar-fill h-full rounded-full transition-all duration-300\"\n                data-level={skill.level}\n                style={{ backgroundColor: skill.color }}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <section\n      id=\"tech-stack\"\n      ref={sectionRef}\n      className=\"min-h-screen py-20 px-4 sm:px-6 lg:px-8\"\n    >\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6\">\n            Tech Stack\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Technologies and tools I work with to bring ideas to life\n          </p>\n        </motion.div>\n\n        {/* Infinite Scrolling Tech Stack */}\n        <div className=\"mb-16\">\n          <Marquee\n            gradient={false}\n            speed={50}\n            className=\"py-4\"\n            pauseOnHover={true}\n          >\n            {marqueeItems.map((tech, index) => (\n              <div\n                key={index}\n                className=\"mx-8 glass px-6 py-3 rounded-full text-neon-blue hover:text-neon-green transition-colors duration-300 font-semibold whitespace-nowrap\"\n              >\n                {tech}\n              </div>\n            ))}\n          </Marquee>\n        </div>\n\n        {/* Skills Grid */}\n        <div className=\"skills-container grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {renderSkillCategory('Frontend', techStack.frontend, '#00d4ff')}\n          {renderSkillCategory('Backend', techStack.backend, '#00ff88')}\n          {renderSkillCategory('Tools', techStack.tools, '#8b5cf6')}\n          {renderSkillCategory('Familiar With', techStack.familiar, '#f472b6')}\n        </div>\n\n        {/* Additional Info */}\n        <motion.div\n          className=\"mt-16 text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"glass p-8 rounded-2xl max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-neon-blue mb-4 font-space\">\n              Always Learning\n            </h3>\n            <p className=\"text-lg text-gray-300 leading-relaxed\">\n              Technology evolves rapidly, and I'm committed to staying current with the latest trends \n              and best practices. I'm always exploring new frameworks, tools, and methodologies to \n              improve my development skills and deliver better solutions.\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4 mt-6\">\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                Next.js\n              </span>\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                TypeScript\n              </span>\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                GraphQL\n              </span>\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                Microservices\n              </span>\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                Cloud Computing\n              </span>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa;AAElB,SAAS;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,MAAM,YAAY;QAChB,UAAU;YACR;gBAAE,MAAM;gBAAY,OAAO;gBAAW,OAAO;YAAG;YAChD;gBAAE,MAAM;gBAAS,OAAO;gBAAW,OAAO;YAAG;YAC7C;gBAAE,MAAM;gBAAQ,OAAO;gBAAW,OAAO;YAAG;YAC5C;gBAAE,MAAM;gBAAc,OAAO;gBAAW,OAAO;YAAG;YAClD;gBAAE,MAAM;gBAAa,OAAO;gBAAW,OAAO;YAAG;SAClD;QACD,SAAS;YACP;gBAAE,MAAM;gBAAW,OAAO;gBAAW,OAAO;YAAG;YAC/C;gBAAE,MAAM;gBAAc,OAAO;gBAAW,OAAO;YAAG;YAClD;gBAAE,MAAM;gBAAW,OAAO;gBAAW,OAAO;YAAG;YAC/C;gBAAE,MAAM;gBAAY,OAAO;gBAAW,OAAO;YAAG;SACjD;QACD,OAAO;YACL;gBAAE,MAAM;gBAAO,OAAO;gBAAW,OAAO;YAAG;YAC3C;gBAAE,MAAM;gBAAU,OAAO;gBAAW,OAAO;YAAG;YAC9C;gBAAE,MAAM;gBAAQ,OAAO;gBAAW,OAAO;YAAG;YAC5C;gBAAE,MAAM;gBAAW,OAAO;gBAAW,OAAO;YAAG;YAC/C;gBAAE,MAAM;gBAAW,OAAO;gBAAW,OAAO;YAAG;SAChD;QACD,UAAU;YACR;gBAAE,MAAM;gBAAmB,OAAO;gBAAW,OAAO;YAAG;YACvD;gBAAE,MAAM;gBAAU,OAAO;gBAAW,OAAO;YAAG;YAC9C;gBAAE,MAAM;gBAAO,OAAO;gBAAW,OAAO;YAAG;SAC5C;IACH;IAEA,MAAM,eAAe;QACnB;QAAY;QAAW;QAAc;QAAW;QAAc;QAAS;QACvE;QAAa;QAAO;QAAU;QAAQ;QAAW;QAAW;QAAU;KACvE;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,MAAM,gJAAA,CAAA,OAAI,CAAC,OAAO;kDAAC;oBACvB,qBAAqB;oBACrB,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,mBACA;wBAAE,OAAO;oBAAK,GACd;wBACE,KAAK;8DAAE,CAAC,GAAG,KAAO,GAAG,YAAY,CAAC,gBAAgB;;wBAClD,UAAU;wBACV,MAAM;wBACN,SAAS;wBACT,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;oBAGF,yBAAyB;oBACzB,gJAAA,CAAA,OAAI,CAAC,MAAM,CACT,kBACA;wBAAE,SAAS;wBAAG,GAAG;wBAAI,OAAO;oBAAI,GAChC;wBACE,SAAS;wBACT,GAAG;wBACH,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,MAAM;wBACN,eAAe;4BACb,SAAS;4BACT,OAAO;4BACP,eAAe;wBACjB;oBACF;gBAEJ;iDAAG;YAEH;8CAAO,IAAM,IAAI,MAAM;;QACzB;qCAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC,OAAe,QAAmC,sBAC7E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,YAAY;gBAAE,OAAO;gBAAM,GAAG,CAAC;YAAE;YACjC,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,6LAAC;oBAAG,WAAU;oBAAoC,OAAO;wBAAE;oBAAM;8BAC9D;;;;;;8BAEH,6LAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA0B,MAAM,IAAI;;;;;;sDACpD,6LAAC;4CAAK,WAAU;;gDAAyB,MAAM,KAAK;gDAAC;;;;;;;;;;;;;8CAEvD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,cAAY,MAAM,KAAK;wCACvB,OAAO;4CAAE,iBAAiB,MAAM,KAAK;wCAAC;;;;;;;;;;;;2BATlC;;;;;;;;;;;;;;;;IAkBlB,qBACE,6LAAC;QACC,IAAG;QACH,KAAK;QACL,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,6LAAC;4BAAG,WAAU;sCAAkE;;;;;;sCAGhF,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,4JAAA,CAAA,UAAO;wBACN,UAAU;wBACV,OAAO;wBACP,WAAU;wBACV,cAAc;kCAEb,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;;;;;;8BAUb,6LAAC;oBAAI,WAAU;;wBACZ,oBAAoB,YAAY,UAAU,QAAQ,EAAE;wBACpD,oBAAoB,WAAW,UAAU,OAAO,EAAE;wBAClD,oBAAoB,SAAS,UAAU,KAAK,EAAE;wBAC9C,oBAAoB,iBAAiB,UAAU,QAAQ,EAAE;;;;;;;8BAI5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAKrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrF;GApMwB;KAAA", "debugId": null}}, {"offset": {"line": 1604, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport SplashScreen from '@/components/ui/SplashScreen';\nimport HeroSection from '@/components/sections/HeroSection';\nimport AboutSection from '@/components/sections/AboutSection';\nimport TechStackSection from '@/components/sections/TechStackSection';\n\nexport default function Home() {\n  const [showSplash, setShowSplash] = useState(true);\n\n  const handleSplashComplete = () => {\n    setShowSplash(false);\n  };\n\n  return (\n    <>\n      {showSplash && <SplashScreen onComplete={handleSplashComplete} />}\n\n      {!showSplash && (\n        <div className=\"min-h-screen\">\n          <HeroSection />\n          <AboutSection />\n          <TechStackSection />\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,uBAAuB;QAC3B,cAAc;IAChB;IAEA,qBACE;;YACG,4BAAc,6LAAC,2IAAA,CAAA,UAAY;gBAAC,YAAY;;;;;;YAExC,CAAC,4BACA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gJAAA,CAAA,UAAW;;;;;kCACZ,6LAAC,iJAAA,CAAA,UAAY;;;;;kCACb,6LAAC,qJAAA,CAAA,UAAgB;;;;;;;;;;;;;AAK3B;GApBwB;KAAA", "debugId": null}}]}