{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Home, User, Briefcase, FileText, Mail, Sun, Moon } from 'lucide-react';\nimport { useTheme } from 'next-themes';\n\nconst navItems = [\n  { href: '/', label: 'Home', icon: Home },\n  { href: '/about', label: 'About', icon: User },\n  { href: '/projects', label: 'Projects', icon: Briefcase },\n  { href: '/resume', label: 'Resume', icon: FileText },\n  { href: '/contact', label: 'Contact', icon: Mail },\n];\n\nexport default function Navigation() {\n  const [mounted, setMounted] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const pathname = usePathname();\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) return null;\n\n  return (\n    <motion.nav\n      className=\"fixed top-0 left-0 right-0 z-40 glass-strong\"\n      initial={{ y: -100, opacity: 0 }}\n      animate={{ y: 0, opacity: 1 }}\n      transition={{ delay: 3, duration: 0.5 }}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo/Name */}\n          <motion.div\n            layoutId=\"main-name\"\n            className=\"text-xl font-bold gradient-text font-orbitron\"\n          >\n            Saurabh Dahariya\n          </motion.div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              \n              return (\n                <Link key={item.href} href={item.href}>\n                  <motion.div\n                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${\n                      isActive\n                        ? 'text-neon-blue glow-text'\n                        : 'text-gray-300 hover:text-neon-green'\n                    }`}\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    <Icon size={18} />\n                    <span className=\"font-medium\">{item.label}</span>\n                  </motion.div>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Theme Toggle */}\n          <motion.button\n            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\n            className=\"p-2 rounded-lg glass text-neon-blue hover:text-neon-green transition-colors\"\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n          >\n            {theme === 'dark' ? <Sun size={20} /> : <Moon size={20} />}\n          </motion.button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <div className=\"md:hidden\">\n        <div className=\"px-2 pt-2 pb-3 space-y-1\">\n          {navItems.map((item) => {\n            const Icon = item.icon;\n            const isActive = pathname === item.href;\n            \n            return (\n              <Link key={item.href} href={item.href}>\n                <motion.div\n                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${\n                    isActive\n                      ? 'text-neon-blue glow-text bg-gray-800'\n                      : 'text-gray-300 hover:text-neon-green hover:bg-gray-800'\n                  }`}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <Icon size={18} />\n                  <span className=\"font-medium\">{item.label}</span>\n                </motion.div>\n              </Link>\n            );\n          })}\n        </div>\n      </div>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAPA;;;;;;;;AASA,MAAM,WAAW;IACf;QAAE,MAAM;QAAK,OAAO;QAAQ,MAAM,mMAAA,CAAA,OAAI;IAAC;IACvC;QAAE,MAAM;QAAU,OAAO;QAAS,MAAM,kMAAA,CAAA,OAAI;IAAC;IAC7C;QAAE,MAAM;QAAa,OAAO;QAAY,MAAM,4MAAA,CAAA,YAAS;IAAC;IACxD;QAAE,MAAM;QAAW,OAAO;QAAU,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACnD;QAAE,MAAM;QAAY,OAAO;QAAW,MAAM,kMAAA,CAAA,OAAI;IAAC;CAClD;AAEc,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS,OAAO;IAErB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC/B,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,YAAY;YAAE,OAAO;YAAG,UAAU;QAAI;;0BAEtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAS;4BACT,WAAU;sCACX;;;;;;sCAKD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAAiB,MAAM,KAAK,IAAI;8CACnC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAW,CAAC,mEAAmE,EAC7E,WACI,6BACA,uCACJ;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC;gDAAK,MAAM;;;;;;0DACZ,8OAAC;gDAAK,WAAU;0DAAe,KAAK,KAAK;;;;;;;;;;;;mCAXlC,KAAK,IAAI;;;;;4BAexB;;;;;;sCAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;4BACrD,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAI;sCAEtB,UAAU,uBAAS,8OAAC,gMAAA,CAAA,MAAG;gCAAC,MAAM;;;;;qDAAS,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;0BAM1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wBAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAAiB,MAAM,KAAK,IAAI;sCACnC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAW,CAAC,mEAAmE,EAC7E,WACI,yCACA,yDACJ;gCACF,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,8OAAC;wCAAK,MAAM;;;;;;kDACZ,8OAAC;wCAAK,WAAU;kDAAe,KAAK,KAAK;;;;;;;;;;;;2BAXlC,KAAK,IAAI;;;;;oBAexB;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/CustomCursor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\n\nexport default function CustomCursor() {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n\n  useEffect(() => {\n    const updateMousePosition = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n    };\n\n    const handleMouseEnter = () => setIsHovering(true);\n    const handleMouseLeave = () => setIsHovering(false);\n\n    // Add event listeners for mouse movement\n    window.addEventListener('mousemove', updateMousePosition);\n\n    // Add hover detection for interactive elements\n    const interactiveElements = document.querySelectorAll('button, a, [role=\"button\"]');\n    interactiveElements.forEach(el => {\n      el.addEventListener('mouseenter', handleMouseEnter);\n      el.addEventListener('mouseleave', handleMouseLeave);\n    });\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition);\n      interactiveElements.forEach(el => {\n        el.removeEventListener('mouseenter', handleMouseEnter);\n        el.removeEventListener('mouseleave', handleMouseLeave);\n      });\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Main cursor */}\n      <motion.div\n        className=\"custom-cursor\"\n        style={{\n          left: mousePosition.x - 10,\n          top: mousePosition.y - 10,\n        }}\n        animate={{\n          scale: isHovering ? 1.5 : 1,\n          backgroundColor: isHovering ? '#00ff88' : '#00d4ff',\n        }}\n        transition={{\n          type: 'spring',\n          stiffness: 500,\n          damping: 28,\n        }}\n      />\n      \n      {/* Cursor trail */}\n      <motion.div\n        className=\"custom-cursor-trail\"\n        style={{\n          left: mousePosition.x - 4,\n          top: mousePosition.y - 4,\n        }}\n        animate={{\n          scale: isHovering ? 2 : 1,\n          opacity: isHovering ? 0.8 : 0.6,\n        }}\n        transition={{\n          type: 'spring',\n          stiffness: 300,\n          damping: 30,\n          delay: 0.05,\n        }}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB,CAAC;YAC3B,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,MAAM,mBAAmB,IAAM,cAAc;QAC7C,MAAM,mBAAmB,IAAM,cAAc;QAE7C,yCAAyC;QACzC,OAAO,gBAAgB,CAAC,aAAa;QAErC,+CAA+C;QAC/C,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;QACtD,oBAAoB,OAAO,CAAC,CAAA;YAC1B,GAAG,gBAAgB,CAAC,cAAc;YAClC,GAAG,gBAAgB,CAAC,cAAc;QACpC;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,aAAa;YACxC,oBAAoB,OAAO,CAAC,CAAA;gBAC1B,GAAG,mBAAmB,CAAC,cAAc;gBACrC,GAAG,mBAAmB,CAAC,cAAc;YACvC;QACF;IACF,GAAG,EAAE;IAEL,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,MAAM,cAAc,CAAC,GAAG;oBACxB,KAAK,cAAc,CAAC,GAAG;gBACzB;gBACA,SAAS;oBACP,OAAO,aAAa,MAAM;oBAC1B,iBAAiB,aAAa,YAAY;gBAC5C;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;0BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,MAAM,cAAc,CAAC,GAAG;oBACxB,KAAK,cAAc,CAAC,GAAG;gBACzB;gBACA,SAAS;oBACP,OAAO,aAAa,IAAI;oBACxB,SAAS,aAAa,MAAM;gBAC9B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,OAAO;gBACT;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/AIAssistant.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Mic, MicOff, Send, Bot, X } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\n\ninterface Message {\n  id: string;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n}\n\nexport default function AIAssistant() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isListening, setIsListening] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const router = useRouter();\n\n  const addMessage = (text: string, isUser: boolean) => {\n    const newMessage: Message = {\n      id: Date.now().toString(),\n      text,\n      isUser,\n      timestamp: new Date(),\n    };\n    setMessages(prev => [...prev, newMessage]);\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputText.trim()) return;\n\n    const userMessage = inputText.trim();\n    setInputText('');\n    addMessage(userMessage, true);\n    setIsLoading(true);\n\n    try {\n      // Simulate AI response and navigation logic\n      const response = await processAIQuery(userMessage);\n      addMessage(response.text, false);\n      \n      if (response.navigate) {\n        setTimeout(() => {\n          router.push(response.navigate);\n        }, 1000);\n      }\n    } catch (error) {\n      addMessage('Sorry, I encountered an error. Please try again.', false);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const processAIQuery = async (query: string): Promise<{ text: string; navigate?: string }> => {\n    try {\n      const response = await fetch('/api/ai-chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ message: query }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to get AI response');\n      }\n\n      const data = await response.json();\n      return {\n        text: data.response,\n        navigate: data.navigate,\n      };\n    } catch (error) {\n      console.error('AI query error:', error);\n      return {\n        text: \"I'm sorry, I'm having trouble processing your request right now. Please try again or feel free to explore the site manually!\",\n      };\n    }\n  };\n\n  const toggleListening = () => {\n    setIsListening(!isListening);\n    // Voice recognition would be implemented here\n  };\n\n  useEffect(() => {\n    if (isOpen && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isOpen]);\n\n  return (\n    <>\n      {/* AI Button - Always visible */}\n      <motion.div\n        className=\"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50\"\n        initial={{ y: 100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ delay: 2, duration: 0.5 }}\n      >\n        <motion.button\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"glass-strong rounded-full p-4 text-neon-blue hover:text-neon-green transition-colors duration-300 glow-box\"\n          whileHover={{ scale: 1.1 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <Bot size={24} />\n        </motion.button>\n      </motion.div>\n\n      {/* AI Chat Interface */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            className=\"fixed bottom-20 left-1/2 transform -translate-x-1/2 w-96 max-w-[90vw] z-40\"\n            initial={{ y: 50, opacity: 0, scale: 0.9 }}\n            animate={{ y: 0, opacity: 1, scale: 1 }}\n            exit={{ y: 50, opacity: 0, scale: 0.9 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"glass-strong rounded-2xl p-4 max-h-96 flex flex-col\">\n              {/* Header */}\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-neon-blue\">AI Assistant</h3>\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                >\n                  <X size={20} />\n                </button>\n              </div>\n\n              {/* Messages */}\n              <div className=\"flex-1 overflow-y-auto mb-4 space-y-3 max-h-48\">\n                {messages.length === 0 && (\n                  <div className=\"text-gray-400 text-sm text-center py-4\">\n                    Ask me about Saurabh's projects, skills, or experience!\n                  </div>\n                )}\n                {messages.map((message) => (\n                  <div\n                    key={message.id}\n                    className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\n                  >\n                    <div\n                      className={`max-w-[80%] p-3 rounded-lg text-sm ${\n                        message.isUser\n                          ? 'bg-neon-blue text-black'\n                          : 'bg-gray-800 text-white'\n                      }`}\n                    >\n                      {message.text}\n                    </div>\n                  </div>\n                ))}\n                {isLoading && (\n                  <div className=\"flex justify-start\">\n                    <div className=\"bg-gray-800 text-white p-3 rounded-lg text-sm\">\n                      <div className=\"flex space-x-1\">\n                        <div className=\"w-2 h-2 bg-neon-blue rounded-full animate-bounce\"></div>\n                        <div className=\"w-2 h-2 bg-neon-blue rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                        <div className=\"w-2 h-2 bg-neon-blue rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Input */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"flex-1 relative\">\n                  <input\n                    ref={inputRef}\n                    type=\"text\"\n                    value={inputText}\n                    onChange={(e) => setInputText(e.target.value)}\n                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\n                    placeholder=\"Ask me anything...\"\n                    className=\"w-full bg-gray-800 text-white rounded-lg px-4 py-2 pr-12 focus:outline-none focus:ring-2 focus:ring-neon-blue\"\n                  />\n                  <button\n                    onClick={handleSendMessage}\n                    disabled={!inputText.trim() || isLoading}\n                    className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-neon-blue hover:text-neon-green disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    <Send size={16} />\n                  </button>\n                </div>\n                <button\n                  onClick={toggleListening}\n                  className={`p-2 rounded-lg transition-colors ${\n                    isListening\n                      ? 'bg-red-500 text-white'\n                      : 'bg-gray-800 text-neon-blue hover:text-neon-green'\n                  }`}\n                >\n                  {isListening ? <MicOff size={16} /> : <Mic size={16} />}\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAC,MAAc;QAChC,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA;YACA,WAAW,IAAI;QACjB;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;IAC3C;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU,IAAI,IAAI;QAEvB,MAAM,cAAc,UAAU,IAAI;QAClC,aAAa;QACb,WAAW,aAAa;QACxB,aAAa;QAEb,IAAI;YACF,4CAA4C;YAC5C,MAAM,WAAW,MAAM,eAAe;YACtC,WAAW,SAAS,IAAI,EAAE;YAE1B,IAAI,SAAS,QAAQ,EAAE;gBACrB,WAAW;oBACT,OAAO,IAAI,CAAC,SAAS,QAAQ;gBAC/B,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,WAAW,oDAAoD;QACjE,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAM;YACxC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,MAAM,KAAK,QAAQ;gBACnB,UAAU,KAAK,QAAQ;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBACL,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,eAAe,CAAC;IAChB,8CAA8C;IAChD;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,SAAS,OAAO,EAAE;YAC9B,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;KAAO;IAEX,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;oBAAK,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;oBAAG,UAAU;gBAAI;0BAEtC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS,IAAM,UAAU,CAAC;oBAC1B,WAAU;oBACV,YAAY;wBAAE,OAAO;oBAAI;oBACzB,UAAU;wBAAE,OAAO;oBAAK;8BAExB,cAAA,8OAAC,gMAAA,CAAA,MAAG;wBAAC,MAAM;;;;;;;;;;;;;;;;0BAKf,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,GAAG;wBAAI,SAAS;wBAAG,OAAO;oBAAI;oBACzC,SAAS;wBAAE,GAAG;wBAAG,SAAS;wBAAG,OAAO;oBAAE;oBACtC,MAAM;wBAAE,GAAG;wBAAI,SAAS;wBAAG,OAAO;oBAAI;oBACtC,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAKb,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,MAAM,KAAK,mBACnB,8OAAC;wCAAI,WAAU;kDAAyC;;;;;;oCAIzD,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;sDAErE,cAAA,8OAAC;gDACC,WAAW,CAAC,mCAAmC,EAC7C,QAAQ,MAAM,GACV,4BACA,0BACJ;0DAED,QAAQ,IAAI;;;;;;2CAVV,QAAQ,EAAE;;;;;oCAclB,2BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;wDAAmD,OAAO;4DAAE,gBAAgB;wDAAO;;;;;;kEAClG,8OAAC;wDAAI,WAAU;wDAAmD,OAAO;4DAAE,gBAAgB;wDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ5G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK;gDACL,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC5C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;gDACxC,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDACC,SAAS;gDACT,UAAU,CAAC,UAAU,IAAI,MAAM;gDAC/B,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;;;;;;;;;;;;kDAGhB,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,iCAAiC,EAC3C,cACI,0BACA,oDACJ;kDAED,4BAAc,8OAAC,0MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;iEAAS,8OAAC,gMAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/ParticleBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { useCallback } from 'react';\nimport Particles from '@tsparticles/react';\nimport { loadSlim } from '@tsparticles/slim';\nimport type { Engine } from '@tsparticles/engine';\n\nexport default function ParticleBackground() {\n  const particlesInit = useCallback(async (engine: Engine) => {\n    await loadSlim(engine);\n  }, []);\n\n  return (\n    <Particles\n      id=\"tsparticles\"\n      init={particlesInit}\n      options={{\n        background: {\n          color: {\n            value: 'transparent',\n          },\n        },\n        fpsLimit: 120,\n        interactivity: {\n          events: {\n            onClick: {\n              enable: true,\n              mode: 'push',\n            },\n            onHover: {\n              enable: true,\n              mode: 'repulse',\n            },\n            resize: true,\n          },\n          modes: {\n            push: {\n              quantity: 4,\n            },\n            repulse: {\n              distance: 200,\n              duration: 0.4,\n            },\n          },\n        },\n        particles: {\n          color: {\n            value: ['#00d4ff', '#00ff88', '#8b5cf6'],\n          },\n          links: {\n            color: '#00d4ff',\n            distance: 150,\n            enable: true,\n            opacity: 0.3,\n            width: 1,\n          },\n          move: {\n            direction: 'none',\n            enable: true,\n            outModes: {\n              default: 'bounce',\n            },\n            random: false,\n            speed: 1,\n            straight: false,\n          },\n          number: {\n            density: {\n              enable: true,\n              area: 800,\n            },\n            value: 80,\n          },\n          opacity: {\n            value: 0.5,\n            animation: {\n              enable: true,\n              speed: 1,\n              minimumValue: 0.1,\n            },\n          },\n          shape: {\n            type: 'circle',\n          },\n          size: {\n            value: { min: 1, max: 5 },\n            animation: {\n              enable: true,\n              speed: 2,\n              minimumValue: 0.1,\n            },\n          },\n        },\n        detectRetina: true,\n      }}\n      className=\"absolute inset-0 -z-10\"\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAOe,SAAS;IACtB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACvC,MAAM,CAAA,GAAA,qJAAA,CAAA,WAAQ,AAAD,EAAE;IACjB,GAAG,EAAE;IAEL,qBACE,8OAAC,2JAAA,CAAA,UAAS;QACR,IAAG;QACH,MAAM;QACN,SAAS;YACP,YAAY;gBACV,OAAO;oBACL,OAAO;gBACT;YACF;YACA,UAAU;YACV,eAAe;gBACb,QAAQ;oBACN,SAAS;wBACP,QAAQ;wBACR,MAAM;oBACR;oBACA,SAAS;wBACP,QAAQ;wBACR,MAAM;oBACR;oBACA,QAAQ;gBACV;gBACA,OAAO;oBACL,MAAM;wBACJ,UAAU;oBACZ;oBACA,SAAS;wBACP,UAAU;wBACV,UAAU;oBACZ;gBACF;YACF;YACA,WAAW;gBACT,OAAO;oBACL,OAAO;wBAAC;wBAAW;wBAAW;qBAAU;gBAC1C;gBACA,OAAO;oBACL,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,OAAO;gBACT;gBACA,MAAM;oBACJ,WAAW;oBACX,QAAQ;oBACR,UAAU;wBACR,SAAS;oBACX;oBACA,QAAQ;oBACR,OAAO;oBACP,UAAU;gBACZ;gBACA,QAAQ;oBACN,SAAS;wBACP,QAAQ;wBACR,MAAM;oBACR;oBACA,OAAO;gBACT;gBACA,SAAS;oBACP,OAAO;oBACP,WAAW;wBACT,QAAQ;wBACR,OAAO;wBACP,cAAc;oBAChB;gBACF;gBACA,OAAO;oBACL,MAAM;gBACR;gBACA,MAAM;oBACJ,OAAO;wBAAE,KAAK;wBAAG,KAAK;oBAAE;oBACxB,WAAW;wBACT,QAAQ;wBACR,OAAO;wBACP,cAAc;oBAChB;gBACF;YACF;YACA,cAAc;QAChB;QACA,WAAU;;;;;;AAGhB", "debugId": null}}]}