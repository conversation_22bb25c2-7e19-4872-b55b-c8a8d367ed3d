/*! For license information please see tsparticles.updater.color.min.js.LICENSE.txt */
!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],o);else{var t="object"==typeof exports?o(require("@tsparticles/engine")):o(e.window);for(var r in t)("object"==typeof exports?exports:e)[r]=t[r]}}(this,(e=>(()=>{var o={303:o=>{o.exports=e}},t={};function r(e){var n=t[e];if(void 0!==n)return n.exports;var i=t[e]={exports:{}};return o[e](i,i.exports,r),i.exports}r.d=(e,o)=>{for(var t in o)r.o(o,t)&&!r.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},r.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};r.r(n),r.d(n,{loadColorUpdater:()=>s});var i=r(303);class a{constructor(e,o){this._container=e,this._engine=o}init(e){const o=(0,i.rangeColorToHsl)(this._engine,e.options.color,e.id,e.options.reduceDuplicates);o&&(e.color=(0,i.getHslAnimationFromHsl)(o,e.options.color.animation,this._container.retina.reduceFactor))}isEnabled(e){const{h:o,s:t,l:r}=e.options.color.animation,{color:n}=e;return!e.destroyed&&!e.spawning&&(void 0!==n?.h.value&&o.enable||void 0!==n?.s.value&&t.enable||void 0!==n?.l.value&&r.enable)}update(e,o){(0,i.updateColor)(e.color,o)}}async function s(e,o=!0){e.checkVersion("3.8.1"),await e.addParticleUpdater("color",(o=>Promise.resolve(new a(o,e))),o)}return n})()));