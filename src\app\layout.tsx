import type { Metada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from 'next-themes';
import Navigation from '@/components/ui/Navigation';
import AIAssistant from '@/components/ui/AIAssistant';

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON><PERSON>ya - Full Stack Developer",
  description: "AI-powered portfolio of <PERSON><PERSON><PERSON><PERSON>, a passionate Full Stack Developer from Bengaluru specializing in React, Node.js, and modern web technologies.",
  keywords: "<PERSON><PERSON><PERSON><PERSON> Dahariya, Full Stack Developer, React, Node.js, Portfolio, Web Developer, Bengaluru",
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>hari<PERSON>" }],
  openGraph: {
    title: "<PERSON><PERSON><PERSON><PERSON> Dahariya - Full Stack Developer",
    description: "AI-powered portfolio showcasing modern web development projects and skills",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased bg-black text-white">
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
          <div className="relative min-h-screen">
            {/* Subtle background */}
            <div className="fixed inset-0 bg-gradient-to-br from-black via-gray-900 to-black -z-20" />
            <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-transparent to-transparent -z-10" />


            <Navigation />
            <main className="relative z-10">
              {children}
            </main>
            <AIAssistant />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
