import type { Metadata } from "next";
import { ThemeProvider } from 'next-themes';
import "./globals.css";
import Navigation from '@/components/ui/Navigation';
import CustomCursor from '@/components/ui/CustomCursor';
import AIAssistant from '@/components/ui/AIAssistant';
import Van<PERSON>Background from '@/components/ui/VantaBackground';

export const metadata: Metadata = {
  title: "<PERSON>ura<PERSON>h Dahariya - Full Stack Developer",
  description: "AI-powered portfolio of <PERSON><PERSON><PERSON><PERSON>, a passionate Full Stack Developer from Bengaluru specializing in React, Node.js, and modern web technologies.",
  keywords: "<PERSON><PERSON><PERSON><PERSON>, Full Stack Developer, React, Node.js, Portfolio, Web Developer, Bengaluru",
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>" }],
  openGraph: {
    title: "<PERSON><PERSON><PERSON><PERSON> Dahariya - Full Stack Developer",
    description: "AI-powered portfolio showcasing modern web development projects and skills",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased">
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
          <div className="relative min-h-screen">
            <VantaBackground effect="net" />
            <CustomCursor />
            <Navigation />
            <main className="relative z-10">
              {children}
            </main>
            <AIAssistant />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
