'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mic, MicOff, Send, Bot, X } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export default function AIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [inputText, setInputText] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const addMessage = (text: string, isUser: boolean) => {
    const newMessage: Message = {
      id: Date.now().toString(),
      text,
      isUser,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleSendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = inputText.trim();
    setInputText('');
    addMessage(userMessage, true);
    setIsLoading(true);

    try {
      // Simulate AI response and navigation logic
      const response = await processAIQuery(userMessage);
      addMessage(response.text, false);
      
      if (response.navigate) {
        setTimeout(() => {
          router.push(response.navigate);
        }, 1000);
      }
    } catch (error) {
      addMessage('Sorry, I encountered an error. Please try again.', false);
    } finally {
      setIsLoading(false);
    }
  };

  const processAIQuery = async (query: string): Promise<{ text: string; navigate?: string }> => {
    try {
      const response = await fetch('/api/ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ message: query }),
      });

      if (!response.ok) {
        throw new Error('Failed to get AI response');
      }

      const data = await response.json();
      return {
        text: data.response,
        navigate: data.navigate,
      };
    } catch (error) {
      console.error('AI query error:', error);
      return {
        text: "I'm sorry, I'm having trouble processing your request right now. Please try again or feel free to explore the site manually!",
      };
    }
  };

  const toggleListening = () => {
    setIsListening(!isListening);
    // Voice recognition would be implemented here
  };

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  return (
    <>
      {/* AI Assistant Bar - Always visible at bottom center */}
      <motion.div
        className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-[9999]"
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 8, duration: 0.8 }}
      >
        {!isOpen ? (
          // Collapsed state - floating chat bar
          <motion.div
            className="backdrop-blur-lg bg-white/10 text-white rounded-full shadow-2xl px-6 py-3 flex gap-3 items-center cursor-pointer border border-white/20"
            onClick={() => setIsOpen(true)}
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              animate={isListening ? { scale: [1, 1.2, 1] } : {}}
              transition={{ duration: 1, repeat: isListening ? Infinity : 0 }}
            >
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleListening();
                }}
                className={`p-2 rounded-full transition-colors ${
                  isListening
                    ? 'bg-red-500 text-white'
                    : 'text-neon-blue hover:text-neon-green'
                }`}
              >
                {isListening ? <MicOff size={16} /> : <Mic size={16} />}
              </button>
            </motion.div>

            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Ask me anything..."
              className="bg-transparent border-none outline-none text-white placeholder-gray-300 w-64"
              onClick={(e) => e.stopPropagation()}
            />

            <button
              onClick={(e) => {
                e.stopPropagation();
                handleSendMessage();
              }}
              disabled={!inputText.trim() || isLoading}
              className="text-neon-blue hover:text-neon-green disabled:opacity-50 disabled:cursor-not-allowed p-1"
            >
              <Send size={16} />
            </button>
          </motion.div>
        ) : (
          // Expanded state - full chat interface
          <motion.div
            className="backdrop-blur-lg bg-white/10 text-white rounded-2xl shadow-2xl border border-white/20 w-96 max-w-[90vw]"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-white/20">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-r from-neon-blue to-neon-green rounded-full flex items-center justify-center">
                  <Bot size={16} className="text-black" />
                </div>
                <div>
                  <h3 className="font-semibold">AI Assistant</h3>
                  <p className="text-xs text-gray-300">Ask about Saurabh's work</p>
                </div>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-white transition-colors p-1"
              >
                <X size={20} />
              </button>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-3 max-h-64 min-h-[200px]">
              {messages.length === 0 && (
                <div className="text-gray-300 text-sm text-center py-8">
                  <Bot className="mx-auto mb-2 text-neon-blue" size={32} />
                  <p>Hi! I'm Saurabh's AI assistant.</p>
                  <p className="text-xs mt-1">Ask me about his projects, skills, or experience!</p>
                </div>
              )}
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div
                    className={`max-w-[85%] p-3 rounded-2xl text-sm ${
                      message.isUser
                        ? 'bg-gradient-to-r from-neon-blue to-neon-green text-black font-medium'
                        : 'bg-white/10 text-white border border-white/20'
                    }`}
                  >
                    {message.text}
                  </div>
                </motion.div>
              ))}
              {isLoading && (
                <motion.div
                  className="flex justify-start"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                >
                  <div className="bg-white/10 border border-white/20 text-white p-3 rounded-2xl text-sm">
                    <div className="flex space-x-1">
                      <motion.div
                        className="w-2 h-2 bg-neon-blue rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity, delay: 0 }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-neon-green rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}
                      />
                      <motion.div
                        className="w-2 h-2 bg-neon-purple rounded-full"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Input Area */}
            <div className="p-4 border-t border-white/20">
              <div className="flex items-center space-x-3">
                <motion.button
                  onClick={toggleListening}
                  className={`p-2 rounded-full transition-colors ${
                    isListening
                      ? 'bg-red-500 text-white'
                      : 'text-neon-blue hover:text-neon-green'
                  }`}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  animate={isListening ? { scale: [1, 1.1, 1] } : {}}
                  transition={{ duration: 1, repeat: isListening ? Infinity : 0 }}
                >
                  {isListening ? <MicOff size={16} /> : <Mic size={16} />}
                </motion.button>

                <div className="flex-1 relative">
                  <input
                    ref={inputRef}
                    type="text"
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Ask me anything..."
                    className="w-full bg-white/10 border border-white/20 text-white rounded-full px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-neon-blue placeholder-gray-300"
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!inputText.trim() || isLoading}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 text-neon-blue hover:text-neon-green disabled:opacity-50 disabled:cursor-not-allowed p-1"
                  >
                    <Send size={14} />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>
    </>
  );
}
