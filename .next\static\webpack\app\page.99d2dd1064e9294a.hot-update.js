"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nfunction AboutSection() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const ctx = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.context({\n                \"AboutSection.useEffect.ctx\": ()=>{\n                    // Animate cards on scroll\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo('.about-card', {\n                        opacity: 0,\n                        y: 50,\n                        scale: 0.9\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1,\n                        duration: 0.8,\n                        stagger: 0.2,\n                        ease: 'power2.out',\n                        scrollTrigger: {\n                            trigger: '.about-cards',\n                            start: 'top 80%',\n                            end: 'bottom 20%',\n                            toggleActions: 'play none none reverse'\n                        }\n                    });\n                    // Animate text elements\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo('.about-text', {\n                        opacity: 0,\n                        x: -50\n                    }, {\n                        opacity: 1,\n                        x: 0,\n                        duration: 1,\n                        ease: 'power2.out',\n                        scrollTrigger: {\n                            trigger: '.about-content',\n                            start: 'top 80%',\n                            toggleActions: 'play none none reverse'\n                        }\n                    });\n                }\n            }[\"AboutSection.useEffect.ctx\"], sectionRef);\n            return ({\n                \"AboutSection.useEffect\": ()=>ctx.revert()\n            })[\"AboutSection.useEffect\"];\n        }\n    }[\"AboutSection.useEffect\"], []);\n    const personalInfo = [\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: 'Location',\n            value: 'Bengaluru, India'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'Phone',\n            value: '+91 8319130513'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'Email',\n            value: '<EMAIL>'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Education',\n            value: 'B.Tech (IT) - Bhilai Institute Of Technology'\n        }\n    ];\n    const highlights = [\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: 'Full Stack Development',\n            description: 'Expertise in React.js, Node.js, Express.js, and MongoDB for building complete web applications.'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: 'Passionate Learner',\n            description: 'Always eager to learn new technologies and stay updated with the latest industry trends.'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: 'Fresh Graduate',\n            description: 'Recent B.Tech IT graduate with hands-on experience through projects and training.'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        ref: sectionRef,\n        className: \"min-h-screen py-20 px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6\",\n                            children: \"About Me\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                            children: \"Get to know more about my background, skills, and passion for technology\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center about-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"about-text\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-3xl font-bold text-neon-blue mb-6 font-space\",\n                                    children: \"Hello! I'm Saurabh Dahariya\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 mb-8 leading-relaxed\",\n                                    children: \"I'm a passionate Full Stack Developer with a strong foundation in modern web technologies. As a recent B.Tech IT graduate from Bhilai Institute Of Technology, I bring fresh perspectives and enthusiasm to every project I work on.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 mb-8 leading-relaxed\",\n                                    children: \"My journey in web development started during my college years, and I've been continuously learning and building projects to enhance my skills. I'm particularly passionate about creating responsive, user-friendly applications that solve real-world problems.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid sm:grid-cols-2 gap-4\",\n                                    children: personalInfo.map((info, index)=>{\n                                        const Icon = info.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            className: \"glass p-4 rounded-lg hover:glow-box transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"text-neon-blue\",\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: info.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: info.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"about-cards\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-neon-green mb-8 font-space\",\n                                    children: \"What Makes Me Unique\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: highlights.map((highlight, index)=>{\n                                        const Icon = highlight.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            className: \"about-card glass-strong p-6 rounded-xl hover:glow-box transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.02,\n                                                y: -5\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-gradient-to-r from-neon-blue to-neon-green rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"text-black\",\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                                children: highlight.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 leading-relaxed\",\n                                                                children: highlight.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"mt-16 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-strong p-8 rounded-2xl max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-neon-purple mb-4 font-space\",\n                                children: \"Current Training\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-300 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"MERN Stack Development\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" at JSpider BTM Layout, Bengaluru\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"September 2024 - February 2025\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mt-4\",\n                                children: \"Intensive training program focusing on MongoDB, Express.js, React.js, and Node.js to build full-stack web applications.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutSection, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = AboutSection;\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/AboutSection.tsx\n"));

/***/ })

});