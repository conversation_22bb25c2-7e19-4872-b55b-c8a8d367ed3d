/*! For license information please see tsparticles.slim.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/basic"),require("@tsparticles/plugin-easing-quad"),require("@tsparticles/shape-emoji"),require("@tsparticles/interaction-external-attract"),require("@tsparticles/interaction-external-bounce"),require("@tsparticles/interaction-external-bubble"),require("@tsparticles/interaction-external-connect"),require("@tsparticles/interaction-external-grab"),require("@tsparticles/interaction-external-pause"),require("@tsparticles/interaction-external-push"),require("@tsparticles/interaction-external-remove"),require("@tsparticles/interaction-external-repulse"),require("@tsparticles/interaction-external-slow"),require("@tsparticles/shape-image"),require("@tsparticles/updater-life"),require("@tsparticles/shape-line"),require("@tsparticles/move-parallax"),require("@tsparticles/interaction-particles-attract"),require("@tsparticles/interaction-particles-collisions"),require("@tsparticles/interaction-particles-links"),require("@tsparticles/shape-polygon"),require("@tsparticles/updater-rotate"),require("@tsparticles/shape-square"),require("@tsparticles/shape-star"),require("@tsparticles/updater-stroke-color"));else if("function"==typeof define&&define.amd)define(["@tsparticles/basic","@tsparticles/plugin-easing-quad","@tsparticles/shape-emoji","@tsparticles/interaction-external-attract","@tsparticles/interaction-external-bounce","@tsparticles/interaction-external-bubble","@tsparticles/interaction-external-connect","@tsparticles/interaction-external-grab","@tsparticles/interaction-external-pause","@tsparticles/interaction-external-push","@tsparticles/interaction-external-remove","@tsparticles/interaction-external-repulse","@tsparticles/interaction-external-slow","@tsparticles/shape-image","@tsparticles/updater-life","@tsparticles/shape-line","@tsparticles/move-parallax","@tsparticles/interaction-particles-attract","@tsparticles/interaction-particles-collisions","@tsparticles/interaction-particles-links","@tsparticles/shape-polygon","@tsparticles/updater-rotate","@tsparticles/shape-square","@tsparticles/shape-star","@tsparticles/updater-stroke-color"],t);else{var r="object"==typeof exports?t(require("@tsparticles/basic"),require("@tsparticles/plugin-easing-quad"),require("@tsparticles/shape-emoji"),require("@tsparticles/interaction-external-attract"),require("@tsparticles/interaction-external-bounce"),require("@tsparticles/interaction-external-bubble"),require("@tsparticles/interaction-external-connect"),require("@tsparticles/interaction-external-grab"),require("@tsparticles/interaction-external-pause"),require("@tsparticles/interaction-external-push"),require("@tsparticles/interaction-external-remove"),require("@tsparticles/interaction-external-repulse"),require("@tsparticles/interaction-external-slow"),require("@tsparticles/shape-image"),require("@tsparticles/updater-life"),require("@tsparticles/shape-line"),require("@tsparticles/move-parallax"),require("@tsparticles/interaction-particles-attract"),require("@tsparticles/interaction-particles-collisions"),require("@tsparticles/interaction-particles-links"),require("@tsparticles/shape-polygon"),require("@tsparticles/updater-rotate"),require("@tsparticles/shape-square"),require("@tsparticles/shape-star"),require("@tsparticles/updater-stroke-color")):t(e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window);for(var a in r)("object"==typeof exports?exports:e)[a]=r[a]}}(this,((e,t,r,a,i,s,n,o,l,p,c,u,d,w,x,q,b,h,f,g,m,v,y,S,I)=>(()=>{var j={145:t=>{t.exports=e},653:e=>{e.exports=a},924:e=>{e.exports=i},798:e=>{e.exports=s},240:e=>{e.exports=n},374:e=>{e.exports=o},596:e=>{e.exports=l},184:e=>{e.exports=p},712:e=>{e.exports=c},476:e=>{e.exports=u},657:e=>{e.exports=d},857:e=>{e.exports=h},85:e=>{e.exports=f},613:e=>{e.exports=g},88:e=>{e.exports=b},918:e=>{e.exports=t},109:e=>{e.exports=r},162:e=>{e.exports=w},983:e=>{e.exports=q},73:e=>{e.exports=m},400:e=>{e.exports=y},29:e=>{e.exports=S},75:e=>{e.exports=x},668:e=>{e.exports=v},227:e=>{e.exports=I}},E={};function P(e){var t=E[e];if(void 0!==t)return t.exports;var r=E[e]={exports:{}};return j[e](r,r.exports,P),r.exports}P.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return P.d(t,{a:t}),t},P.d=(e,t)=>{for(var r in t)P.o(t,r)&&!P.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},P.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),P.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var k={};P.r(k),P.d(k,{loadSlim:()=>ee});var O=P(145),M=P(918),_=P(109),B=P(653),C=P(924),L=P(798),R=P(240),U=P(374),A=P(596),T=P(184),G=P(712),Q=P(476),V=P(657),z=P(162),D=P(75),F=P(983),H=P(88),J=P(857),K=P(85),N=P(613),W=P(73),X=P(668),Y=P(400),Z=P(29),$=P(227);async function ee(e,t=!0){e.checkVersion("3.8.1"),await(0,H.loadParallaxMover)(e,!1),await(0,B.loadExternalAttractInteraction)(e,!1),await(0,C.loadExternalBounceInteraction)(e,!1),await(0,L.loadExternalBubbleInteraction)(e,!1),await(0,R.loadExternalConnectInteraction)(e,!1),await(0,U.loadExternalGrabInteraction)(e,!1),await(0,A.loadExternalPauseInteraction)(e,!1),await(0,T.loadExternalPushInteraction)(e,!1),await(0,G.loadExternalRemoveInteraction)(e,!1),await(0,Q.loadExternalRepulseInteraction)(e,!1),await(0,V.loadExternalSlowInteraction)(e,!1),await(0,J.loadParticlesAttractInteraction)(e,!1),await(0,K.loadParticlesCollisionsInteraction)(e,!1),await(0,N.loadParticlesLinksInteraction)(e,!1),await(0,M.loadEasingQuadPlugin)(e,!1),await(0,_.loadEmojiShape)(e,!1),await(0,z.loadImageShape)(e,!1),await(0,F.loadLineShape)(e,!1),await(0,W.loadPolygonShape)(e,!1),await(0,Y.loadSquareShape)(e,!1),await(0,Z.loadStarShape)(e,!1),await(0,D.loadLifeUpdater)(e,!1),await(0,X.loadRotateUpdater)(e,!1),await(0,$.loadStrokeColorUpdater)(e,!1),await(0,O.loadBasic)(e,t)}return k})()));