/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/typewriter-effect";
exports.ids = ["vendor-chunks/typewriter-effect"];
exports.modules = {

/***/ "(ssr)/./node_modules/typewriter-effect/dist/react.js":
/*!******************************************************!*\
  !*** ./node_modules/typewriter-effect/dist/react.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("!function(e,t){ true?module.exports=t(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\")):0}(\"undefined\"!=typeof self?self:this,(e=>(()=>{var t={2:(e,t,r)=>{var n=r(2199),o=r(4664),a=r(5950);e.exports=function(e){return n(e,a,o)}},79:(e,t,r)=>{var n=r(3702),o=r(80),a=r(4739),i=r(8655),s=r(1175);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=a,u.prototype.has=i,u.prototype.set=s,e.exports=u},80:(e,t,r)=>{var n=r(6025),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,r=n(t,e);return!(r<0||(r==t.length-1?t.pop():o.call(t,r,1),--this.size,0))}},270:(e,t,r)=>{var n=r(7068),o=r(346);e.exports=function e(t,r,a,i,s){return t===r||(null==t||null==r||!o(t)&&!o(r)?t!=t&&r!=r:n(t,r,a,i,e,s))}},289:(e,t,r)=>{var n=r(2651);e.exports=function(e){return n(this,e).get(e)}},294:e=>{e.exports=function(e){return\"number\"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},317:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e,n){r[++t]=[n,e]})),r}},346:e=>{e.exports=function(e){return null!=e&&\"object\"==typeof e}},361:e=>{var t=/^(?:0|[1-9]\\d*)$/;e.exports=function(e,r){var n=typeof e;return!!(r=null==r?9007199254740991:r)&&(\"number\"==n||\"symbol\"!=n&&t.test(e))&&e>-1&&e%1==0&&e<r}},392:e=>{e.exports=function(e,t){return null==e?void 0:e[t]}},659:(e,t,r)=>{var n=r(1873),o=Object.prototype,a=o.hasOwnProperty,i=o.toString,s=n?n.toStringTag:void 0;e.exports=function(e){var t=a.call(e,s),r=e[s];try{e[s]=void 0;var n=!0}catch(e){}var o=i.call(e);return n&&(t?e[s]=r:delete e[s]),o}},689:(e,t,r)=>{var n=r(2),o=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,a,i,s){var u=1&r,c=n(e),p=c.length;if(p!=n(t).length&&!u)return!1;for(var l=p;l--;){var f=c[l];if(!(u?f in t:o.call(t,f)))return!1}var v=s.get(e),d=s.get(t);if(v&&d)return v==t&&d==e;var h=!0;s.set(e,t),s.set(t,e);for(var y=u;++l<p;){var b=e[f=c[l]],m=t[f];if(a)var _=u?a(m,b,f,t,e,s):a(b,m,f,e,t,s);if(!(void 0===_?b===m||i(b,m,r,a,s):_)){h=!1;break}y||(y=\"constructor\"==f)}if(h&&!y){var g=e.constructor,w=t.constructor;g==w||!(\"constructor\"in e)||!(\"constructor\"in t)||\"function\"==typeof g&&g instanceof g&&\"function\"==typeof w&&w instanceof w||(h=!1)}return s.delete(e),s.delete(t),h}},695:(e,t,r)=>{var n=r(8096),o=r(2428),a=r(6449),i=r(3656),s=r(361),u=r(7167),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var r=a(e),p=!r&&o(e),l=!r&&!p&&i(e),f=!r&&!p&&!l&&u(e),v=r||p||l||f,d=v?n(e.length,String):[],h=d.length;for(var y in e)!t&&!c.call(e,y)||v&&(\"length\"==y||l&&(\"offset\"==y||\"parent\"==y)||f&&(\"buffer\"==y||\"byteLength\"==y||\"byteOffset\"==y)||s(y,h))||d.push(y);return d}},938:e=>{e.exports=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}},945:(e,t,r)=>{var n=r(79),o=r(8223),a=r(3661);e.exports=function(e,t){var r=this.__data__;if(r instanceof n){var i=r.__data__;if(!o||i.length<199)return i.push([e,t]),this.size=++r.size,this;r=this.__data__=new a(i)}return r.set(e,t),this.size=r.size,this}},1042:(e,t,r)=>{var n=r(6110)(Object,\"create\");e.exports=n},1175:(e,t,r)=>{var n=r(6025);e.exports=function(e,t){var r=this.__data__,o=n(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}},1380:e=>{e.exports=function(e){return this.__data__.set(e,\"__lodash_hash_undefined__\"),this}},1420:(e,t,r)=>{var n=r(79);e.exports=function(){this.__data__=new n,this.size=0}},1459:e=>{e.exports=function(e){return this.__data__.has(e)}},1549:(e,t,r)=>{var n=r(2032),o=r(3862),a=r(6721),i=r(2749),s=r(5749);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=a,u.prototype.has=i,u.prototype.set=s,e.exports=u},1873:(e,t,r)=>{var n=r(9325).Symbol;e.exports=n},1882:(e,t,r)=>{var n=r(2552),o=r(3805);e.exports=function(e){if(!o(e))return!1;var t=n(e);return\"[object Function]\"==t||\"[object GeneratorFunction]\"==t||\"[object AsyncFunction]\"==t||\"[object Proxy]\"==t}},1986:(e,t,r)=>{var n=r(1873),o=r(7828),a=r(5288),i=r(5911),s=r(317),u=r(4247),c=n?n.prototype:void 0,p=c?c.valueOf:void 0;e.exports=function(e,t,r,n,c,l,f){switch(r){case\"[object DataView]\":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case\"[object ArrayBuffer]\":return!(e.byteLength!=t.byteLength||!l(new o(e),new o(t)));case\"[object Boolean]\":case\"[object Date]\":case\"[object Number]\":return a(+e,+t);case\"[object Error]\":return e.name==t.name&&e.message==t.message;case\"[object RegExp]\":case\"[object String]\":return e==t+\"\";case\"[object Map]\":var v=s;case\"[object Set]\":var d=1&n;if(v||(v=u),e.size!=t.size&&!d)return!1;var h=f.get(e);if(h)return h==t;n|=2,f.set(e,t);var y=i(v(e),v(t),n,c,l,f);return f.delete(e),y;case\"[object Symbol]\":if(p)return p.call(e)==p.call(t)}return!1}},2032:(e,t,r)=>{var n=r(1042);e.exports=function(){this.__data__=n?n(null):{},this.size=0}},2199:(e,t,r)=>{var n=r(4528),o=r(6449);e.exports=function(e,t,r){var a=t(e);return o(e)?a:n(a,r(e))}},2404:(e,t,r)=>{var n=r(270);e.exports=function(e,t){return n(e,t)}},2428:(e,t,r)=>{var n=r(7534),o=r(346),a=Object.prototype,i=a.hasOwnProperty,s=a.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(e){return o(e)&&i.call(e,\"callee\")&&!s.call(e,\"callee\")};e.exports=u},2552:(e,t,r)=>{var n=r(1873),o=r(659),a=r(9350),i=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?\"[object Undefined]\":\"[object Null]\":i&&i in Object(e)?o(e):a(e)}},2651:(e,t,r)=>{var n=r(4218);e.exports=function(e,t){var r=e.__data__;return n(t)?r[\"string\"==typeof t?\"string\":\"hash\"]:r.map}},2749:(e,t,r)=>{var n=r(1042),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return n?void 0!==t[e]:o.call(t,e)}},2804:(e,t,r)=>{var n=r(6110)(r(9325),\"Promise\");e.exports=n},2949:(e,t,r)=>{var n=r(2651);e.exports=function(e,t){var r=n(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}},3040:(e,t,r)=>{var n=r(1549),o=r(79),a=r(8223);e.exports=function(){this.size=0,this.__data__={hash:new n,map:new(a||o),string:new n}}},3146:(e,t,r)=>{for(var n=r(3491),o=\"undefined\"==typeof window?r.g:window,a=[\"moz\",\"webkit\"],i=\"AnimationFrame\",s=o[\"request\"+i],u=o[\"cancel\"+i]||o[\"cancelRequest\"+i],c=0;!s&&c<a.length;c++)s=o[a[c]+\"Request\"+i],u=o[a[c]+\"Cancel\"+i]||o[a[c]+\"CancelRequest\"+i];if(!s||!u){var p=0,l=0,f=[],v=1e3/60;s=function(e){if(0===f.length){var t=n(),r=Math.max(0,v-(t-p));p=r+t,setTimeout((function(){var e=f.slice(0);f.length=0;for(var t=0;t<e.length;t++)if(!e[t].cancelled)try{e[t].callback(p)}catch(e){setTimeout((function(){throw e}),0)}}),Math.round(r))}return f.push({handle:++l,callback:e,cancelled:!1}),l},u=function(e){for(var t=0;t<f.length;t++)f[t].handle===e&&(f[t].cancelled=!0)}}e.exports=function(e){return s.call(o,e)},e.exports.cancel=function(){u.apply(o,arguments)},e.exports.polyfill=function(e){e||(e=o),e.requestAnimationFrame=s,e.cancelAnimationFrame=u}},3345:e=>{e.exports=function(){return[]}},3491:function(e){(function(){var t,r,n,o,a,i;\"undefined\"!=typeof performance&&null!==performance&&performance.now?e.exports=function(){return performance.now()}:\"undefined\"!=typeof process&&null!==process&&process.hrtime?(e.exports=function(){return(t()-a)/1e6},r=process.hrtime,o=(t=function(){var e;return 1e9*(e=r())[0]+e[1]})(),i=1e9*process.uptime(),a=o-i):Date.now?(e.exports=function(){return Date.now()-n},n=Date.now()):(e.exports=function(){return(new Date).getTime()-n},n=(new Date).getTime())}).call(this)},3605:e=>{e.exports=function(e){return this.__data__.get(e)}},3650:(e,t,r)=>{var n=r(4335)(Object.keys,Object);e.exports=n},3656:(e,t,r)=>{e=r.nmd(e);var n=r(9325),o=r(9935),a=t&&!t.nodeType&&t,i=a&&e&&!e.nodeType&&e,s=i&&i.exports===a?n.Buffer:void 0,u=(s?s.isBuffer:void 0)||o;e.exports=u},3661:(e,t,r)=>{var n=r(3040),o=r(7670),a=r(289),i=r(4509),s=r(2949);function u(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=a,u.prototype.has=i,u.prototype.set=s,e.exports=u},3702:e=>{e.exports=function(){this.__data__=[],this.size=0}},3805:e=>{e.exports=function(e){var t=typeof e;return null!=e&&(\"object\"==t||\"function\"==t)}},3862:e=>{e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},4218:e=>{e.exports=function(e){var t=typeof e;return\"string\"==t||\"number\"==t||\"symbol\"==t||\"boolean\"==t?\"__proto__\"!==e:null===e}},4247:e=>{e.exports=function(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r}},4248:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}},4335:e=>{e.exports=function(e,t){return function(r){return e(t(r))}}},4509:(e,t,r)=>{var n=r(2651);e.exports=function(e){return n(this,e).has(e)}},4528:e=>{e.exports=function(e,t){for(var r=-1,n=t.length,o=e.length;++r<n;)e[o+r]=t[r];return e}},4664:(e,t,r)=>{var n=r(9770),o=r(3345),a=Object.prototype.propertyIsEnumerable,i=Object.getOwnPropertySymbols,s=i?function(e){return null==e?[]:(e=Object(e),n(i(e),(function(t){return a.call(e,t)})))}:o;e.exports=s},4739:(e,t,r)=>{var n=r(6025);e.exports=function(e){var t=this.__data__,r=n(t,e);return r<0?void 0:t[r][1]}},4840:(e,t,r)=>{var n=\"object\"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=n},4894:(e,t,r)=>{var n=r(1882),o=r(294);e.exports=function(e){return null!=e&&o(e.length)&&!n(e)}},4901:(e,t,r)=>{var n=r(2552),o=r(294),a=r(346),i={};i[\"[object Float32Array]\"]=i[\"[object Float64Array]\"]=i[\"[object Int8Array]\"]=i[\"[object Int16Array]\"]=i[\"[object Int32Array]\"]=i[\"[object Uint8Array]\"]=i[\"[object Uint8ClampedArray]\"]=i[\"[object Uint16Array]\"]=i[\"[object Uint32Array]\"]=!0,i[\"[object Arguments]\"]=i[\"[object Array]\"]=i[\"[object ArrayBuffer]\"]=i[\"[object Boolean]\"]=i[\"[object DataView]\"]=i[\"[object Date]\"]=i[\"[object Error]\"]=i[\"[object Function]\"]=i[\"[object Map]\"]=i[\"[object Number]\"]=i[\"[object Object]\"]=i[\"[object RegExp]\"]=i[\"[object Set]\"]=i[\"[object String]\"]=i[\"[object WeakMap]\"]=!1,e.exports=function(e){return a(e)&&o(e.length)&&!!i[n(e)]}},5083:(e,t,r)=>{var n=r(1882),o=r(7296),a=r(3805),i=r(7473),s=/^\\[object .+?Constructor\\]$/,u=Function.prototype,c=Object.prototype,p=u.toString,l=c.hasOwnProperty,f=RegExp(\"^\"+p.call(l).replace(/[\\\\^$.*+?()[\\]{}|]/g,\"\\\\$&\").replace(/hasOwnProperty|(function).*?(?=\\\\\\()| for .+?(?=\\\\\\])/g,\"$1.*?\")+\"$\");e.exports=function(e){return!(!a(e)||o(e))&&(n(e)?f:s).test(i(e))}},5288:e=>{e.exports=function(e,t){return e===t||e!=e&&t!=t}},5481:(e,t,r)=>{var n=r(9325)[\"__core-js_shared__\"];e.exports=n},5527:e=>{var t=Object.prototype;e.exports=function(e){var r=e&&e.constructor;return e===(\"function\"==typeof r&&r.prototype||t)}},5580:(e,t,r)=>{var n=r(6110)(r(9325),\"DataView\");e.exports=n},5749:(e,t,r)=>{var n=r(1042);e.exports=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=n&&void 0===t?\"__lodash_hash_undefined__\":t,this}},5861:(e,t,r)=>{var n=r(5580),o=r(8223),a=r(2804),i=r(6545),s=r(8303),u=r(2552),c=r(7473),p=\"[object Map]\",l=\"[object Promise]\",f=\"[object Set]\",v=\"[object WeakMap]\",d=\"[object DataView]\",h=c(n),y=c(o),b=c(a),m=c(i),_=c(s),g=u;(n&&g(new n(new ArrayBuffer(1)))!=d||o&&g(new o)!=p||a&&g(a.resolve())!=l||i&&g(new i)!=f||s&&g(new s)!=v)&&(g=function(e){var t=u(e),r=\"[object Object]\"==t?e.constructor:void 0,n=r?c(r):\"\";if(n)switch(n){case h:return d;case y:return p;case b:return l;case m:return f;case _:return v}return t}),e.exports=g},5911:(e,t,r)=>{var n=r(8859),o=r(4248),a=r(9219);e.exports=function(e,t,r,i,s,u){var c=1&r,p=e.length,l=t.length;if(p!=l&&!(c&&l>p))return!1;var f=u.get(e),v=u.get(t);if(f&&v)return f==t&&v==e;var d=-1,h=!0,y=2&r?new n:void 0;for(u.set(e,t),u.set(t,e);++d<p;){var b=e[d],m=t[d];if(i)var _=c?i(m,b,d,t,e,u):i(b,m,d,e,t,u);if(void 0!==_){if(_)continue;h=!1;break}if(y){if(!o(t,(function(e,t){if(!a(y,t)&&(b===e||s(b,e,r,i,u)))return y.push(t)}))){h=!1;break}}else if(b!==m&&!s(b,m,r,i,u)){h=!1;break}}return u.delete(e),u.delete(t),h}},5950:(e,t,r)=>{var n=r(695),o=r(8984),a=r(4894);e.exports=function(e){return a(e)?n(e):o(e)}},6009:(e,t,r)=>{e=r.nmd(e);var n=r(4840),o=t&&!t.nodeType&&t,a=o&&e&&!e.nodeType&&e,i=a&&a.exports===o&&n.process,s=function(){try{return a&&a.require&&a.require(\"util\").types||i&&i.binding&&i.binding(\"util\")}catch(e){}}();e.exports=s},6025:(e,t,r)=>{var n=r(5288);e.exports=function(e,t){for(var r=e.length;r--;)if(n(e[r][0],t))return r;return-1}},6110:(e,t,r)=>{var n=r(5083),o=r(392);e.exports=function(e,t){var r=o(e,t);return n(r)?r:void 0}},6449:e=>{var t=Array.isArray;e.exports=t},6545:(e,t,r)=>{var n=r(6110)(r(9325),\"Set\");e.exports=n},6721:(e,t,r)=>{var n=r(1042),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(n){var r=t[e];return\"__lodash_hash_undefined__\"===r?void 0:r}return o.call(t,e)?t[e]:void 0}},7068:(e,t,r)=>{var n=r(7217),o=r(5911),a=r(1986),i=r(689),s=r(5861),u=r(6449),c=r(3656),p=r(7167),l=\"[object Arguments]\",f=\"[object Array]\",v=\"[object Object]\",d=Object.prototype.hasOwnProperty;e.exports=function(e,t,r,h,y,b){var m=u(e),_=u(t),g=m?f:s(e),w=_?f:s(t),x=(g=g==l?v:g)==v,j=(w=w==l?v:w)==v,E=g==w;if(E&&c(e)){if(!c(t))return!1;m=!0,x=!1}if(E&&!x)return b||(b=new n),m||p(e)?o(e,t,r,h,y,b):a(e,t,g,r,h,y,b);if(!(1&r)){var O=x&&d.call(e,\"__wrapped__\"),T=j&&d.call(t,\"__wrapped__\");if(O||T){var A=O?e.value():e,S=T?t.value():t;return b||(b=new n),y(A,S,r,h,b)}}return!!E&&(b||(b=new n),i(e,t,r,h,y,b))}},7167:(e,t,r)=>{var n=r(4901),o=r(7301),a=r(6009),i=a&&a.isTypedArray,s=i?o(i):n;e.exports=s},7217:(e,t,r)=>{var n=r(79),o=r(1420),a=r(938),i=r(3605),s=r(9817),u=r(945);function c(e){var t=this.__data__=new n(e);this.size=t.size}c.prototype.clear=o,c.prototype.delete=a,c.prototype.get=i,c.prototype.has=s,c.prototype.set=u,e.exports=c},7296:(e,t,r)=>{var n,o=r(5481),a=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||\"\"))?\"Symbol(src)_1.\"+n:\"\";e.exports=function(e){return!!a&&a in e}},7301:e=>{e.exports=function(e){return function(t){return e(t)}}},7473:e=>{var t=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return t.call(e)}catch(e){}try{return e+\"\"}catch(e){}}return\"\"}},7534:(e,t,r)=>{var n=r(2552),o=r(346);e.exports=function(e){return o(e)&&\"[object Arguments]\"==n(e)}},7670:(e,t,r)=>{var n=r(2651);e.exports=function(e){var t=n(this,e).delete(e);return this.size-=t?1:0,t}},7828:(e,t,r)=>{var n=r(9325).Uint8Array;e.exports=n},8096:e=>{e.exports=function(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}},8223:(e,t,r)=>{var n=r(6110)(r(9325),\"Map\");e.exports=n},8303:(e,t,r)=>{var n=r(6110)(r(9325),\"WeakMap\");e.exports=n},8655:(e,t,r)=>{var n=r(6025);e.exports=function(e){return n(this.__data__,e)>-1}},8859:(e,t,r)=>{var n=r(3661),o=r(1380),a=r(1459);function i(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new n;++t<r;)this.add(e[t])}i.prototype.add=i.prototype.push=o,i.prototype.has=a,e.exports=i},8984:(e,t,r)=>{var n=r(5527),o=r(3650),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!n(e))return o(e);var t=[];for(var r in Object(e))a.call(e,r)&&\"constructor\"!=r&&t.push(r);return t}},9155:t=>{\"use strict\";t.exports=e},9219:e=>{e.exports=function(e,t){return e.has(t)}},9325:(e,t,r)=>{var n=r(4840),o=\"object\"==typeof self&&self&&self.Object===Object&&self,a=n||o||Function(\"return this\")();e.exports=a},9350:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},9770:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,o=0,a=[];++r<n;){var i=e[r];t(i,r,e)&&(a[o++]=i)}return a}},9817:e=>{e.exports=function(e){return this.__data__.has(e)}},9905:(e,t,r)=>{\"use strict\";r.d(t,{default:()=>A});var n=r(3146),o=r.n(n);const a=function(e){return new RegExp(/<[a-z][\\s\\S]*>/i).test(e)},i=function(e,t){return Math.floor(Math.random()*(t-e+1))+e};var s=\"TYPE_CHARACTER\",u=\"REMOVE_CHARACTER\",c=\"REMOVE_ALL\",p=\"REMOVE_LAST_VISIBLE_NODE\",l=\"PAUSE_FOR\",f=\"CALL_FUNCTION\",v=\"ADD_HTML_TAG_ELEMENT\",d=\"CHANGE_DELETE_SPEED\",h=\"CHANGE_DELAY\",y=\"CHANGE_CURSOR\",b=\"PASTE_STRING\",m=\"HTML_TAG\";function _(e){return _=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},_(e)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach((function(t){O(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function x(e){return function(e){if(Array.isArray(e))return j(e)}(e)||function(e){if(\"undefined\"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e[\"@@iterator\"])return Array.from(e)}(e)||function(e,t){if(e){if(\"string\"==typeof e)return j(e,t);var r={}.toString.call(e).slice(8,-1);return\"Object\"===r&&e.constructor&&(r=e.constructor.name),\"Map\"===r||\"Set\"===r?Array.from(e):\"Arguments\"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?j(e,t):void 0}}(e)||function(){throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\")}()}function j(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function E(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(e,T(n.key),n)}}function O(e,t,r){return(t=T(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function T(e){var t=function(e){if(\"object\"!=_(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,\"string\");if(\"object\"!=_(r))return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return String(e)}(e);return\"symbol\"==_(t)?t:t+\"\"}const A=function(){function e(t,r){var _=this;if(function(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}(this,e),O(this,\"state\",{cursorAnimation:null,lastFrameTime:null,pauseUntil:null,eventQueue:[],eventLoop:null,eventLoopPaused:!1,reverseCalledEvents:[],calledEvents:[],visibleNodes:[],initialOptions:null,elements:{container:null,wrapper:document.createElement(\"span\"),cursor:document.createElement(\"span\")}}),O(this,\"options\",{strings:null,cursor:\"|\",delay:\"natural\",pauseFor:1500,deleteSpeed:\"natural\",loop:!1,autoStart:!1,devMode:!1,skipAddStyles:!1,wrapperClassName:\"Typewriter__wrapper\",cursorClassName:\"Typewriter__cursor\",stringSplitter:null,onCreateTextNode:null,onRemoveNode:null}),O(this,\"setupWrapperElement\",(function(){_.state.elements.container&&(_.state.elements.wrapper.className=_.options.wrapperClassName,_.state.elements.cursor.className=_.options.cursorClassName,_.state.elements.cursor.innerHTML=_.options.cursor,_.state.elements.container.innerHTML=\"\",_.state.elements.container.appendChild(_.state.elements.wrapper),_.state.elements.container.appendChild(_.state.elements.cursor))})),O(this,\"start\",(function(){return _.state.eventLoopPaused=!1,_.runEventLoop(),_})),O(this,\"pause\",(function(){return _.state.eventLoopPaused=!0,_})),O(this,\"stop\",(function(){return _.state.eventLoop&&((0,n.cancel)(_.state.eventLoop),_.state.eventLoop=null),_})),O(this,\"pauseFor\",(function(e){return _.addEventToQueue(l,{ms:e}),_})),O(this,\"typeOutAllStrings\",(function(){return\"string\"==typeof _.options.strings?(_.typeString(_.options.strings).pauseFor(_.options.pauseFor),_):(_.options.strings.forEach((function(e){_.typeString(e).pauseFor(_.options.pauseFor).deleteAll(_.options.deleteSpeed)})),_)})),O(this,\"typeString\",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(a(e))return _.typeOutHTMLString(e,t);if(e){var r=(_.options||{}).stringSplitter,n=\"function\"==typeof r?r(e):e.split(\"\");_.typeCharacters(n,t)}return _})),O(this,\"pasteString\",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return a(e)?_.typeOutHTMLString(e,t,!0):(e&&_.addEventToQueue(b,{character:e,node:t}),_)})),O(this,\"typeOutHTMLString\",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2?arguments[2]:void 0,n=function(e){var t=document.createElement(\"div\");return t.innerHTML=e,t.childNodes}(e);if(n.length>0)for(var o=0;o<n.length;o++){var a=n[o],i=a.innerHTML;a&&3!==a.nodeType?(a.innerHTML=\"\",_.addEventToQueue(v,{node:a,parentNode:t}),r?_.pasteString(i,a):_.typeString(i,a)):a.textContent&&(r?_.pasteString(a.textContent,t):_.typeString(a.textContent,t))}return _})),O(this,\"deleteAll\",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:\"natural\";return _.addEventToQueue(c,{speed:e}),_})),O(this,\"changeDeleteSpeed\",(function(e){if(!e)throw new Error(\"Must provide new delete speed\");return _.addEventToQueue(d,{speed:e}),_})),O(this,\"changeDelay\",(function(e){if(!e)throw new Error(\"Must provide new delay\");return _.addEventToQueue(h,{delay:e}),_})),O(this,\"changeCursor\",(function(e){if(!e)throw new Error(\"Must provide new cursor\");return _.addEventToQueue(y,{cursor:e}),_})),O(this,\"deleteChars\",(function(e){if(!e)throw new Error(\"Must provide amount of characters to delete\");for(var t=0;t<e;t++)_.addEventToQueue(u);return _})),O(this,\"callFunction\",(function(e,t){if(!e||\"function\"!=typeof e)throw new Error(\"Callback must be a function\");return _.addEventToQueue(f,{cb:e,thisArg:t}),_})),O(this,\"typeCharacters\",(function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!e||!Array.isArray(e))throw new Error(\"Characters must be an array\");return e.forEach((function(e){_.addEventToQueue(s,{character:e,node:t})})),_})),O(this,\"removeCharacters\",(function(e){if(!e||!Array.isArray(e))throw new Error(\"Characters must be an array\");return e.forEach((function(){_.addEventToQueue(u)})),_})),O(this,\"addEventToQueue\",(function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return _.addEventToStateProperty(e,t,r,\"eventQueue\")})),O(this,\"addReverseCalledEvent\",(function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return _.options.loop?_.addEventToStateProperty(e,t,r,\"reverseCalledEvents\"):_})),O(this,\"addEventToStateProperty\",(function(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3?arguments[3]:void 0,o={eventName:e,eventArgs:t||{}};return _.state[n]=r?[o].concat(x(_.state[n])):[].concat(x(_.state[n]),[o]),_})),O(this,\"runEventLoop\",(function(){_.state.lastFrameTime||(_.state.lastFrameTime=Date.now());var e=Date.now(),t=e-_.state.lastFrameTime;if(!_.state.eventQueue.length){if(!_.options.loop)return;_.state.eventQueue=x(_.state.calledEvents),_.state.calledEvents=[],_.options=w({},_.state.initialOptions)}if(_.state.eventLoop=o()(_.runEventLoop),!_.state.eventLoopPaused){if(_.state.pauseUntil){if(e<_.state.pauseUntil)return;_.state.pauseUntil=null}var r,n=x(_.state.eventQueue),a=n.shift();if(!(t<=(r=a.eventName===p||a.eventName===u?\"natural\"===_.options.deleteSpeed?i(40,80):_.options.deleteSpeed:\"natural\"===_.options.delay?i(120,160):_.options.delay))){var g=a.eventName,j=a.eventArgs;switch(_.logInDevMode({currentEvent:a,state:_.state,delay:r}),g){case b:case s:var E=j.character,O=j.node,T=document.createTextNode(E),A=T;_.options.onCreateTextNode&&\"function\"==typeof _.options.onCreateTextNode&&(A=_.options.onCreateTextNode(E,T)),A&&(O?O.appendChild(A):_.state.elements.wrapper.appendChild(A)),_.state.visibleNodes=[].concat(x(_.state.visibleNodes),[{type:\"TEXT_NODE\",character:E,node:A}]);break;case u:n.unshift({eventName:p,eventArgs:{removingCharacterNode:!0}});break;case l:var S=a.eventArgs.ms;_.state.pauseUntil=Date.now()+parseInt(S);break;case f:var N=a.eventArgs,P=N.cb,C=N.thisArg;P.call(C,{elements:_.state.elements});break;case v:var L=a.eventArgs,k=L.node,D=L.parentNode;D?D.appendChild(k):_.state.elements.wrapper.appendChild(k),_.state.visibleNodes=[].concat(x(_.state.visibleNodes),[{type:m,node:k,parentNode:D||_.state.elements.wrapper}]);break;case c:var M=_.state.visibleNodes,R=j.speed,F=[];R&&F.push({eventName:d,eventArgs:{speed:R,temp:!0}});for(var z=0,Q=M.length;z<Q;z++)F.push({eventName:p,eventArgs:{removingCharacterNode:!1}});R&&F.push({eventName:d,eventArgs:{speed:_.options.deleteSpeed,temp:!0}}),n.unshift.apply(n,F);break;case p:var I=a.eventArgs.removingCharacterNode;if(_.state.visibleNodes.length){var U=_.state.visibleNodes.pop(),H=U.type,q=U.node,B=U.character;_.options.onRemoveNode&&\"function\"==typeof _.options.onRemoveNode&&_.options.onRemoveNode({node:q,character:B}),q&&q.parentNode.removeChild(q),H===m&&I&&n.unshift({eventName:p,eventArgs:{}})}break;case d:_.options.deleteSpeed=a.eventArgs.speed;break;case h:_.options.delay=a.eventArgs.delay;break;case y:_.options.cursor=a.eventArgs.cursor,_.state.elements.cursor.innerHTML=a.eventArgs.cursor}_.options.loop&&(a.eventName===p||a.eventArgs&&a.eventArgs.temp||(_.state.calledEvents=[].concat(x(_.state.calledEvents),[a]))),_.state.eventQueue=n,_.state.lastFrameTime=e}}})),t)if(\"string\"==typeof t){var g=document.querySelector(t);if(!g)throw new Error(\"Could not find container element\");this.state.elements.container=g}else this.state.elements.container=t;r&&(this.options=w(w({},this.options),r)),this.state.initialOptions=w({},this.options),this.init()}var t,r;return t=e,(r=[{key:\"init\",value:function(){var e,t;this.setupWrapperElement(),this.addEventToQueue(y,{cursor:this.options.cursor},!0),this.addEventToQueue(c,null,!0),!window||window.___TYPEWRITER_JS_STYLES_ADDED___||this.options.skipAddStyles||(e=\".Typewriter__cursor{-webkit-animation:Typewriter-cursor 1s infinite;animation:Typewriter-cursor 1s infinite;margin-left:1px}@-webkit-keyframes Typewriter-cursor{0%{opacity:0}50%{opacity:1}100%{opacity:0}}@keyframes Typewriter-cursor{0%{opacity:0}50%{opacity:1}100%{opacity:0}}\",(t=document.createElement(\"style\")).appendChild(document.createTextNode(e)),document.head.appendChild(t),window.___TYPEWRITER_JS_STYLES_ADDED___=!0),!0===this.options.autoStart&&this.options.strings&&this.typeOutAllStrings().start()}},{key:\"logInDevMode\",value:function(e){this.options.devMode&&console.log(e)}}])&&E(t.prototype,r),Object.defineProperty(t,\"prototype\",{writable:!1}),e}()},9935:e=>{e.exports=function(){return!1}}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={id:e,loaded:!1,exports:{}};return t[e].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if(\"object\"==typeof globalThis)return globalThis;try{return this||new Function(\"return this\")()}catch(e){if(\"object\"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nmd=e=>(e.paths=[],e.children||(e.children=[]),e);var o={};return(()=>{\"use strict\";n.d(o,{default:()=>h});var e=n(9155),t=n.n(e),r=n(9905),a=n(2404),i=n.n(a);function s(e){return s=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},s(e)}function u(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,\"value\"in n&&(n.writable=!0),Object.defineProperty(e,v(n.key),n)}}function c(e,t){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,t)}function p(e){if(void 0===e)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return e}function l(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(l=function(){return!!e})()}function f(e){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},f(e)}function v(e){var t=function(e){if(\"object\"!=s(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,\"string\");if(\"object\"!=s(r))return r;throw new TypeError(\"@@toPrimitive must return a primitive value.\")}return String(e)}(e);return\"symbol\"==s(t)?t:t+\"\"}var d=function(e){!function(e,t){if(\"function\"!=typeof t&&null!==t)throw new TypeError(\"Super expression must either be null or a function\");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,\"prototype\",{writable:!1}),t&&c(e,t)}(d,e);var n,o,a=function(e){var t=l();return function(){var r,n=f(e);if(t){var o=f(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(e,t){if(t&&(\"object\"==s(t)||\"function\"==typeof t))return t;if(void 0!==t)throw new TypeError(\"Derived constructors may only return object or undefined\");return p(e)}(this,r)}}(d);function d(){var e,t,r,n;!function(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}(this,d);for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return t=p(e=a.call.apply(a,[this].concat(i))),n={instance:null},(r=v(r=\"state\"))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,e}return n=d,(o=[{key:\"componentDidMount\",value:function(){var e=this,t=new r.default(this.typewriter,this.props.options);this.setState({instance:t},(function(){var r=e.props.onInit;r&&r(t)}))}},{key:\"componentDidUpdate\",value:function(e){i()(this.props.options,e.options)||this.setState({instance:new r.default(this.typewriter,this.props.options)})}},{key:\"componentWillUnmount\",value:function(){this.state.instance&&this.state.instance.stop()}},{key:\"render\",value:function(){var e=this,r=this.props.component;return t().createElement(r,{ref:function(t){return e.typewriter=t},className:\"Typewriter\",\"data-testid\":\"typewriter-wrapper\"})}}])&&u(n.prototype,o),Object.defineProperty(n,\"prototype\",{writable:!1}),d}(e.Component);d.defaultProps={component:\"div\"};const h=d})(),o.default})()));\n//# sourceMappingURL=react.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/typewriter-effect/dist/react.js\n");

/***/ })

};
;