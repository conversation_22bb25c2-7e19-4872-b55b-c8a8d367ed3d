/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/GifUtils/ByteStream.js":
/*!*********************************************!*\
  !*** ./dist/browser/GifUtils/ByteStream.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ByteStream: () => (/* binding */ ByteStream)\n/* harmony export */ });\nclass ByteStream {\n  constructor(bytes) {\n    this.pos = 0;\n    this.data = new Uint8ClampedArray(bytes);\n  }\n  getString(count) {\n    const slice = this.data.slice(this.pos, this.pos + count);\n    this.pos += slice.length;\n    return slice.reduce((acc, curr) => acc + String.fromCharCode(curr), \"\");\n  }\n  nextByte() {\n    return this.data[this.pos++];\n  }\n  nextTwoBytes() {\n    const increment = 2,\n      previous = 1,\n      shift = 8;\n    this.pos += increment;\n    return this.data[this.pos - increment] + (this.data[this.pos - previous] << shift);\n  }\n  readSubBlocks() {\n    let blockString = \"\",\n      size = 0;\n    const minCount = 0,\n      emptySize = 0;\n    do {\n      size = this.data[this.pos++];\n      for (let count = size; --count >= minCount; blockString += String.fromCharCode(this.data[this.pos++])) {}\n    } while (size !== emptySize);\n    return blockString;\n  }\n  readSubBlocksBin() {\n    let size = this.data[this.pos],\n      len = 0;\n    const emptySize = 0,\n      increment = 1;\n    for (let offset = 0; size !== emptySize; offset += size + increment, size = this.data[this.pos + offset]) {\n      len += size;\n    }\n    const blockData = new Uint8Array(len);\n    size = this.data[this.pos++];\n    for (let i = 0; size !== emptySize; size = this.data[this.pos++]) {\n      for (let count = size; --count >= emptySize; blockData[i++] = this.data[this.pos++]) {}\n    }\n    return blockData;\n  }\n  skipSubBlocks() {\n    for (const increment = 1, noData = 0; this.data[this.pos] !== noData; this.pos += this.data[this.pos] + increment) {}\n    this.pos++;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/GifUtils/ByteStream.js?");

/***/ }),

/***/ "./dist/browser/GifUtils/Constants.js":
/*!********************************************!*\
  !*** ./dist/browser/GifUtils/Constants.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InterlaceOffsets: () => (/* binding */ InterlaceOffsets),\n/* harmony export */   InterlaceSteps: () => (/* binding */ InterlaceSteps)\n/* harmony export */ });\nconst InterlaceOffsets = [0, 4, 2, 1];\nconst InterlaceSteps = [8, 8, 4, 2];\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/GifUtils/Constants.js?");

/***/ }),

/***/ "./dist/browser/GifUtils/Enums/DisposalMethod.js":
/*!*******************************************************!*\
  !*** ./dist/browser/GifUtils/Enums/DisposalMethod.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DisposalMethod: () => (/* binding */ DisposalMethod)\n/* harmony export */ });\nvar DisposalMethod;\n(function (DisposalMethod) {\n  DisposalMethod[DisposalMethod[\"Replace\"] = 0] = \"Replace\";\n  DisposalMethod[DisposalMethod[\"Combine\"] = 1] = \"Combine\";\n  DisposalMethod[DisposalMethod[\"RestoreBackground\"] = 2] = \"RestoreBackground\";\n  DisposalMethod[DisposalMethod[\"RestorePrevious\"] = 3] = \"RestorePrevious\";\n  DisposalMethod[DisposalMethod[\"UndefinedA\"] = 4] = \"UndefinedA\";\n  DisposalMethod[DisposalMethod[\"UndefinedB\"] = 5] = \"UndefinedB\";\n  DisposalMethod[DisposalMethod[\"UndefinedC\"] = 6] = \"UndefinedC\";\n  DisposalMethod[DisposalMethod[\"UndefinedD\"] = 7] = \"UndefinedD\";\n})(DisposalMethod || (DisposalMethod = {}));\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/GifUtils/Enums/DisposalMethod.js?");

/***/ }),

/***/ "./dist/browser/GifUtils/Types/GIFDataHeaders.js":
/*!*******************************************************!*\
  !*** ./dist/browser/GifUtils/Types/GIFDataHeaders.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GIFDataHeaders: () => (/* binding */ GIFDataHeaders)\n/* harmony export */ });\nvar GIFDataHeaders;\n(function (GIFDataHeaders) {\n  GIFDataHeaders[GIFDataHeaders[\"Extension\"] = 33] = \"Extension\";\n  GIFDataHeaders[GIFDataHeaders[\"ApplicationExtension\"] = 255] = \"ApplicationExtension\";\n  GIFDataHeaders[GIFDataHeaders[\"GraphicsControlExtension\"] = 249] = \"GraphicsControlExtension\";\n  GIFDataHeaders[GIFDataHeaders[\"PlainTextExtension\"] = 1] = \"PlainTextExtension\";\n  GIFDataHeaders[GIFDataHeaders[\"CommentExtension\"] = 254] = \"CommentExtension\";\n  GIFDataHeaders[GIFDataHeaders[\"Image\"] = 44] = \"Image\";\n  GIFDataHeaders[GIFDataHeaders[\"EndOfFile\"] = 59] = \"EndOfFile\";\n})(GIFDataHeaders || (GIFDataHeaders = {}));\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/GifUtils/Types/GIFDataHeaders.js?");

/***/ }),

/***/ "./dist/browser/GifUtils/Utils.js":
/*!****************************************!*\
  !*** ./dist/browser/GifUtils/Utils.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeGIF: () => (/* binding */ decodeGIF),\n/* harmony export */   drawGif: () => (/* binding */ drawGif),\n/* harmony export */   getGIFLoopAmount: () => (/* binding */ getGIFLoopAmount),\n/* harmony export */   loadGifImage: () => (/* binding */ loadGifImage)\n/* harmony export */ });\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../Utils.js */ \"./dist/browser/Utils.js\");\n/* harmony import */ var _Constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Constants.js */ \"./dist/browser/GifUtils/Constants.js\");\n/* harmony import */ var _ByteStream_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ByteStream.js */ \"./dist/browser/GifUtils/ByteStream.js\");\n/* harmony import */ var _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Enums/DisposalMethod.js */ \"./dist/browser/GifUtils/Enums/DisposalMethod.js\");\n/* harmony import */ var _Types_GIFDataHeaders_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Types/GIFDataHeaders.js */ \"./dist/browser/GifUtils/Types/GIFDataHeaders.js\");\n\n\n\n\n\nconst origin = {\n    x: 0,\n    y: 0\n  },\n  defaultFrame = 0,\n  half = 0.5,\n  initialTime = 0,\n  firstIndex = 0,\n  defaultLoopCount = 0;\nfunction parseColorTable(byteStream, count) {\n  const colors = [];\n  for (let i = 0; i < count; i++) {\n    colors.push({\n      r: byteStream.data[byteStream.pos],\n      g: byteStream.data[byteStream.pos + 1],\n      b: byteStream.data[byteStream.pos + 2]\n    });\n    byteStream.pos += 3;\n  }\n  return colors;\n}\nfunction parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex) {\n  switch (byteStream.nextByte()) {\n    case _Types_GIFDataHeaders_js__WEBPACK_IMPORTED_MODULE_0__.GIFDataHeaders.GraphicsControlExtension:\n      {\n        const frame = gif.frames[getFrameIndex(false)];\n        byteStream.pos++;\n        const packedByte = byteStream.nextByte();\n        frame.GCreserved = (packedByte & 0xe0) >>> 5;\n        frame.disposalMethod = (packedByte & 0x1c) >>> 2;\n        frame.userInputDelayFlag = (packedByte & 2) === 2;\n        const transparencyFlag = (packedByte & 1) === 1;\n        frame.delayTime = byteStream.nextTwoBytes() * 0xa;\n        const transparencyIndex = byteStream.nextByte();\n        if (transparencyFlag) {\n          getTransparencyIndex(transparencyIndex);\n        }\n        byteStream.pos++;\n        break;\n      }\n    case _Types_GIFDataHeaders_js__WEBPACK_IMPORTED_MODULE_0__.GIFDataHeaders.ApplicationExtension:\n      {\n        byteStream.pos++;\n        const applicationExtension = {\n          identifier: byteStream.getString(8),\n          authenticationCode: byteStream.getString(3),\n          data: byteStream.readSubBlocksBin()\n        };\n        gif.applicationExtensions.push(applicationExtension);\n        break;\n      }\n    case _Types_GIFDataHeaders_js__WEBPACK_IMPORTED_MODULE_0__.GIFDataHeaders.CommentExtension:\n      {\n        gif.comments.push([getFrameIndex(false), byteStream.readSubBlocks()]);\n        break;\n      }\n    case _Types_GIFDataHeaders_js__WEBPACK_IMPORTED_MODULE_0__.GIFDataHeaders.PlainTextExtension:\n      {\n        if (gif.globalColorTable.length === 0) {\n          throw new EvalError(\"plain text extension without global color table\");\n        }\n        byteStream.pos++;\n        gif.frames[getFrameIndex(false)].plainTextData = {\n          left: byteStream.nextTwoBytes(),\n          top: byteStream.nextTwoBytes(),\n          width: byteStream.nextTwoBytes(),\n          height: byteStream.nextTwoBytes(),\n          charSize: {\n            width: byteStream.nextTwoBytes(),\n            height: byteStream.nextTwoBytes()\n          },\n          foregroundColor: byteStream.nextByte(),\n          backgroundColor: byteStream.nextByte(),\n          text: byteStream.readSubBlocks()\n        };\n        break;\n      }\n    default:\n      byteStream.skipSubBlocks();\n      break;\n  }\n}\nasync function parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n  const frame = gif.frames[getFrameIndex(true)];\n  frame.left = byteStream.nextTwoBytes();\n  frame.top = byteStream.nextTwoBytes();\n  frame.width = byteStream.nextTwoBytes();\n  frame.height = byteStream.nextTwoBytes();\n  const packedByte = byteStream.nextByte(),\n    localColorTableFlag = (packedByte & 0x80) === 0x80,\n    interlacedFlag = (packedByte & 0x40) === 0x40;\n  frame.sortFlag = (packedByte & 0x20) === 0x20;\n  frame.reserved = (packedByte & 0x18) >>> 3;\n  const localColorCount = 1 << (packedByte & 7) + 1;\n  if (localColorTableFlag) {\n    frame.localColorTable = parseColorTable(byteStream, localColorCount);\n  }\n  const getColor = index => {\n    const {\n      r,\n      g,\n      b\n    } = (localColorTableFlag ? frame.localColorTable : gif.globalColorTable)[index];\n    if (index !== getTransparencyIndex(null)) {\n      return {\n        r,\n        g,\n        b,\n        a: 255\n      };\n    }\n    return {\n      r,\n      g,\n      b,\n      a: avgAlpha ? ~~((r + g + b) / 3) : 0\n    };\n  };\n  const image = (() => {\n    try {\n      return new ImageData(frame.width, frame.height, {\n        colorSpace: \"srgb\"\n      });\n    } catch (error) {\n      if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n        return null;\n      }\n      throw error;\n    }\n  })();\n  if (image == null) {\n    throw new EvalError(\"GIF frame size is to large\");\n  }\n  const minCodeSize = byteStream.nextByte(),\n    imageData = byteStream.readSubBlocksBin(),\n    clearCode = 1 << minCodeSize;\n  const readBits = (pos, len) => {\n    const bytePos = pos >>> 3,\n      bitPos = pos & 7;\n    return (imageData[bytePos] + (imageData[bytePos + 1] << 8) + (imageData[bytePos + 2] << 16) & (1 << len) - 1 << bitPos) >>> bitPos;\n  };\n  if (interlacedFlag) {\n    for (let code = 0, size = minCodeSize + 1, pos = 0, dic = [[0]], pass = 0; pass < 4; pass++) {\n      if (_Constants_js__WEBPACK_IMPORTED_MODULE_1__.InterlaceOffsets[pass] < frame.height) {\n        let pixelPos = 0,\n          lineIndex = 0,\n          exit = false;\n        while (!exit) {\n          const last = code;\n          code = readBits(pos, size);\n          pos += size + 1;\n          if (code === clearCode) {\n            size = minCodeSize + 1;\n            dic.length = clearCode + 2;\n            for (let i = 0; i < dic.length; i++) {\n              dic[i] = i < clearCode ? [i] : [];\n            }\n          } else {\n            if (code >= dic.length) {\n              dic.push(dic[last].concat(dic[last][0]));\n            } else if (last !== clearCode) {\n              dic.push(dic[last].concat(dic[code][0]));\n            }\n            for (const item of dic[code]) {\n              const {\n                r,\n                g,\n                b,\n                a\n              } = getColor(item);\n              image.data.set([r, g, b, a], _Constants_js__WEBPACK_IMPORTED_MODULE_1__.InterlaceOffsets[pass] * frame.width + _Constants_js__WEBPACK_IMPORTED_MODULE_1__.InterlaceSteps[pass] * lineIndex + pixelPos % (frame.width * 4));\n              pixelPos += 4;\n            }\n            if (dic.length === 1 << size && size < 0xc) {\n              size++;\n            }\n          }\n          if (pixelPos === frame.width * 4 * (lineIndex + 1)) {\n            lineIndex++;\n            if (_Constants_js__WEBPACK_IMPORTED_MODULE_1__.InterlaceOffsets[pass] + _Constants_js__WEBPACK_IMPORTED_MODULE_1__.InterlaceSteps[pass] * lineIndex >= frame.height) {\n              exit = true;\n            }\n          }\n        }\n      }\n      progressCallback?.(byteStream.pos / (byteStream.data.length - 1), getFrameIndex(false) + 1, image, {\n        x: frame.left,\n        y: frame.top\n      }, {\n        width: gif.width,\n        height: gif.height\n      });\n    }\n    frame.image = image;\n    frame.bitmap = await createImageBitmap(image);\n  } else {\n    let code = 0,\n      size = minCodeSize + 1,\n      pos = 0,\n      pixelPos = -4,\n      exit = false;\n    const dic = [[0]];\n    while (!exit) {\n      const last = code;\n      code = readBits(pos, size);\n      pos += size;\n      if (code === clearCode) {\n        size = minCodeSize + 1;\n        dic.length = clearCode + 2;\n        for (let i = 0; i < dic.length; i++) {\n          dic[i] = i < clearCode ? [i] : [];\n        }\n      } else {\n        if (code === clearCode + 1) {\n          exit = true;\n          break;\n        }\n        if (code >= dic.length) {\n          dic.push(dic[last].concat(dic[last][0]));\n        } else if (last !== clearCode) {\n          dic.push(dic[last].concat(dic[code][0]));\n        }\n        for (const item of dic[code]) {\n          const {\n            r,\n            g,\n            b,\n            a\n          } = getColor(item);\n          image.data.set([r, g, b, a], pixelPos += 4);\n        }\n        if (dic.length >= 1 << size && size < 0xc) {\n          size++;\n        }\n      }\n    }\n    frame.image = image;\n    frame.bitmap = await createImageBitmap(image);\n    progressCallback?.((byteStream.pos + 1) / byteStream.data.length, getFrameIndex(false) + 1, frame.image, {\n      x: frame.left,\n      y: frame.top\n    }, {\n      width: gif.width,\n      height: gif.height\n    });\n  }\n}\nasync function parseBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback) {\n  switch (byteStream.nextByte()) {\n    case _Types_GIFDataHeaders_js__WEBPACK_IMPORTED_MODULE_0__.GIFDataHeaders.EndOfFile:\n      return true;\n    case _Types_GIFDataHeaders_js__WEBPACK_IMPORTED_MODULE_0__.GIFDataHeaders.Image:\n      await parseImageBlock(byteStream, gif, avgAlpha, getFrameIndex, getTransparencyIndex, progressCallback);\n      break;\n    case _Types_GIFDataHeaders_js__WEBPACK_IMPORTED_MODULE_0__.GIFDataHeaders.Extension:\n      parseExtensionBlock(byteStream, gif, getFrameIndex, getTransparencyIndex);\n      break;\n    default:\n      throw new EvalError(\"undefined block found\");\n  }\n  return false;\n}\nfunction getGIFLoopAmount(gif) {\n  for (const extension of gif.applicationExtensions) {\n    if (extension.identifier + extension.authenticationCode !== \"NETSCAPE2.0\") {\n      continue;\n    }\n    return extension.data[1] + (extension.data[2] << 8);\n  }\n  return NaN;\n}\nasync function decodeGIF(gifURL, progressCallback, avgAlpha) {\n  if (!avgAlpha) avgAlpha = false;\n  const res = await fetch(gifURL);\n  if (!res.ok && res.status === 404) {\n    throw new EvalError(\"file not found\");\n  }\n  const buffer = await res.arrayBuffer();\n  const gif = {\n      width: 0,\n      height: 0,\n      totalTime: 0,\n      colorRes: 0,\n      pixelAspectRatio: 0,\n      frames: [],\n      sortFlag: false,\n      globalColorTable: [],\n      backgroundImage: new ImageData(1, 1, {\n        colorSpace: \"srgb\"\n      }),\n      comments: [],\n      applicationExtensions: []\n    },\n    byteStream = new _ByteStream_js__WEBPACK_IMPORTED_MODULE_2__.ByteStream(new Uint8ClampedArray(buffer));\n  if (byteStream.getString(6) !== \"GIF89a\") {\n    throw new Error(\"not a supported GIF file\");\n  }\n  gif.width = byteStream.nextTwoBytes();\n  gif.height = byteStream.nextTwoBytes();\n  const packedByte = byteStream.nextByte(),\n    globalColorTableFlag = (packedByte & 0x80) === 0x80;\n  gif.colorRes = (packedByte & 0x70) >>> 4;\n  gif.sortFlag = (packedByte & 8) === 8;\n  const globalColorCount = 1 << (packedByte & 7) + 1,\n    backgroundColorIndex = byteStream.nextByte();\n  gif.pixelAspectRatio = byteStream.nextByte();\n  if (gif.pixelAspectRatio !== 0) {\n    gif.pixelAspectRatio = (gif.pixelAspectRatio + 0xf) / 0x40;\n  }\n  if (globalColorTableFlag) {\n    gif.globalColorTable = parseColorTable(byteStream, globalColorCount);\n  }\n  const backgroundImage = (() => {\n    try {\n      return new ImageData(gif.width, gif.height, {\n        colorSpace: \"srgb\"\n      });\n    } catch (error) {\n      if (error instanceof DOMException && error.name === \"IndexSizeError\") {\n        return null;\n      }\n      throw error;\n    }\n  })();\n  if (backgroundImage == null) {\n    throw new Error(\"GIF frame size is to large\");\n  }\n  const {\n    r,\n    g,\n    b\n  } = gif.globalColorTable[backgroundColorIndex];\n  backgroundImage.data.set(globalColorTableFlag ? [r, g, b, 255] : [0, 0, 0, 0]);\n  for (let i = 4; i < backgroundImage.data.length; i *= 2) {\n    backgroundImage.data.copyWithin(i, 0, i);\n  }\n  gif.backgroundImage = backgroundImage;\n  let frameIndex = -1,\n    incrementFrameIndex = true,\n    transparencyIndex = -1;\n  const getframeIndex = increment => {\n    if (increment) {\n      incrementFrameIndex = true;\n    }\n    return frameIndex;\n  };\n  const getTransparencyIndex = newValue => {\n    if (newValue != null) {\n      transparencyIndex = newValue;\n    }\n    return transparencyIndex;\n  };\n  try {\n    do {\n      if (incrementFrameIndex) {\n        gif.frames.push({\n          left: 0,\n          top: 0,\n          width: 0,\n          height: 0,\n          disposalMethod: _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.Replace,\n          image: new ImageData(1, 1, {\n            colorSpace: \"srgb\"\n          }),\n          plainTextData: null,\n          userInputDelayFlag: false,\n          delayTime: 0,\n          sortFlag: false,\n          localColorTable: [],\n          reserved: 0,\n          GCreserved: 0\n        });\n        frameIndex++;\n        transparencyIndex = -1;\n        incrementFrameIndex = false;\n      }\n    } while (!(await parseBlock(byteStream, gif, avgAlpha, getframeIndex, getTransparencyIndex, progressCallback)));\n    gif.frames.length--;\n    for (const frame of gif.frames) {\n      if (frame.userInputDelayFlag && frame.delayTime === 0) {\n        gif.totalTime = Infinity;\n        break;\n      }\n      gif.totalTime += frame.delayTime;\n    }\n    return gif;\n  } catch (error) {\n    if (error instanceof EvalError) {\n      throw new Error(`error while parsing frame ${frameIndex} \"${error.message}\"`);\n    }\n    throw error;\n  }\n}\nfunction drawGif(data) {\n  const {\n      context,\n      radius,\n      particle,\n      delta\n    } = data,\n    image = particle.image;\n  if (!image?.gifData || !image.gif) {\n    return;\n  }\n  const offscreenCanvas = new OffscreenCanvas(image.gifData.width, image.gifData.height),\n    offscreenContext = offscreenCanvas.getContext(\"2d\");\n  if (!offscreenContext) {\n    throw new Error(\"could not create offscreen canvas context\");\n  }\n  offscreenContext.imageSmoothingQuality = \"low\";\n  offscreenContext.imageSmoothingEnabled = false;\n  offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n  if (particle.gifLoopCount === undefined) {\n    particle.gifLoopCount = image.gifLoopCount ?? defaultLoopCount;\n  }\n  let frameIndex = particle.gifFrame ?? defaultFrame;\n  const pos = {\n      x: -image.gifData.width * half,\n      y: -image.gifData.height * half\n    },\n    frame = image.gifData.frames[frameIndex];\n  if (particle.gifTime === undefined) {\n    particle.gifTime = initialTime;\n  }\n  if (!frame.bitmap) {\n    return;\n  }\n  context.scale(radius / image.gifData.width, radius / image.gifData.height);\n  switch (frame.disposalMethod) {\n    case _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.UndefinedA:\n    case _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.UndefinedB:\n    case _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.UndefinedC:\n    case _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.UndefinedD:\n    case _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.Replace:\n      offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n      context.drawImage(offscreenCanvas, pos.x, pos.y);\n      offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n      break;\n    case _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.Combine:\n      offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n      context.drawImage(offscreenCanvas, pos.x, pos.y);\n      break;\n    case _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.RestoreBackground:\n      offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n      context.drawImage(offscreenCanvas, pos.x, pos.y);\n      offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n      if (!image.gifData.globalColorTable.length) {\n        offscreenContext.putImageData(image.gifData.frames[firstIndex].image, pos.x + frame.left, pos.y + frame.top);\n      } else {\n        offscreenContext.putImageData(image.gifData.backgroundImage, pos.x, pos.y);\n      }\n      break;\n    case _Enums_DisposalMethod_js__WEBPACK_IMPORTED_MODULE_3__.DisposalMethod.RestorePrevious:\n      {\n        const previousImageData = offscreenContext.getImageData(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n        offscreenContext.drawImage(frame.bitmap, frame.left, frame.top);\n        context.drawImage(offscreenCanvas, pos.x, pos.y);\n        offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n        offscreenContext.putImageData(previousImageData, origin.x, origin.y);\n      }\n      break;\n  }\n  particle.gifTime += delta.value;\n  if (particle.gifTime > frame.delayTime) {\n    particle.gifTime -= frame.delayTime;\n    if (++frameIndex >= image.gifData.frames.length) {\n      if (--particle.gifLoopCount <= defaultLoopCount) {\n        return;\n      }\n      frameIndex = firstIndex;\n      offscreenContext.clearRect(origin.x, origin.y, offscreenCanvas.width, offscreenCanvas.height);\n    }\n    particle.gifFrame = frameIndex;\n  }\n  context.scale(image.gifData.width / radius, image.gifData.height / radius);\n}\nasync function loadGifImage(image) {\n  if (image.type !== \"gif\") {\n    await (0,_Utils_js__WEBPACK_IMPORTED_MODULE_4__.loadImage)(image);\n    return;\n  }\n  image.loading = true;\n  try {\n    image.gifData = await decodeGIF(image.source);\n    image.gifLoopCount = getGIFLoopAmount(image.gifData) ?? defaultLoopCount;\n    if (!image.gifLoopCount) {\n      image.gifLoopCount = Infinity;\n    }\n  } catch {\n    image.error = true;\n  }\n  image.loading = false;\n}\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/GifUtils/Utils.js?");

/***/ }),

/***/ "./dist/browser/ImageDrawer.js":
/*!*************************************!*\
  !*** ./dist/browser/ImageDrawer.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageDrawer: () => (/* binding */ ImageDrawer)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n/* harmony import */ var _GifUtils_Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GifUtils/Utils.js */ \"./dist/browser/GifUtils/Utils.js\");\n\n\n\nconst double = 2,\n  defaultAlpha = 1,\n  sides = 12,\n  defaultRatio = 1;\nclass ImageDrawer {\n  constructor(engine) {\n    this.validTypes = [\"image\", \"images\"];\n    this.loadImageShape = async imageShape => {\n      if (!this._engine.loadImage) {\n        throw new Error(`${_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} image shape not initialized`);\n      }\n      await this._engine.loadImage({\n        gif: imageShape.gif,\n        name: imageShape.name,\n        replaceColor: imageShape.replaceColor ?? false,\n        src: imageShape.src\n      });\n    };\n    this._engine = engine;\n  }\n  addImage(image) {\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    this._engine.images.push(image);\n  }\n  draw(data) {\n    const {\n        context,\n        radius,\n        particle,\n        opacity\n      } = data,\n      image = particle.image,\n      element = image?.element;\n    if (!image) {\n      return;\n    }\n    context.globalAlpha = opacity;\n    if (image.gif && image.gifData) {\n      (0,_GifUtils_Utils_js__WEBPACK_IMPORTED_MODULE_1__.drawGif)(data);\n    } else if (element) {\n      const ratio = image.ratio,\n        pos = {\n          x: -radius,\n          y: -radius\n        },\n        diameter = radius * double;\n      context.drawImage(element, pos.x, pos.y, diameter, diameter / ratio);\n    }\n    context.globalAlpha = defaultAlpha;\n  }\n  getSidesCount() {\n    return sides;\n  }\n  async init(container) {\n    const options = container.actualOptions;\n    if (!options.preload || !this._engine.loadImage) {\n      return;\n    }\n    for (const imageData of options.preload) {\n      await this._engine.loadImage(imageData);\n    }\n  }\n  loadShape(particle) {\n    if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n      return;\n    }\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    const imageData = particle.shapeData;\n    if (!imageData) {\n      return;\n    }\n    const image = this._engine.images.find(t => t.name === imageData.name || t.source === imageData.src);\n    if (!image) {\n      void this.loadImageShape(imageData).then(() => {\n        this.loadShape(particle);\n      });\n    }\n  }\n  particleInit(container, particle) {\n    if (particle.shape !== \"image\" && particle.shape !== \"images\") {\n      return;\n    }\n    if (!this._engine.images) {\n      this._engine.images = [];\n    }\n    const images = this._engine.images,\n      imageData = particle.shapeData;\n    if (!imageData) {\n      return;\n    }\n    const color = particle.getFillColor(),\n      image = images.find(t => t.name === imageData.name || t.source === imageData.src);\n    if (!image) {\n      return;\n    }\n    const replaceColor = imageData.replaceColor ?? image.replaceColor;\n    if (image.loading) {\n      setTimeout(() => {\n        this.particleInit(container, particle);\n      });\n      return;\n    }\n    void (async () => {\n      let imageRes;\n      if (image.svgData && color) {\n        imageRes = await (0,_Utils_js__WEBPACK_IMPORTED_MODULE_2__.replaceImageColor)(image, imageData, color, particle);\n      } else {\n        imageRes = {\n          color,\n          data: image,\n          element: image.element,\n          gif: image.gif,\n          gifData: image.gifData,\n          gifLoopCount: image.gifLoopCount,\n          loaded: true,\n          ratio: imageData.width && imageData.height ? imageData.width / imageData.height : image.ratio ?? defaultRatio,\n          replaceColor: replaceColor,\n          source: imageData.src\n        };\n      }\n      if (!imageRes.ratio) {\n        imageRes.ratio = 1;\n      }\n      const fill = imageData.fill ?? particle.shapeFill,\n        close = imageData.close ?? particle.shapeClose,\n        imageShape = {\n          image: imageRes,\n          fill,\n          close\n        };\n      particle.image = imageShape.image;\n      particle.shapeFill = imageShape.fill;\n      particle.shapeClose = imageShape.close;\n    })();\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/ImageDrawer.js?");

/***/ }),

/***/ "./dist/browser/ImagePreloader.js":
/*!****************************************!*\
  !*** ./dist/browser/ImagePreloader.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImagePreloaderPlugin: () => (/* binding */ ImagePreloaderPlugin)\n/* harmony export */ });\n/* harmony import */ var _Options_Classes_Preload_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Options/Classes/Preload.js */ \"./dist/browser/Options/Classes/Preload.js\");\n\nclass ImagePreloaderPlugin {\n  constructor(engine) {\n    this.id = \"imagePreloader\";\n    this._engine = engine;\n  }\n  async getPlugin() {\n    await Promise.resolve();\n    return {};\n  }\n  loadOptions(options, source) {\n    if (!source?.preload) {\n      return;\n    }\n    if (!options.preload) {\n      options.preload = [];\n    }\n    const preloadOptions = options.preload;\n    for (const item of source.preload) {\n      const existing = preloadOptions.find(t => t.name === item.name || t.src === item.src);\n      if (existing) {\n        existing.load(item);\n      } else {\n        const preload = new _Options_Classes_Preload_js__WEBPACK_IMPORTED_MODULE_0__.Preload();\n        preload.load(item);\n        preloadOptions.push(preload);\n      }\n    }\n  }\n  needsPlugin() {\n    return true;\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/ImagePreloader.js?");

/***/ }),

/***/ "./dist/browser/Options/Classes/Preload.js":
/*!*************************************************!*\
  !*** ./dist/browser/Options/Classes/Preload.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Preload: () => (/* binding */ Preload)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nclass Preload {\n  constructor() {\n    this.src = \"\";\n    this.gif = false;\n  }\n  load(data) {\n    if ((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isNull)(data)) {\n      return;\n    }\n    if (data.gif !== undefined) {\n      this.gif = data.gif;\n    }\n    if (data.height !== undefined) {\n      this.height = data.height;\n    }\n    if (data.name !== undefined) {\n      this.name = data.name;\n    }\n    if (data.replaceColor !== undefined) {\n      this.replaceColor = data.replaceColor;\n    }\n    if (data.src !== undefined) {\n      this.src = data.src;\n    }\n    if (data.width !== undefined) {\n      this.width = data.width;\n    }\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/Options/Classes/Preload.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   downloadSvgImage: () => (/* binding */ downloadSvgImage),\n/* harmony export */   loadImage: () => (/* binding */ loadImage),\n/* harmony export */   replaceImageColor: () => (/* binding */ replaceImageColor)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst stringStart = 0,\n  defaultOpacity = 1;\nconst currentColorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\\((-?\\d+%?[,\\s]+){2,3}\\s*[\\d.]+%?\\))|currentcolor/gi;\nfunction replaceColorSvg(imageShape, color, opacity) {\n  const {\n    svgData\n  } = imageShape;\n  if (!svgData) {\n    return \"\";\n  }\n  const colorStyle = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getStyleFromHsl)(color, opacity);\n  if (svgData.includes(\"fill\")) {\n    return svgData.replace(currentColorRegex, () => colorStyle);\n  }\n  const preFillIndex = svgData.indexOf(\">\");\n  return `${svgData.substring(stringStart, preFillIndex)} fill=\"${colorStyle}\"${svgData.substring(preFillIndex)}`;\n}\nasync function loadImage(image) {\n  return new Promise(resolve => {\n    image.loading = true;\n    const img = new Image();\n    image.element = img;\n    img.addEventListener(\"load\", () => {\n      image.loading = false;\n      resolve();\n    });\n    img.addEventListener(\"error\", () => {\n      image.element = undefined;\n      image.error = true;\n      image.loading = false;\n      (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getLogger)().error(`${_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} loading image: ${image.source}`);\n      resolve();\n    });\n    img.src = image.source;\n  });\n}\nasync function downloadSvgImage(image) {\n  if (image.type !== \"svg\") {\n    await loadImage(image);\n    return;\n  }\n  image.loading = true;\n  const response = await fetch(image.source);\n  if (!response.ok) {\n    (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.getLogger)().error(`${_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} Image not found`);\n    image.error = true;\n  } else {\n    image.svgData = await response.text();\n  }\n  image.loading = false;\n}\nfunction replaceImageColor(image, imageData, color, particle) {\n  const svgColoredData = replaceColorSvg(image, color, particle.opacity?.value ?? defaultOpacity),\n    imageRes = {\n      color,\n      gif: imageData.gif,\n      data: {\n        ...image,\n        svgData: svgColoredData\n      },\n      loaded: false,\n      ratio: imageData.width / imageData.height,\n      replaceColor: imageData.replaceColor,\n      source: imageData.src\n    };\n  return new Promise(resolve => {\n    const svg = new Blob([svgColoredData], {\n        type: \"image/svg+xml\"\n      }),\n      domUrl = URL || window.URL || window.webkitURL || window,\n      url = domUrl.createObjectURL(svg),\n      img = new Image();\n    img.addEventListener(\"load\", () => {\n      imageRes.loaded = true;\n      imageRes.element = img;\n      resolve(imageRes);\n      domUrl.revokeObjectURL(url);\n    });\n    const errorHandler = async () => {\n      domUrl.revokeObjectURL(url);\n      const img2 = {\n        ...image,\n        error: false,\n        loading: true\n      };\n      await loadImage(img2);\n      imageRes.loaded = true;\n      imageRes.element = img2.element;\n      resolve(imageRes);\n    };\n    img.addEventListener(\"error\", () => void errorHandler());\n    img.src = url;\n  });\n}\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadImageShape: () => (/* binding */ loadImageShape)\n/* harmony export */ });\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n/* harmony import */ var _ImageDrawer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ImageDrawer.js */ \"./dist/browser/ImageDrawer.js\");\n/* harmony import */ var _ImagePreloader_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ImagePreloader.js */ \"./dist/browser/ImagePreloader.js\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _GifUtils_Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./GifUtils/Utils.js */ \"./dist/browser/GifUtils/Utils.js\");\n\n\n\n\n\nconst extLength = 3;\nfunction addLoadImageToEngine(engine) {\n  if (engine.loadImage) {\n    return;\n  }\n  engine.loadImage = async data => {\n    if (!data.name && !data.src) {\n      throw new Error(`${_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} no image source provided`);\n    }\n    if (!engine.images) {\n      engine.images = [];\n    }\n    if (engine.images.find(t => t.name === data.name || t.source === data.src)) {\n      return;\n    }\n    try {\n      const image = {\n        gif: data.gif ?? false,\n        name: data.name ?? data.src,\n        source: data.src,\n        type: data.src.substring(data.src.length - extLength),\n        error: false,\n        loading: true,\n        replaceColor: data.replaceColor,\n        ratio: data.width && data.height ? data.width / data.height : undefined\n      };\n      engine.images.push(image);\n      let imageFunc;\n      if (data.gif) {\n        imageFunc = _GifUtils_Utils_js__WEBPACK_IMPORTED_MODULE_1__.loadGifImage;\n      } else {\n        imageFunc = data.replaceColor ? _Utils_js__WEBPACK_IMPORTED_MODULE_2__.downloadSvgImage : _Utils_js__WEBPACK_IMPORTED_MODULE_2__.loadImage;\n      }\n      await imageFunc(image);\n    } catch {\n      throw new Error(`${_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.errorPrefix} ${data.name ?? data.src} not found`);\n    }\n  };\n}\nasync function loadImageShape(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  addLoadImageToEngine(engine);\n  const preloader = new _ImagePreloader_js__WEBPACK_IMPORTED_MODULE_3__.ImagePreloaderPlugin(engine);\n  await engine.addPlugin(preloader, refresh);\n  await engine.addShape(new _ImageDrawer_js__WEBPACK_IMPORTED_MODULE_4__.ImageDrawer(engine), refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/shape-image/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});