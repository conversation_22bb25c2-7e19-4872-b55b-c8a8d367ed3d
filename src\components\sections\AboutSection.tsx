'use client';

import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { MapPin, Phone, Mail, GraduationCap, Code, Heart } from 'lucide-react';

gsap.registerPlugin(ScrollTrigger);

export default function AboutSection() {
  const sectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate cards on scroll
      gsap.fromTo(
        '.about-card',
        { opacity: 0, y: 50, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.about-cards',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse',
          },
        }
      );

      // Animate text elements
      gsap.fromTo(
        '.about-text',
        { opacity: 0, x: -50 },
        {
          opacity: 1,
          x: 0,
          duration: 1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.about-content',
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const personalInfo = [
    { icon: MapPin, label: 'Location', value: 'Bengaluru, India' },
    { icon: Phone, label: 'Phone', value: '+91 8319130513' },
    { icon: Mail, label: 'Email', value: '<EMAIL>' },
    { icon: GraduationCap, label: 'Education', value: 'B.Tech (IT) - Bhilai Institute Of Technology' },
  ];

  const highlights = [
    {
      icon: Code,
      title: 'Full Stack Development',
      description: 'Expertise in React.js, Node.js, Express.js, and MongoDB for building complete web applications.',
    },
    {
      icon: Heart,
      title: 'Passionate Learner',
      description: 'Always eager to learn new technologies and stay updated with the latest industry trends.',
    },
    {
      icon: GraduationCap,
      title: 'Fresh Graduate',
      description: 'Recent B.Tech IT graduate with hands-on experience through projects and training.',
    },
  ];

  return (
    <section
      id="about"
      ref={sectionRef}
      className="min-h-screen py-20 px-4 sm:px-6 lg:px-8"
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6">
            About Me
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get to know more about my background, skills, and passion for technology
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center about-content">
          {/* Left Side - Personal Info */}
          <div className="about-text">
            <h3 className="text-3xl font-bold text-neon-blue mb-6 font-space">
              Hello! I'm Saurabh Dahariya
            </h3>
            
            <p className="text-lg text-gray-300 mb-8 leading-relaxed">
              I'm a passionate Full Stack Developer with a strong foundation in modern web technologies. 
              As a recent B.Tech IT graduate from Bhilai Institute Of Technology, I bring fresh perspectives 
              and enthusiasm to every project I work on.
            </p>

            <p className="text-lg text-gray-300 mb-8 leading-relaxed">
              My journey in web development started during my college years, and I've been continuously 
              learning and building projects to enhance my skills. I'm particularly passionate about 
              creating responsive, user-friendly applications that solve real-world problems.
            </p>

            {/* Personal Information Cards */}
            <div className="grid sm:grid-cols-2 gap-4">
              {personalInfo.map((info, index) => {
                const Icon = info.icon;
                return (
                  <motion.div
                    key={index}
                    className="glass p-4 rounded-lg hover:glow-box transition-all duration-300"
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="text-neon-blue" size={20} />
                      <div>
                        <p className="text-sm text-gray-400">{info.label}</p>
                        <p className="text-white font-medium">{info.value}</p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>

          {/* Right Side - Highlights */}
          <div className="about-cards">
            <h3 className="text-2xl font-bold text-neon-green mb-8 font-space">
              What Makes Me Unique
            </h3>
            
            <div className="space-y-6">
              {highlights.map((highlight, index) => {
                const Icon = highlight.icon;
                return (
                  <motion.div
                    key={index}
                    className="about-card glass-strong p-6 rounded-xl hover:glow-box transition-all duration-300"
                    whileHover={{ scale: 1.02, y: -5 }}
                  >
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-gradient-to-r from-neon-blue to-neon-green rounded-lg">
                        <Icon className="text-black" size={24} />
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-white mb-2">
                          {highlight.title}
                        </h4>
                        <p className="text-gray-300 leading-relaxed">
                          {highlight.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Training Section */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="glass-strong p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-neon-purple mb-4 font-space">
              Current Training
            </h3>
            <p className="text-lg text-gray-300 mb-4">
              <strong>MERN Stack Development</strong> at JSpider BTM Layout, Bengaluru
            </p>
            <p className="text-gray-400">
              September 2024 - February 2025
            </p>
            <p className="text-gray-300 mt-4">
              Intensive training program focusing on MongoDB, Express.js, React.js, and Node.js 
              to build full-stack web applications.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
