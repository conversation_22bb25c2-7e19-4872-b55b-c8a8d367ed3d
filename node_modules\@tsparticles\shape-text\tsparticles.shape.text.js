/*!
 * Author : <PERSON>
 * MIT license: https://opensource.org/licenses/MIT
 * Demo / Generator : https://particles.js.org/
 * GitHub : https://www.github.com/matteobruni/tsparticles
 * How to use? : Check the GitHub README
 * v3.8.1
 */
/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("@tsparticles/engine"));
	else if(typeof define === 'function' && define.amd)
		define(["@tsparticles/engine"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("@tsparticles/engine")) : factory(root["window"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(this, (__WEBPACK_EXTERNAL_MODULE__tsparticles_engine__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./dist/browser/TextDrawer.js":
/*!************************************!*\
  !*** ./dist/browser/TextDrawer.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TextDrawer: () => (/* binding */ TextDrawer)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Utils.js */ \"./dist/browser/Utils.js\");\n\n\nclass TextDrawer {\n  constructor() {\n    this.validTypes = [\"text\", \"character\", \"char\", \"multiline-text\"];\n  }\n  draw(data) {\n    (0,_Utils_js__WEBPACK_IMPORTED_MODULE_1__.drawText)(data);\n  }\n  async init(container) {\n    const options = container.actualOptions,\n      {\n        validTypes\n      } = this;\n    if (validTypes.find(t => (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.isInArray)(t, options.particles.shape.type))) {\n      const shapeOptions = validTypes.map(t => options.particles.shape.options[t]).find(t => !!t),\n        promises = [];\n      (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.executeOnSingleOrMultiple)(shapeOptions, shape => {\n        promises.push((0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.loadFont)(shape.font, shape.weight));\n      });\n      await Promise.all(promises);\n    }\n  }\n  particleInit(container, particle) {\n    if (!particle.shape || !this.validTypes.includes(particle.shape)) {\n      return;\n    }\n    const character = particle.shapeData;\n    if (character === undefined) {\n      return;\n    }\n    const textData = character.value;\n    if (textData === undefined) {\n      return;\n    }\n    particle.text = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(textData, particle.randomIndexData);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/shape-text/./dist/browser/TextDrawer.js?");

/***/ }),

/***/ "./dist/browser/Utils.js":
/*!*******************************!*\
  !*** ./dist/browser/Utils.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   drawText: () => (/* binding */ drawText)\n/* harmony export */ });\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tsparticles/engine */ \"@tsparticles/engine\");\n/* harmony import */ var _tsparticles_engine__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__);\n\nconst double = 2,\n  half = 0.5;\nfunction drawText(data) {\n  const {\n      context,\n      particle,\n      radius,\n      opacity\n    } = data,\n    character = particle.shapeData;\n  if (!character) {\n    return;\n  }\n  const textData = character.value;\n  if (textData === undefined) {\n    return;\n  }\n  if (particle.text === undefined) {\n    particle.text = (0,_tsparticles_engine__WEBPACK_IMPORTED_MODULE_0__.itemFromSingleOrMultiple)(textData, particle.randomIndexData);\n  }\n  const text = particle.text,\n    style = character.style ?? \"\",\n    weight = character.weight ?? \"400\",\n    size = Math.round(radius) * double,\n    font = character.font ?? \"Verdana\",\n    fill = particle.shapeFill;\n  const lines = text?.split(\"\\n\");\n  if (!lines) {\n    return;\n  }\n  context.font = `${style} ${weight} ${size}px \"${font}\"`;\n  context.globalAlpha = opacity;\n  for (let i = 0; i < lines.length; i++) {\n    drawLine(context, lines[i], radius, opacity, i, fill);\n  }\n  context.globalAlpha = 1;\n}\nfunction drawLine(context, line, radius, opacity, index, fill) {\n  const offsetX = line.length * radius * half,\n    pos = {\n      x: -offsetX,\n      y: radius * half\n    },\n    diameter = radius * double;\n  if (fill) {\n    context.fillText(line, pos.x, pos.y + diameter * index);\n  } else {\n    context.strokeText(line, pos.x, pos.y + diameter * index);\n  }\n}\n\n//# sourceURL=webpack://@tsparticles/shape-text/./dist/browser/Utils.js?");

/***/ }),

/***/ "./dist/browser/index.js":
/*!*******************************!*\
  !*** ./dist/browser/index.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   loadTextShape: () => (/* binding */ loadTextShape)\n/* harmony export */ });\n/* harmony import */ var _TextDrawer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./TextDrawer.js */ \"./dist/browser/TextDrawer.js\");\n\nasync function loadTextShape(engine, refresh = true) {\n  engine.checkVersion(\"3.8.1\");\n  await engine.addShape(new _TextDrawer_js__WEBPACK_IMPORTED_MODULE_0__.TextDrawer(), refresh);\n}\n\n//# sourceURL=webpack://@tsparticles/shape-text/./dist/browser/index.js?");

/***/ }),

/***/ "@tsparticles/engine":
/*!*********************************************************************************************************************************!*\
  !*** external {"commonjs":"@tsparticles/engine","commonjs2":"@tsparticles/engine","amd":"@tsparticles/engine","root":"window"} ***!
  \*********************************************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE__tsparticles_engine__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./dist/browser/index.js");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});