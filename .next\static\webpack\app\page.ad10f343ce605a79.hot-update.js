"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code,GraduationCap,Heart,Mail,MapPin,Phone!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nfunction AboutSection() {\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const ctx = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.context({\n                \"AboutSection.useEffect.ctx\": ()=>{\n                    // Animate cards on scroll\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo('.about-card', {\n                        opacity: 0,\n                        y: 50,\n                        scale: 0.9\n                    }, {\n                        opacity: 1,\n                        y: 0,\n                        scale: 1,\n                        duration: 0.8,\n                        stagger: 0.2,\n                        ease: 'power2.out',\n                        scrollTrigger: {\n                            trigger: '.about-cards',\n                            start: 'top 80%',\n                            end: 'bottom 20%',\n                            toggleActions: 'play none none reverse'\n                        }\n                    });\n                    // Animate text elements\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo('.about-text', {\n                        opacity: 0,\n                        x: -50\n                    }, {\n                        opacity: 1,\n                        x: 0,\n                        duration: 1,\n                        ease: 'power2.out',\n                        scrollTrigger: {\n                            trigger: '.about-content',\n                            start: 'top 80%',\n                            toggleActions: 'play none none reverse'\n                        }\n                    });\n                }\n            }[\"AboutSection.useEffect.ctx\"], sectionRef);\n            return ({\n                \"AboutSection.useEffect\": ()=>ctx.revert()\n            })[\"AboutSection.useEffect\"];\n        }\n    }[\"AboutSection.useEffect\"], []);\n    const personalInfo = [\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: 'Location',\n            value: 'Bengaluru, India'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'Phone',\n            value: '+91 8319130513'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'Email',\n            value: '<EMAIL>'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Education',\n            value: 'B.Tech (IT) - Bhilai Institute Of Technology'\n        }\n    ];\n    const highlights = [\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            title: 'Full Stack Development',\n            description: 'Expertise in React.js, Node.js, Express.js, and MongoDB for building complete web applications.'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            title: 'Passionate Learner',\n            description: 'Always eager to learn new technologies and stay updated with the latest industry trends.'\n        },\n        {\n            icon: _barrel_optimize_names_Code_GraduationCap_Heart_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: 'Fresh Graduate',\n            description: 'Recent B.Tech IT graduate with hands-on experience through projects and training.'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        ref: sectionRef,\n        className: \"min-h-screen py-20 px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"text-center mb-20\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-blue-600 bg-clip-text text-transparent font-orbitron mb-8\",\n                            children: \"About Me\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"Get to know more about my background, skills, and passion for technology\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center about-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"about-text\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-3xl font-bold text-neon-blue mb-6 font-space\",\n                                    children: \"Hello! I'm Saurabh Dahariya\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 mb-8 leading-relaxed\",\n                                    children: \"I'm a passionate Full Stack Developer with a strong foundation in modern web technologies. As a recent B.Tech IT graduate from Bhilai Institute Of Technology, I bring fresh perspectives and enthusiasm to every project I work on.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-300 mb-8 leading-relaxed\",\n                                    children: \"My journey in web development started during my college years, and I've been continuously learning and building projects to enhance my skills. I'm particularly passionate about creating responsive, user-friendly applications that solve real-world problems.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid sm:grid-cols-2 gap-4\",\n                                    children: personalInfo.map((info, index)=>{\n                                        const Icon = info.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            className: \"glass p-4 rounded-lg hover:glow-box transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.02\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"text-neon-blue\",\n                                                        size: 20\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: info.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-white font-medium\",\n                                                                children: info.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"about-cards\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-neon-green mb-8 font-space\",\n                                    children: \"What Makes Me Unique\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: highlights.map((highlight, index)=>{\n                                        const Icon = highlight.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                            className: \"about-card glass-strong p-6 rounded-xl hover:glow-box transition-all duration-300\",\n                                            whileHover: {\n                                                scale: 1.02,\n                                                y: -5\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-gradient-to-r from-neon-blue to-neon-green rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"text-black\",\n                                                            size: 24\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xl font-semibold text-white mb-2\",\n                                                                children: highlight.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 leading-relaxed\",\n                                                                children: highlight.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                    className: \"mt-16 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-strong p-8 rounded-2xl max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-neon-purple mb-4 font-space\",\n                                children: \"Current Training\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-300 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"MERN Stack Development\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" at JSpider BTM Layout, Bengaluru\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400\",\n                                children: \"September 2024 - February 2025\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-300 mt-4\",\n                                children: \"Intensive training program focusing on MongoDB, Express.js, React.js, and Node.js to build full-stack web applications.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\portfolio\\\\mordern-portfolio-saurabh\\\\src\\\\components\\\\sections\\\\AboutSection.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(AboutSection, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = AboutSection;\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/AboutSection.tsx\n"));

/***/ })

});