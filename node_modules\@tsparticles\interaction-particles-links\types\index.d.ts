import { type Engine } from "@tsparticles/engine";
export declare function loadParticlesLinksInteraction(engine: Engine, refresh?: boolean): Promise<void>;
export * from "./Options/Classes/Links.js";
export * from "./Options/Classes/LinksShadow.js";
export * from "./Options/Classes/LinksTriangle.js";
export * from "./Options/Interfaces/ILinks.js";
export * from "./Options/Interfaces/ILinksShadow.js";
export * from "./Options/Interfaces/ILinksTriangle.js";
