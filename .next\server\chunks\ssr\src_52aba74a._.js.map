{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/SplashScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { gsap } from 'gsap';\n\ninterface SplashScreenProps {\n  onComplete: () => void;\n}\n\nexport default function SplashScreen({ onComplete }: SplashScreenProps) {\n  const [currentStep, setCurrentStep] = useState(0);\n  const [animationComplete, setAnimationComplete] = useState(false);\n\n  const steps = [\n    \"Hello World\",\n    \"I am <PERSON><PERSON><PERSON><PERSON>\",\n    \"A Full Stack Software Developer\"\n  ];\n\n  useEffect(() => {\n    const timeline = gsap.timeline();\n\n    // Step 1: Hello World (2s)\n    timeline\n      .to({}, { duration: 2 })\n      .call(() => setCurrentStep(1))\n      // Step 2: I am <PERSON><PERSON><PERSON><PERSON> (2.5s)\n      .to({}, { duration: 2.5 })\n      .call(() => setCurrentStep(2))\n      // Step 3: A Full Stack Software Developer (2.5s)\n      .to({}, { duration: 2.5 })\n      .call(() => {\n        setAnimationComplete(true);\n        setTimeout(onComplete, 800);\n      });\n\n    return () => {\n      timeline.kill();\n    };\n  }, [onComplete]);\n\n  const typewriterVariants = {\n    hidden: { width: 0 },\n    visible: {\n      width: \"100%\",\n      transition: {\n        duration: 1.5,\n        ease: \"easeInOut\",\n        delay: 0.3\n      }\n    }\n  };\n\n  const glowVariants = {\n    initial: {\n      textShadow: \"0 0 10px rgba(0, 212, 255, 0.5)\"\n    },\n    animate: {\n      textShadow: [\n        \"0 0 10px rgba(0, 212, 255, 0.5)\",\n        \"0 0 20px rgba(0, 212, 255, 0.8)\",\n        \"0 0 30px rgba(0, 212, 255, 1)\",\n        \"0 0 20px rgba(0, 212, 255, 0.8)\",\n        \"0 0 10px rgba(0, 212, 255, 0.5)\"\n      ],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  return (\n    <AnimatePresence>\n      {!animationComplete && (\n        <motion.div\n          className=\"fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black\"\n          initial={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          {/* Animated background particles */}\n          <div className=\"absolute inset-0 overflow-hidden\">\n            {[...Array(50)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-1 h-1 bg-neon-blue rounded-full\"\n                style={{\n                  left: `${Math.random() * 100}%`,\n                  top: `${Math.random() * 100}%`,\n                }}\n                animate={{\n                  opacity: [0, 1, 0],\n                  scale: [0, 1, 0],\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  delay: Math.random() * 3,\n                }}\n              />\n            ))}\n          </div>\n\n          <div className=\"text-center relative z-10\">\n            <AnimatePresence mode=\"wait\">\n              {currentStep === 0 && (\n                <motion.div\n                  key=\"hello\"\n                  initial={{ opacity: 0, y: 30 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -30 }}\n                  transition={{ duration: 0.8 }}\n                  className=\"relative\"\n                >\n                  <motion.div\n                    className=\"text-4xl md:text-6xl font-bold text-neon-blue font-space overflow-hidden whitespace-nowrap border-r-2 border-neon-blue\"\n                    variants={typewriterVariants}\n                    initial=\"hidden\"\n                    animate=\"visible\"\n                  >\n                    Hello World\n                  </motion.div>\n                </motion.div>\n              )}\n\n              {currentStep === 1 && (\n                <motion.div\n                  key=\"name\"\n                  layoutId=\"main-name\"\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 1.2 }}\n                  transition={{ duration: 1, ease: \"easeOut\" }}\n                  className=\"relative\"\n                >\n                  <motion.div\n                    className=\"text-5xl md:text-7xl font-bold gradient-text font-orbitron\"\n                    variants={glowVariants}\n                    initial=\"initial\"\n                    animate=\"animate\"\n                  >\n                    I am Saurabh Dahariya\n                  </motion.div>\n                </motion.div>\n              )}\n\n              {currentStep === 2 && (\n                <motion.div\n                  key=\"title\"\n                  initial={{ opacity: 0, y: 50 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -50 }}\n                  transition={{ duration: 1, ease: \"easeOut\" }}\n                  className=\"space-y-4\"\n                >\n                  <motion.div\n                    className=\"text-5xl md:text-7xl font-bold gradient-text font-orbitron\"\n                    variants={glowVariants}\n                    initial=\"initial\"\n                    animate=\"animate\"\n                  >\n                    I am Saurabh Dahariya\n                  </motion.div>\n                  <motion.div\n                    className=\"text-2xl md:text-3xl text-neon-green font-space\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    transition={{ delay: 0.5, duration: 1 }}\n                  >\n                    A Full Stack Software Developer\n                  </motion.div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAUe,SAAS,aAAa,EAAE,UAAU,EAAqB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,QAAQ;QACZ;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,6IAAA,CAAA,OAAI,CAAC,QAAQ;QAE9B,2BAA2B;QAC3B,SACG,EAAE,CAAC,CAAC,GAAG;YAAE,UAAU;QAAE,GACrB,IAAI,CAAC,IAAM,eAAe,GAC3B,uCAAuC;SACtC,EAAE,CAAC,CAAC,GAAG;YAAE,UAAU;QAAI,GACvB,IAAI,CAAC,IAAM,eAAe,GAC3B,iDAAiD;SAChD,EAAE,CAAC,CAAC,GAAG;YAAE,UAAU;QAAI,GACvB,IAAI,CAAC;YACJ,qBAAqB;YACrB,WAAW,YAAY;QACzB;QAEF,OAAO;YACL,SAAS,IAAI;QACf;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,qBAAqB;QACzB,QAAQ;YAAE,OAAO;QAAE;QACnB,SAAS;YACP,OAAO;YACP,YAAY;gBACV,UAAU;gBACV,MAAM;gBACN,OAAO;YACT;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS;YACP,YAAY;QACd;QACA,SAAS;YACP,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,CAAC,mCACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAChC;4BACA,SAAS;gCACP,SAAS;oCAAC;oCAAG;oCAAG;iCAAE;gCAClB,OAAO;oCAAC;oCAAG;oCAAG;iCAAE;4BAClB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,OAAO,KAAK,MAAM,KAAK;4BACzB;2BAdK;;;;;;;;;;8BAmBX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;;4BACnB,gBAAgB,mBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,UAAU;oCACV,SAAQ;oCACR,SAAQ;8CACT;;;;;;+BAZG;;;;;4BAkBP,gBAAgB,mBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAS;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAC/B,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAU;gCAC3C,WAAU;0CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,UAAU;oCACV,SAAQ;oCACR,SAAQ;8CACT;;;;;;+BAbG;;;;;4BAmBP,gBAAgB,mBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAU;gCAC3C,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAU;wCACV,SAAQ;wCACR,SAAQ;kDACT;;;;;;kDAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;4CAAK,UAAU;wCAAE;kDACvC;;;;;;;+BApBG;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BtB", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/TechLogos.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport <PERSON>que<PERSON> from 'react-fast-marquee';\n\nconst techLogos = [\n  {\n    name: 'React',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <circle cx=\"12\" cy=\"12\" r=\"2\" fill=\"#61DAFB\"/>\n        <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4Z\" fill=\"#61DAFB\"/>\n        <ellipse cx=\"12\" cy=\"12\" rx=\"8\" ry=\"3\" fill=\"none\" stroke=\"#61DAFB\" strokeWidth=\"1\"/>\n        <ellipse cx=\"12\" cy=\"12\" rx=\"8\" ry=\"3\" fill=\"none\" stroke=\"#61DAFB\" strokeWidth=\"1\" transform=\"rotate(60 12 12)\"/>\n        <ellipse cx=\"12\" cy=\"12\" rx=\"8\" ry=\"3\" fill=\"none\" stroke=\"#61DAFB\" strokeWidth=\"1\" transform=\"rotate(120 12 12)\"/>\n      </svg>\n    ),\n    color: '#61DAFB'\n  },\n  {\n    name: 'Node.js',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <path d=\"M12,1.85C11.73,1.85 11.45,1.92 11.22,2.05L3.78,6.35C3.32,6.61 3.05,7.11 3.05,7.65V16.35C3.05,16.89 3.32,17.39 3.78,17.65L11.22,21.95C11.45,22.08 11.73,22.15 12,22.15C12.27,22.15 12.55,22.08 12.78,21.95L20.22,17.65C20.68,17.39 20.95,16.89 20.95,16.35V7.65C20.95,7.11 20.68,6.61 20.22,6.35L12.78,2.05C12.55,1.92 12.27,1.85 12,1.85M12,3.05L19,7V17L12,21L5,17V7L12,3.05Z\" fill=\"#339933\"/>\n      </svg>\n    ),\n    color: '#339933'\n  },\n  {\n    name: 'JavaScript',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <rect width=\"24\" height=\"24\" rx=\"3\" fill=\"#F7DF1E\"/>\n        <path d=\"M7.5,16.5V14.5H9V16.5C9,17.33 9.67,18 10.5,18C11.33,18 12,17.33 12,16.5V10H13.5V16.5C13.5,18.16 12.16,19.5 10.5,19.5C8.84,19.5 7.5,18.16 7.5,16.5M15,14.5H16.5V15.5H17.5V14H15V12.5H19V16H17.5V15H16.5V16.5H19V18H15V14.5Z\" fill=\"#000\"/>\n      </svg>\n    ),\n    color: '#F7DF1E'\n  },\n  {\n    name: 'MongoDB',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12.5,17V19H11.5V17C10.9,16.9 10.4,16.6 10,16.2L11.1,15.1C11.4,15.4 11.7,15.5 12,15.5C12.6,15.5 13,15.1 13,14.5V9.5C13,8.9 12.6,8.5 12,8.5C11.4,8.5 11,8.9 11,9.5V14.5C11,15.9 12.1,17 13.5,17H12.5Z\" fill=\"#47A248\"/>\n      </svg>\n    ),\n    color: '#47A248'\n  },\n  {\n    name: 'HTML5',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <path d=\"M3.5,2L4.8,20L12,22L19.2,20L20.5,2H3.5M17.5,8H8.5L8.8,11H17.2L16.5,17L12,18.5L7.5,17L7.2,14H10.2L10.4,15.5L12,16L13.6,15.5L13.8,13H7L6.5,8H17.5V8Z\" fill=\"#E34F26\"/>\n      </svg>\n    ),\n    color: '#E34F26'\n  },\n  {\n    name: 'CSS3',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <path d=\"M3.5,2L4.8,20L12,22L19.2,20L20.5,2H3.5M16.5,8H8.5L8.8,11H16.2L15.5,17L12,18.5L8.5,17L8.2,14H11.2L11.4,15.5L12,16L12.6,15.5L12.8,13H8L7.5,8H16.5V8Z\" fill=\"#1572B6\"/>\n      </svg>\n    ),\n    color: '#1572B6'\n  },\n  {\n    name: 'Git',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <path d=\"M21.62,11.13L12.87,2.38C12.5,2 12,2 11.62,2.38L9.87,4.13L12.5,6.75C13,6.5 13.62,6.5 14.12,7C14.62,7.5 14.62,8.12 14.12,8.62C13.62,9.12 13,9.12 12.5,8.62L10,6.12V17.87C10.5,18.12 11,18.62 11,19.25C11,20.12 10.37,20.75 9.5,20.75C8.62,20.75 8,20.12 8,19.25C8,18.62 8.5,18.12 9,17.87V6.12L7.5,4.62L2.38,9.75C2,10.12 2,10.62 2.38,11L11.13,19.75C11.5,20.12 12,20.12 12.38,19.75L21.62,11.5C22,11.12 22,10.62 21.62,11.13Z\" fill=\"#F05032\"/>\n      </svg>\n    ),\n    color: '#F05032'\n  },\n  {\n    name: 'Express.js',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <path d=\"M24,18.588A1.529,1.529,0,0,1,22.471,20.117H1.529A1.529,1.529,0,0,1,0,18.588V5.412A1.529,1.529,0,0,1,1.529,3.883H22.471A1.529,1.529,0,0,1,24,5.412ZM22.471,5.412H1.529V18.588H22.471Z\" fill=\"#000\"/>\n        <path d=\"M5.764,13.588L8.235,8.412H9.765L6.706,14.824H4.824L1.765,8.412H3.294Z\" fill=\"#000\"/>\n        <path d=\"M13.588,8.412V14.824H12.059V8.412Z\" fill=\"#000\"/>\n        <path d=\"M18.824,8.412V14.824H17.294V8.412Z\" fill=\"#000\"/>\n      </svg>\n    ),\n    color: '#000000'\n  },\n  {\n    name: 'Bootstrap',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <path d=\"M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6A2,2 0 0,0 20,4M20,18H4V6H20V18M13.5,8H10V16H13.5A2.5,2.5 0 0,0 16,13.5A2.5,2.5 0 0,0 13.5,11A2.5,2.5 0 0,0 16,8.5A2.5,2.5 0 0,0 13.5,8M13.5,9.5A1,1 0 0,1 14.5,10.5A1,1 0 0,1 13.5,11.5H11.5V9.5H13.5M13.5,13A1,1 0 0,1 14.5,14A1,1 0 0,1 13.5,15H11.5V13H13.5Z\" fill=\"#7952B3\"/>\n      </svg>\n    ),\n    color: '#7952B3'\n  },\n  {\n    name: 'Tailwind CSS',\n    icon: (\n      <svg viewBox=\"0 0 24 24\" className=\"w-12 h-12\">\n        <path d=\"M12,2C8,2 5.5,4 4.5,8C6,6 7.75,5.25 9.75,5.75C10.6,5.95 11.2,6.55 11.85,7.2C12.95,8.3 14.25,9.5 17,9.5C21,9.5 23.5,7.5 24.5,3.5C23,5.5 21.25,6.25 19.25,5.75C18.4,5.55 17.8,4.95 17.15,4.3C16.05,3.2 14.75,2 12,2M4.5,9.5C0.5,9.5 -2,11.5 -3,15.5C-1.5,13.5 0.25,12.75 2.25,13.25C3.1,13.45 3.7,14.05 4.35,14.7C5.45,15.8 6.75,17 9.5,17C13.5,17 16,15 17,11C15.5,13 13.75,13.75 11.75,13.25C10.9,13.05 10.3,12.45 9.65,11.8C8.55,10.7 7.25,9.5 4.5,9.5Z\" fill=\"#06B6D4\"/>\n      </svg>\n    ),\n    color: '#06B6D4'\n  }\n];\n\ninterface TechLogosProps {\n  direction?: 'left' | 'right';\n  speed?: number;\n  className?: string;\n}\n\nexport default function TechLogos({ direction = 'left', speed = 50, className = '' }: TechLogosProps) {\n  return (\n    <div className={`py-8 ${className}`}>\n      <Marquee\n        direction={direction}\n        speed={speed}\n        gradient={false}\n        pauseOnHover={true}\n        className=\"overflow-hidden\"\n      >\n        {techLogos.map((tech, index) => (\n          <motion.div\n            key={index}\n            className=\"mx-8 flex flex-col items-center group cursor-pointer\"\n            whileHover={{ scale: 1.1, y: -5 }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"p-4 glass rounded-xl group-hover:glow-box transition-all duration-300 grayscale group-hover:grayscale-0\">\n              <div style={{ color: tech.color }}>\n                {tech.icon}\n              </div>\n            </div>\n            <span className=\"mt-2 text-sm font-medium text-gray-400 group-hover:text-white transition-colors duration-300\">\n              {tech.name}\n            </span>\n          </motion.div>\n        ))}\n      </Marquee>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,YAAY;IAChB;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;;8BACjC,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAI,MAAK;;;;;;8BACnC,8OAAC;oBAAK,GAAE;oBAAkJ,MAAK;;;;;;8BAC/J,8OAAC;oBAAQ,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAI,IAAG;oBAAI,MAAK;oBAAO,QAAO;oBAAU,aAAY;;;;;;8BAChF,8OAAC;oBAAQ,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAI,IAAG;oBAAI,MAAK;oBAAO,QAAO;oBAAU,aAAY;oBAAI,WAAU;;;;;;8BAC9F,8OAAC;oBAAQ,IAAG;oBAAK,IAAG;oBAAK,IAAG;oBAAI,IAAG;oBAAI,MAAK;oBAAO,QAAO;oBAAU,aAAY;oBAAI,WAAU;;;;;;;;;;;;QAGlG,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;sBACjC,cAAA,8OAAC;gBAAK,GAAE;gBAAiX,MAAK;;;;;;;;;;;QAGlY,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;;8BACjC,8OAAC;oBAAK,OAAM;oBAAK,QAAO;oBAAK,IAAG;oBAAI,MAAK;;;;;;8BACzC,8OAAC;oBAAK,GAAE;oBAA6N,MAAK;;;;;;;;;;;;QAG9O,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;sBACjC,cAAA,8OAAC;gBAAK,GAAE;gBAAkR,MAAK;;;;;;;;;;;QAGnS,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;sBACjC,cAAA,8OAAC;gBAAK,GAAE;gBAAqJ,MAAK;;;;;;;;;;;QAGtK,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;sBACjC,cAAA,8OAAC;gBAAK,GAAE;gBAAqJ,MAAK;;;;;;;;;;;QAGtK,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;sBACjC,cAAA,8OAAC;gBAAK,GAAE;gBAAga,MAAK;;;;;;;;;;;QAGjb,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;;8BACjC,8OAAC;oBAAK,GAAE;oBAAuL,MAAK;;;;;;8BACpM,8OAAC;oBAAK,GAAE;oBAAwE,MAAK;;;;;;8BACrF,8OAAC;oBAAK,GAAE;oBAAqC,MAAK;;;;;;8BAClD,8OAAC;oBAAK,GAAE;oBAAqC,MAAK;;;;;;;;;;;;QAGtD,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;sBACjC,cAAA,8OAAC;gBAAK,GAAE;gBAA+T,MAAK;;;;;;;;;;;QAGhV,OAAO;IACT;IACA;QACE,MAAM;QACN,oBACE,8OAAC;YAAI,SAAQ;YAAY,WAAU;sBACjC,cAAA,8OAAC;gBAAK,GAAE;gBAA2b,MAAK;;;;;;;;;;;QAG5c,OAAO;IACT;CACD;AAQc,SAAS,UAAU,EAAE,YAAY,MAAM,EAAE,QAAQ,EAAE,EAAE,YAAY,EAAE,EAAkB;IAClG,qBACE,8OAAC;QAAI,WAAW,CAAC,KAAK,EAAE,WAAW;kBACjC,cAAA,8OAAC,yJAAA,CAAA,UAAO;YACN,WAAW;YACX,OAAO;YACP,UAAU;YACV,cAAc;YACd,WAAU;sBAET,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,YAAY;wBAAE,OAAO;wBAAK,GAAG,CAAC;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,OAAO;oCAAE,OAAO,KAAK,KAAK;gCAAC;0CAC7B,KAAK,IAAI;;;;;;;;;;;sCAGd,8OAAC;4BAAK,WAAU;sCACb,KAAK,IAAI;;;;;;;mBAXP;;;;;;;;;;;;;;;AAkBjB", "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/sections/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { gsap } from 'gsap';\nimport { ChevronDown } from 'lucide-react';\nimport Typewriter from 'typewriter-effect';\nimport TechLogos from '@/components/ui/TechLogos';\n\nexport default function HeroSection() {\n  const heroRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Animate hero elements after splash screen\n      gsap.fromTo(\n        '.hero-content',\n        { opacity: 0, y: 50 },\n        { opacity: 1, y: 0, duration: 1, delay: 0.5, ease: 'power2.out' }\n      );\n\n      gsap.fromTo(\n        '.hero-subtitle',\n        { opacity: 0, y: 30 },\n        { opacity: 1, y: 0, duration: 1, delay: 1, ease: 'power2.out' }\n      );\n\n      gsap.fromTo(\n        '.scroll-indicator',\n        { opacity: 0, y: 20 },\n        { opacity: 1, y: 0, duration: 1, delay: 1.5, ease: 'power2.out' }\n      );\n    }, heroRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const scrollToNext = () => {\n    const nextSection = document.getElementById('about');\n    if (nextSection) {\n      nextSection.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section\n      ref={heroRef}\n      className=\"min-h-screen flex items-center justify-center relative pt-20 pb-10 px-4 md:px-10\"\n    >\n      <div className=\"max-w-[90vw] mx-auto text-center space-y-8\">\n        <motion.div\n          className=\"hero-content\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.5, duration: 1 }}\n        >\n          {/* Main Title */}\n          <motion.h1\n            className=\"text-5xl md:text-7xl lg:text-8xl font-bold mb-6 gradient-text font-orbitron\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 1 }}\n          >\n            Saurabh Dahariya\n          </motion.h1>\n\n          {/* Animated Subtitle */}\n          <motion.div\n            className=\"hero-subtitle text-xl md:text-3xl lg:text-4xl text-neon-blue mb-8 font-space h-16 flex items-center justify-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.2, duration: 0.8 }}\n          >\n            <Typewriter\n              options={{\n                strings: [\n                  'Full Stack Software Developer',\n                  'React.js Specialist',\n                  'Node.js Expert',\n                  'MERN Stack Developer',\n                  'Problem Solver'\n                ],\n                autoStart: true,\n                loop: true,\n                delay: 75,\n                deleteSpeed: 50,\n              }}\n            />\n          </motion.div>\n\n          {/* Description */}\n          <motion.p\n            className=\"text-lg md:text-xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.5, duration: 0.8 }}\n          >\n            Passionate B.Tech IT graduate from Bengaluru with expertise in modern web technologies.\n            I build responsive, scalable applications and love turning ideas into digital reality.\n          </motion.p>\n\n          {/* CTA Buttons */}\n          <motion.div\n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.8, duration: 0.8 }}\n          >\n            <motion.button\n              className=\"glass-strong px-8 py-4 rounded-full text-neon-blue hover:text-neon-green transition-all duration-300 glow-box magnetic font-semibold border border-neon-blue/30 hover:border-neon-green/50\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}\n            >\n              View My Work\n            </motion.button>\n\n            <motion.button\n              className=\"glass px-8 py-4 rounded-full text-white hover:text-neon-blue transition-all duration-300 magnetic font-semibold border border-white/20 hover:border-neon-blue/50\"\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}\n            >\n              Get In Touch\n            </motion.button>\n          </motion.div>\n        </motion.div>\n\n        {/* Tech Stack Logos */}\n        <motion.div\n          className=\"mt-16\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 2.2, duration: 1 }}\n        >\n          <p className=\"text-gray-400 text-sm mb-6 font-medium\">Technologies I work with</p>\n          <TechLogos speed={40} />\n        </motion.div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          className=\"scroll-indicator absolute bottom-8 left-1/2 transform -translate-x-1/2 cursor-pointer\"\n          onClick={scrollToNext}\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 2.5, duration: 0.8 }}\n        >\n          <motion.div\n            className=\"flex flex-col items-center text-neon-blue hover:text-neon-green transition-colors group\"\n            animate={{ y: [0, 8, 0] }}\n            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}\n          >\n            <span className=\"text-sm mb-2 font-medium group-hover:text-neon-green transition-colors\">Scroll Down</span>\n            <motion.div\n              className=\"p-2 rounded-full glass group-hover:glow-box transition-all duration-300\"\n              whileHover={{ scale: 1.1 }}\n            >\n              <ChevronDown size={20} />\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Floating Elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <motion.div\n          className=\"absolute top-1/4 left-1/4 w-4 h-4 bg-neon-blue rounded-full opacity-60\"\n          animate={{\n            y: [0, -20, 0],\n            opacity: [0.6, 1, 0.6],\n          }}\n          transition={{\n            duration: 4,\n            repeat: Infinity,\n            ease: 'easeInOut',\n          }}\n        />\n        <motion.div\n          className=\"absolute top-1/3 right-1/4 w-6 h-6 bg-neon-green rounded-full opacity-40\"\n          animate={{\n            y: [0, 30, 0],\n            opacity: [0.4, 0.8, 0.4],\n          }}\n          transition={{\n            duration: 5,\n            repeat: Infinity,\n            ease: 'easeInOut',\n            delay: 1,\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-1/3 left-1/3 w-3 h-3 bg-neon-purple rounded-full opacity-50\"\n          animate={{\n            y: [0, -15, 0],\n            opacity: [0.5, 1, 0.5],\n          }}\n          transition={{\n            duration: 3,\n            repeat: Infinity,\n            ease: 'easeInOut',\n            delay: 2,\n          }}\n        />\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,6IAAA,CAAA,OAAI,CAAC,OAAO,CAAC;YACvB,4CAA4C;YAC5C,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,iBACA;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBAAE,SAAS;gBAAG,GAAG;gBAAG,UAAU;gBAAG,OAAO;gBAAK,MAAM;YAAa;YAGlE,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,kBACA;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBAAE,SAAS;gBAAG,GAAG;gBAAG,UAAU;gBAAG,OAAO;gBAAG,MAAM;YAAa;YAGhE,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,qBACA;gBAAE,SAAS;gBAAG,GAAG;YAAG,GACpB;gBAAE,SAAS;gBAAG,GAAG;gBAAG,UAAU;gBAAG,OAAO;gBAAK,MAAM;YAAa;QAEpE,GAAG;QAEH,OAAO,IAAM,IAAI,MAAM;IACzB,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,IAAI,aAAa;YACf,YAAY,cAAc,CAAC;gBAAE,UAAU;YAAS;QAClD;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAE;;0CAGtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAE;0CACvC;;;;;;0CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;0CAExC,cAAA,8OAAC,qJAAA,CAAA,UAAU;oCACT,SAAS;wCACP,SAAS;4CACP;4CACA;4CACA;4CACA;4CACA;yCACD;wCACD,WAAW;wCACX,MAAM;wCACN,OAAO;wCACP,aAAa;oCACf;;;;;;;;;;;0CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;0CACzC;;;;;;0CAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;oCAAK,UAAU;gCAAI;;kDAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAM,GAAG,CAAC;wCAAE;wCACjC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,SAAS,cAAc,CAAC,aAAa,eAAe;gDAAE,UAAU;4CAAS;kDACzF;;;;;;kDAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAM,GAAG,CAAC;wCAAE;wCACjC,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,SAAS,cAAc,CAAC,YAAY,eAAe;gDAAE,UAAU;4CAAS;kDACxF;;;;;;;;;;;;;;;;;;kCAOL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAE;;0CAEtC,8OAAC;gCAAE,WAAU;0CAAyC;;;;;;0CACtD,8OAAC,qIAAA,CAAA,UAAS;gCAAC,OAAO;;;;;;;;;;;;kCAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;kCAExC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAG;iCAAE;4BAAC;4BACxB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;gCAAU,MAAM;4BAAY;;8CAE/D,8OAAC;oCAAK,WAAU;8CAAyE;;;;;;8CACzF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAI;8CAEzB,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;kCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/sections/AboutSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { MapPin, Phone, Mail, GraduationCap, Code, Heart } from 'lucide-react';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function AboutSection() {\n  const sectionRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Animate cards on scroll\n      gsap.fromTo(\n        '.about-card',\n        { opacity: 0, y: 50, scale: 0.9 },\n        {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.8,\n          stagger: 0.2,\n          ease: 'power2.out',\n          scrollTrigger: {\n            trigger: '.about-cards',\n            start: 'top 80%',\n            end: 'bottom 20%',\n            toggleActions: 'play none none reverse',\n          },\n        }\n      );\n\n      // Animate text elements\n      gsap.fromTo(\n        '.about-text',\n        { opacity: 0, x: -50 },\n        {\n          opacity: 1,\n          x: 0,\n          duration: 1,\n          ease: 'power2.out',\n          scrollTrigger: {\n            trigger: '.about-content',\n            start: 'top 80%',\n            toggleActions: 'play none none reverse',\n          },\n        }\n      );\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const personalInfo = [\n    { icon: MapPin, label: 'Location', value: 'Bengaluru, India' },\n    { icon: Phone, label: 'Phone', value: '+91 8319130513' },\n    { icon: Mail, label: 'Email', value: '<EMAIL>' },\n    { icon: GraduationCap, label: 'Education', value: 'B.Tech (IT) - Bhilai Institute Of Technology' },\n  ];\n\n  const highlights = [\n    {\n      icon: Code,\n      title: 'Full Stack Development',\n      description: 'Expertise in React.js, Node.js, Express.js, and MongoDB for building complete web applications.',\n    },\n    {\n      icon: Heart,\n      title: 'Passionate Learner',\n      description: 'Always eager to learn new technologies and stay updated with the latest industry trends.',\n    },\n    {\n      icon: GraduationCap,\n      title: 'Fresh Graduate',\n      description: 'Recent B.Tech IT graduate with hands-on experience through projects and training.',\n    },\n  ];\n\n  return (\n    <section\n      id=\"about\"\n      ref={sectionRef}\n      className=\"min-h-screen py-20 px-4 sm:px-6 lg:px-8\"\n    >\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6\">\n            About Me\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Get to know more about my background, skills, and passion for technology\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center about-content\">\n          {/* Left Side - Personal Info */}\n          <div className=\"about-text\">\n            <h3 className=\"text-3xl font-bold text-neon-blue mb-6 font-space\">\n              Hello! I'm Saurabh Dahariya\n            </h3>\n            \n            <p className=\"text-lg text-gray-300 mb-8 leading-relaxed\">\n              I'm a passionate Full Stack Developer with a strong foundation in modern web technologies. \n              As a recent B.Tech IT graduate from Bhilai Institute Of Technology, I bring fresh perspectives \n              and enthusiasm to every project I work on.\n            </p>\n\n            <p className=\"text-lg text-gray-300 mb-8 leading-relaxed\">\n              My journey in web development started during my college years, and I've been continuously \n              learning and building projects to enhance my skills. I'm particularly passionate about \n              creating responsive, user-friendly applications that solve real-world problems.\n            </p>\n\n            {/* Personal Information Cards */}\n            <div className=\"grid sm:grid-cols-2 gap-4\">\n              {personalInfo.map((info, index) => {\n                const Icon = info.icon;\n                return (\n                  <motion.div\n                    key={index}\n                    className=\"glass p-4 rounded-lg hover:glow-box transition-all duration-300\"\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <Icon className=\"text-neon-blue\" size={20} />\n                      <div>\n                        <p className=\"text-sm text-gray-400\">{info.label}</p>\n                        <p className=\"text-white font-medium\">{info.value}</p>\n                      </div>\n                    </div>\n                  </motion.div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Right Side - Highlights */}\n          <div className=\"about-cards\">\n            <h3 className=\"text-2xl font-bold text-neon-green mb-8 font-space\">\n              What Makes Me Unique\n            </h3>\n            \n            <div className=\"space-y-6\">\n              {highlights.map((highlight, index) => {\n                const Icon = highlight.icon;\n                return (\n                  <motion.div\n                    key={index}\n                    className=\"about-card glass-strong p-6 rounded-xl hover:glow-box transition-all duration-300\"\n                    whileHover={{ scale: 1.02, y: -5 }}\n                  >\n                    <div className=\"flex items-start space-x-4\">\n                      <div className=\"p-3 bg-gradient-to-r from-neon-blue to-neon-green rounded-lg\">\n                        <Icon className=\"text-black\" size={24} />\n                      </div>\n                      <div>\n                        <h4 className=\"text-xl font-semibold text-white mb-2\">\n                          {highlight.title}\n                        </h4>\n                        <p className=\"text-gray-300 leading-relaxed\">\n                          {highlight.description}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Training Section */}\n        <motion.div\n          className=\"mt-16 text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"glass-strong p-8 rounded-2xl max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-neon-purple mb-4 font-space\">\n              Current Training\n            </h3>\n            <p className=\"text-lg text-gray-300 mb-4\">\n              <strong>MERN Stack Development</strong> at JSpider BTM Layout, Bengaluru\n            </p>\n            <p className=\"text-gray-400\">\n              September 2024 - February 2025\n            </p>\n            <p className=\"text-gray-300 mt-4\">\n              Intensive training program focusing on MongoDB, Express.js, React.js, and Node.js \n              to build full-stack web applications.\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,6IAAA,CAAA,OAAI,CAAC,OAAO,CAAC;YACvB,0BAA0B;YAC1B,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,eACA;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAI,GAChC;gBACE,SAAS;gBACT,GAAG;gBACH,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,KAAK;oBACL,eAAe;gBACjB;YACF;YAGF,wBAAwB;YACxB,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,eACA;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG,GACrB;gBACE,SAAS;gBACT,GAAG;gBACH,UAAU;gBACV,MAAM;gBACN,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,eAAe;gBACjB;YACF;QAEJ,GAAG;QAEH,OAAO,IAAM,IAAI,MAAM;IACzB,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB;YAAE,MAAM,0MAAA,CAAA,SAAM;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC7D;YAAE,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;YAAS,OAAO;QAAiB;QACvD;YAAE,MAAM,kMAAA,CAAA,OAAI;YAAE,OAAO;YAAS,OAAO;QAAgC;QACrE;YAAE,MAAM,wNAAA,CAAA,gBAAa;YAAE,OAAO;YAAa,OAAO;QAA+C;KAClG;IAED,MAAM,aAAa;QACjB;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QACC,IAAG;QACH,KAAK;QACL,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAkE;;;;;;sCAGhF,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAIlE,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAM1D,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAO1D,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,MAAM;wCACvB,MAAM,OAAO,KAAK,IAAI;wCACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;sDAE1B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;wDAAiB,MAAM;;;;;;kEACvC,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAyB,KAAK,KAAK;;;;;;0EAChD,8OAAC;gEAAE,WAAU;0EAA0B,KAAK,KAAK;;;;;;;;;;;;;;;;;;2CARhD;;;;;oCAaX;;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAInE,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW;wCAC1B,MAAM,OAAO,UAAU,IAAI;wCAC3B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,YAAY;gDAAE,OAAO;gDAAM,GAAG,CAAC;4CAAE;sDAEjC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;4DAAa,MAAM;;;;;;;;;;;kEAErC,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EACX,UAAU,KAAK;;;;;;0EAElB,8OAAC;gEAAE,WAAU;0EACV,UAAU,WAAW;;;;;;;;;;;;;;;;;;2CAbvB;;;;;oCAmBX;;;;;;;;;;;;;;;;;;8BAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;kDAAO;;;;;;oCAA+B;;;;;;;0CAEzC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAG7B,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 1511, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/sections/TechStackSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport TechLogos from '@/components/ui/TechLogos';\n\ngsap.registerPlugin(ScrollTrigger);\n\nexport default function TechStackSection() {\n  const sectionRef = useRef<HTMLDivElement>(null);\n\n  const techStack = {\n    frontend: [\n      { name: 'React.js', color: '#61DAFB', level: 90 },\n      { name: 'HTML5', color: '#E34F26', level: 95 },\n      { name: 'CSS3', color: '#1572B6', level: 90 },\n      { name: 'JavaScript', color: '#F7DF1E', level: 85 },\n      { name: 'Bootstrap', color: '#7952B3', level: 80 },\n    ],\n    backend: [\n      { name: 'Node.js', color: '#339933', level: 85 },\n      { name: 'Express.js', color: '#000000', level: 80 },\n      { name: 'MongoDB', color: '#47A248', level: 75 },\n      { name: 'Mongoose', color: '#880000', level: 75 },\n    ],\n    tools: [\n      { name: 'Git', color: '#F05032', level: 85 },\n      { name: 'GitHub', color: '#181717', level: 85 },\n      { name: 'JIRA', color: '#0052CC', level: 70 },\n      { name: 'Postman', color: '#FF6C37', level: 80 },\n      { name: 'Swagger', color: '#85EA2D', level: 70 },\n    ],\n    familiar: [\n      { name: 'CI/CD Pipelines', color: '#326CE5', level: 60 },\n      { name: 'Docker', color: '#2496ED', level: 55 },\n      { name: 'AWS', color: '#FF9900', level: 50 },\n    ],\n  };\n\n  const marqueeItems = [\n    'React.js', 'Node.js', 'Express.js', 'MongoDB', 'JavaScript', 'HTML5', 'CSS3', \n    'Bootstrap', 'Git', 'GitHub', 'JIRA', 'Postman', 'Swagger', 'Docker', 'AWS'\n  ];\n\n  useEffect(() => {\n    const ctx = gsap.context(() => {\n      // Animate skill bars\n      gsap.fromTo(\n        '.skill-bar-fill',\n        { width: '0%' },\n        {\n          width: (i, el) => el.getAttribute('data-level') + '%',\n          duration: 1.5,\n          ease: 'power2.out',\n          stagger: 0.1,\n          scrollTrigger: {\n            trigger: '.skills-container',\n            start: 'top 80%',\n            toggleActions: 'play none none reverse',\n          },\n        }\n      );\n\n      // Animate category cards\n      gsap.fromTo(\n        '.tech-category',\n        { opacity: 0, y: 50, scale: 0.9 },\n        {\n          opacity: 1,\n          y: 0,\n          scale: 1,\n          duration: 0.8,\n          stagger: 0.2,\n          ease: 'power2.out',\n          scrollTrigger: {\n            trigger: '.skills-container',\n            start: 'top 80%',\n            toggleActions: 'play none none reverse',\n          },\n        }\n      );\n    }, sectionRef);\n\n    return () => ctx.revert();\n  }, []);\n\n  const renderSkillCategory = (title: string, skills: typeof techStack.frontend, color: string) => (\n    <motion.div\n      className=\"tech-category glass-strong p-6 rounded-xl\"\n      whileHover={{ scale: 1.02, y: -5 }}\n      transition={{ duration: 0.3 }}\n    >\n      <h3 className=\"text-xl font-bold mb-6 font-space\" style={{ color }}>\n        {title}\n      </h3>\n      <div className=\"space-y-4\">\n        {skills.map((skill, index) => (\n          <div key={index} className=\"skill-item\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-white font-medium\">{skill.name}</span>\n              <span className=\"text-gray-400 text-sm\">{skill.level}%</span>\n            </div>\n            <div className=\"w-full bg-gray-700 rounded-full h-2 overflow-hidden\">\n              <div\n                className=\"skill-bar-fill h-full rounded-full transition-all duration-300\"\n                data-level={skill.level}\n                style={{ backgroundColor: skill.color }}\n              />\n            </div>\n          </div>\n        ))}\n      </div>\n    </motion.div>\n  );\n\n  return (\n    <section\n      id=\"tech-stack\"\n      ref={sectionRef}\n      className=\"min-h-screen py-20 px-4 sm:px-6 lg:px-8\"\n    >\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n        >\n          <h2 className=\"text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6\">\n            Tech Stack\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto\">\n            Technologies and tools I work with to bring ideas to life\n          </p>\n        </motion.div>\n\n        {/* Tech Logos Marquee */}\n        <motion.div\n          className=\"mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <TechLogos speed={60} />\n        </motion.div>\n\n        {/* Reverse Direction Marquee */}\n        <motion.div\n          className=\"mb-16\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <TechLogos direction=\"right\" speed={45} />\n        </motion.div>\n\n        {/* Skills Grid */}\n        <div className=\"skills-container grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {renderSkillCategory('Frontend', techStack.frontend, '#00d4ff')}\n          {renderSkillCategory('Backend', techStack.backend, '#00ff88')}\n          {renderSkillCategory('Tools', techStack.tools, '#8b5cf6')}\n          {renderSkillCategory('Familiar With', techStack.familiar, '#f472b6')}\n        </div>\n\n        {/* Additional Info */}\n        <motion.div\n          className=\"mt-16 text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"glass p-8 rounded-2xl max-w-4xl mx-auto\">\n            <h3 className=\"text-2xl font-bold text-neon-blue mb-4 font-space\">\n              Always Learning\n            </h3>\n            <p className=\"text-lg text-gray-300 leading-relaxed\">\n              Technology evolves rapidly, and I'm committed to staying current with the latest trends \n              and best practices. I'm always exploring new frameworks, tools, and methodologies to \n              improve my development skills and deliver better solutions.\n            </p>\n            <div className=\"flex flex-wrap justify-center gap-4 mt-6\">\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                Next.js\n              </span>\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                TypeScript\n              </span>\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                GraphQL\n              </span>\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                Microservices\n              </span>\n              <span className=\"glass px-4 py-2 rounded-full text-sm text-neon-green\">\n                Cloud Computing\n              </span>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,6IAAA,CAAA,OAAI,CAAC,cAAc,CAAC,qIAAA,CAAA,gBAAa;AAElB,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,MAAM,YAAY;QAChB,UAAU;YACR;gBAAE,MAAM;gBAAY,OAAO;gBAAW,OAAO;YAAG;YAChD;gBAAE,MAAM;gBAAS,OAAO;gBAAW,OAAO;YAAG;YAC7C;gBAAE,MAAM;gBAAQ,OAAO;gBAAW,OAAO;YAAG;YAC5C;gBAAE,MAAM;gBAAc,OAAO;gBAAW,OAAO;YAAG;YAClD;gBAAE,MAAM;gBAAa,OAAO;gBAAW,OAAO;YAAG;SAClD;QACD,SAAS;YACP;gBAAE,MAAM;gBAAW,OAAO;gBAAW,OAAO;YAAG;YAC/C;gBAAE,MAAM;gBAAc,OAAO;gBAAW,OAAO;YAAG;YAClD;gBAAE,MAAM;gBAAW,OAAO;gBAAW,OAAO;YAAG;YAC/C;gBAAE,MAAM;gBAAY,OAAO;gBAAW,OAAO;YAAG;SACjD;QACD,OAAO;YACL;gBAAE,MAAM;gBAAO,OAAO;gBAAW,OAAO;YAAG;YAC3C;gBAAE,MAAM;gBAAU,OAAO;gBAAW,OAAO;YAAG;YAC9C;gBAAE,MAAM;gBAAQ,OAAO;gBAAW,OAAO;YAAG;YAC5C;gBAAE,MAAM;gBAAW,OAAO;gBAAW,OAAO;YAAG;YAC/C;gBAAE,MAAM;gBAAW,OAAO;gBAAW,OAAO;YAAG;SAChD;QACD,UAAU;YACR;gBAAE,MAAM;gBAAmB,OAAO;gBAAW,OAAO;YAAG;YACvD;gBAAE,MAAM;gBAAU,OAAO;gBAAW,OAAO;YAAG;YAC9C;gBAAE,MAAM;gBAAO,OAAO;gBAAW,OAAO;YAAG;SAC5C;IACH;IAEA,MAAM,eAAe;QACnB;QAAY;QAAW;QAAc;QAAW;QAAc;QAAS;QACvE;QAAa;QAAO;QAAU;QAAQ;QAAW;QAAW;QAAU;KACvE;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,6IAAA,CAAA,OAAI,CAAC,OAAO,CAAC;YACvB,qBAAqB;YACrB,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,mBACA;gBAAE,OAAO;YAAK,GACd;gBACE,OAAO,CAAC,GAAG,KAAO,GAAG,YAAY,CAAC,gBAAgB;gBAClD,UAAU;gBACV,MAAM;gBACN,SAAS;gBACT,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,eAAe;gBACjB;YACF;YAGF,yBAAyB;YACzB,6IAAA,CAAA,OAAI,CAAC,MAAM,CACT,kBACA;gBAAE,SAAS;gBAAG,GAAG;gBAAI,OAAO;YAAI,GAChC;gBACE,SAAS;gBACT,GAAG;gBACH,OAAO;gBACP,UAAU;gBACV,SAAS;gBACT,MAAM;gBACN,eAAe;oBACb,SAAS;oBACT,OAAO;oBACP,eAAe;gBACjB;YACF;QAEJ,GAAG;QAEH,OAAO,IAAM,IAAI,MAAM;IACzB,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC,OAAe,QAAmC,sBAC7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,YAAY;gBAAE,OAAO;gBAAM,GAAG,CAAC;YAAE;YACjC,YAAY;gBAAE,UAAU;YAAI;;8BAE5B,8OAAC;oBAAG,WAAU;oBAAoC,OAAO;wBAAE;oBAAM;8BAC9D;;;;;;8BAEH,8OAAC;oBAAI,WAAU;8BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA0B,MAAM,IAAI;;;;;;sDACpD,8OAAC;4CAAK,WAAU;;gDAAyB,MAAM,KAAK;gDAAC;;;;;;;;;;;;;8CAEvD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,cAAY,MAAM,KAAK;wCACvB,OAAO;4CAAE,iBAAiB,MAAM,KAAK;wCAAC;;;;;;;;;;;;2BATlC;;;;;;;;;;;;;;;;IAkBlB,qBACE,8OAAC;QACC,IAAG;QACH,KAAK;QACL,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAkE;;;;;;sCAGhF,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC,qIAAA,CAAA,UAAS;wBAAC,OAAO;;;;;;;;;;;8BAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC,qIAAA,CAAA,UAAS;wBAAC,WAAU;wBAAQ,OAAO;;;;;;;;;;;8BAItC,8OAAC;oBAAI,WAAU;;wBACZ,oBAAoB,YAAY,UAAU,QAAQ,EAAE;wBACpD,oBAAoB,WAAW,UAAU,OAAO,EAAE;wBAClD,oBAAoB,SAAS,UAAU,KAAK,EAAE;wBAC9C,oBAAoB,iBAAiB,UAAU,QAAQ,EAAE;;;;;;;8BAI5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAKrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;kDAGvE,8OAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrF", "debugId": null}}, {"offset": {"line": 2000, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport SplashScreen from '@/components/ui/SplashScreen';\nimport HeroSection from '@/components/sections/HeroSection';\nimport AboutSection from '@/components/sections/AboutSection';\nimport TechStackSection from '@/components/sections/TechStackSection';\n\nexport default function Home() {\n  const [showSplash, setShowSplash] = useState(true);\n\n  const handleSplashComplete = () => {\n    setShowSplash(false);\n  };\n\n  return (\n    <>\n      {showSplash && <SplashScreen onComplete={handleSplashComplete} />}\n\n      {!showSplash && (\n        <div className=\"min-h-screen\">\n          <HeroSection />\n          <AboutSection />\n          <TechStackSection />\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,uBAAuB;QAC3B,cAAc;IAChB;IAEA,qBACE;;YACG,4BAAc,8OAAC,wIAAA,CAAA,UAAY;gBAAC,YAAY;;;;;;YAExC,CAAC,4BACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6IAAA,CAAA,UAAW;;;;;kCACZ,8OAAC,8IAAA,CAAA,UAAY;;;;;kCACb,8OAAC,kJAAA,CAAA,UAAgB;;;;;;;;;;;;;AAK3B", "debugId": null}}]}