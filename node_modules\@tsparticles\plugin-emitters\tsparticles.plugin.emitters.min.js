/*! For license information please see tsparticles.plugin.emitters.min.js.LICENSE.txt */
!function(t,i){if("object"==typeof exports&&"object"==typeof module)module.exports=i(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],i);else{var e="object"==typeof exports?i(require("@tsparticles/engine")):i(t.window);for(var s in e)("object"==typeof exports?exports:t)[s]=e[s]}}(this,(t=>(()=>{var i={303:i=>{i.exports=t}},e={};function s(t){var o=e[t];if(void 0!==o)return o.exports;var n=e[t]={exports:{}};return i[t](n,n.exports,s),n.exports}s.d=(t,i)=>{for(var e in i)s.o(i,e)&&!s.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:i[e]})},s.o=(t,i)=>Object.prototype.hasOwnProperty.call(t,i),s.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var o={};s.r(o),s.d(o,{EmitterClickMode:()=>n,EmitterShapeBase:()=>g,loadEmittersPlugin:()=>w});var n,a=s(303);class r{constructor(){this.wait=!1}load(t){(0,a.isNull)(t)||(void 0!==t.count&&(this.count=t.count),void 0!==t.delay&&(this.delay=(0,a.setRangeValue)(t.delay)),void 0!==t.duration&&(this.duration=(0,a.setRangeValue)(t.duration)),void 0!==t.wait&&(this.wait=t.wait))}}class h{constructor(){this.quantity=1,this.delay=.1}load(t){(0,a.isNull)(t)||(void 0!==t.quantity&&(this.quantity=(0,a.setRangeValue)(t.quantity)),void 0!==t.delay&&(this.delay=(0,a.setRangeValue)(t.delay)))}}class l{constructor(){this.color=!1,this.opacity=!1}load(t){(0,a.isNull)(t)||(void 0!==t.color&&(this.color=t.color),void 0!==t.opacity&&(this.opacity=t.opacity))}}class c{constructor(){this.options={},this.replace=new l,this.type="square"}load(t){(0,a.isNull)(t)||(void 0!==t.options&&(this.options=(0,a.deepExtend)({},t.options??{})),this.replace.load(t.replace),void 0!==t.type&&(this.type=t.type))}}class d{constructor(){this.mode=a.PixelMode.percent,this.height=0,this.width=0}load(t){(0,a.isNull)(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.height&&(this.height=t.height),void 0!==t.width&&(this.width=t.width))}}class p{constructor(){this.autoPlay=!0,this.fill=!0,this.life=new r,this.rate=new h,this.shape=new c,this.startCount=0}load(t){(0,a.isNull)(t)||(void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.size&&(this.size||(this.size=new d),this.size.load(t.size)),void 0!==t.direction&&(this.direction=t.direction),this.domId=t.domId,void 0!==t.fill&&(this.fill=t.fill),this.life.load(t.life),this.name=t.name,this.particles=(0,a.executeOnSingleOrMultiple)(t.particles,(t=>(0,a.deepExtend)({},t))),this.rate.load(t.rate),this.shape.load(t.shape),void 0!==t.position&&(this.position={},void 0!==t.position.x&&(this.position.x=(0,a.setRangeValue)(t.position.x)),void 0!==t.position.y&&(this.position.y=(0,a.setRangeValue)(t.position.y))),void 0!==t.spawnColor&&(void 0===this.spawnColor&&(this.spawnColor=new a.AnimatableColor),this.spawnColor.load(t.spawnColor)),void 0!==t.startCount&&(this.startCount=t.startCount))}}!function(t){t.emitter="emitter"}(n||(n={}));function u(t,i){t.color?t.color.value=i:t.color={value:i}}class m{constructor(t,i,e,s,o){this.emitters=i,this.container=e,this._destroy=()=>{this._mutationObserver?.disconnect(),this._mutationObserver=void 0,this._resizeObserver?.disconnect(),this._resizeObserver=void 0,this.emitters.removeEmitter(this),this._engine.dispatchEvent("emitterDestroyed",{container:this.container,data:{emitter:this}})},this._prepareToDie=()=>{if(this._paused)return;const t=void 0!==this.options.life?.duration?(0,a.getRangeValue)(this.options.life.duration):void 0;this.container.retina.reduceFactor&&(this._lifeCount>0||this._immortal)&&void 0!==t&&t>0&&(this._duration=t*a.millisecondsToSeconds)},this._setColorAnimation=(t,i,e,s=1)=>{const o=this.container;if(!t.enable)return i;const n=(0,a.randomInRange)(t.offset),r=(0,a.getRangeValue)(this.options.rate.delay)*a.millisecondsToSeconds/o.retina.reduceFactor;return(i+(0,a.getRangeValue)(t.speed??0)*o.fpsLimit/r+n*s)%e},this._engine=t,this._currentDuration=0,this._currentEmitDelay=0,this._currentSpawnDelay=0,this._initialPosition=o,s instanceof p?this.options=s:(this.options=new p,this.options.load(s)),this._spawnDelay=(0,a.getRangeValue)(this.options.life.delay??0)*a.millisecondsToSeconds/this.container.retina.reduceFactor,this.position=this._initialPosition??this._calcPosition(),this.name=this.options.name,this.fill=this.options.fill,this._firstSpawn=!this.options.life.wait,this._startParticlesAdded=!1;let n=(0,a.deepExtend)({},this.options.particles);if(n??={},n.move??={},n.move.direction??=this.options.direction,this.options.spawnColor&&(this.spawnColor=(0,a.rangeColorToHsl)(this._engine,this.options.spawnColor)),this._paused=!this.options.autoPlay,this._particlesOptions=n,this._size=this._calcSize(),this.size=(0,a.getSize)(this._size,this.container.canvas.size),this._lifeCount=this.options.life.count??-1,this._immortal=this._lifeCount<=0,this.options.domId){const t=document.getElementById(this.options.domId);t&&(this._mutationObserver=new MutationObserver((()=>{this.resize()})),this._resizeObserver=new ResizeObserver((()=>{this.resize()})),this._mutationObserver.observe(t,{attributes:!0,attributeFilter:["style","width","height"]}),this._resizeObserver.observe(t))}const r=this.options.shape,h=this._engine.emitterShapeManager?.getShapeGenerator(r.type);h&&(this._shape=h.generate(this.position,this.size,this.fill,r.options)),this._engine.dispatchEvent("emitterCreated",{container:e,data:{emitter:this}}),this.play()}externalPause(){this._paused=!0,this.pause()}externalPlay(){this._paused=!1,this.play()}async init(){await(this._shape?.init())}pause(){this._paused||delete this._emitDelay}play(){if(!this._paused&&this.container.retina.reduceFactor&&(this._lifeCount>0||this._immortal||!this.options.life.count)&&(this._firstSpawn||this._currentSpawnDelay>=(this._spawnDelay??0))){if(void 0===this._emitDelay){const t=(0,a.getRangeValue)(this.options.rate.delay);this._emitDelay=t*a.millisecondsToSeconds/this.container.retina.reduceFactor}(this._lifeCount>0||this._immortal)&&this._prepareToDie()}}resize(){const t=this._initialPosition;this.position=t&&(0,a.isPointInside)(t,this.container.canvas.size,a.Vector.origin)?t:this._calcPosition(),this._size=this._calcSize(),this.size=(0,a.getSize)(this._size,this.container.canvas.size),this._shape?.resize(this.position,this.size)}update(t){this._paused||(this._firstSpawn&&(this._firstSpawn=!1,this._currentSpawnDelay=this._spawnDelay??0,this._currentEmitDelay=this._emitDelay??0),this._startParticlesAdded||(this._startParticlesAdded=!0,this._emitParticles(this.options.startCount)),void 0!==this._duration&&(this._currentDuration+=t.value,this._currentDuration>=this._duration&&(this.pause(),void 0!==this._spawnDelay&&delete this._spawnDelay,this._immortal||this._lifeCount--,this._lifeCount>0||this._immortal?(this.position=this._calcPosition(),this._shape?.resize(this.position,this.size),this._spawnDelay=(0,a.getRangeValue)(this.options.life.delay??0)*a.millisecondsToSeconds/this.container.retina.reduceFactor):this._destroy(),this._currentDuration-=this._duration,delete this._duration)),void 0!==this._spawnDelay&&(this._currentSpawnDelay+=t.value,this._currentSpawnDelay>=this._spawnDelay&&(this._engine.dispatchEvent("emitterPlay",{container:this.container}),this.play(),this._currentSpawnDelay-=this._currentSpawnDelay,delete this._spawnDelay)),void 0!==this._emitDelay&&(this._currentEmitDelay+=t.value,this._currentEmitDelay>=this._emitDelay&&(this._emit(),this._currentEmitDelay-=this._emitDelay)))}_calcPosition(){if(this.options.domId){const t=document.getElementById(this.options.domId);if(t){const i=t.getBoundingClientRect(),e=this.container.retina.pixelRatio;return{x:(i.x+.5*i.width)*e,y:(i.y+.5*i.height)*e}}}return(0,a.calcPositionOrRandomFromSizeRanged)({size:this.container.canvas.size,position:this.options.position})}_calcSize(){const t=this.container;if(this.options.domId){const i=document.getElementById(this.options.domId);if(i){const e=i.getBoundingClientRect();return{width:e.width*t.retina.pixelRatio,height:e.height*t.retina.pixelRatio,mode:a.PixelMode.precise}}}return this.options.size??(()=>{const t=new d;return t.load({height:0,mode:a.PixelMode.percent,width:0}),t})()}_emit(){if(this._paused)return;const t=(0,a.getRangeValue)(this.options.rate.quantity);this._emitParticles(t)}_emitParticles(t){const i=(0,a.itemFromSingleOrMultiple)(this._particlesOptions);for(let e=0;e<t;e++){const t=(0,a.deepExtend)({},i);if(this.spawnColor){const i=this.options.spawnColor?.animation;if(i){const t={h:360,s:100,l:100},e=3.6;this.spawnColor.h=this._setColorAnimation(i.h,this.spawnColor.h,t.h,e),this.spawnColor.s=this._setColorAnimation(i.s,this.spawnColor.s,t.s),this.spawnColor.l=this._setColorAnimation(i.l,this.spawnColor.l,t.l)}u(t,this.spawnColor)}const e=this.options.shape;let s=this.position;if(this._shape){const i=this._shape.randomPosition();if(i){s=i.position;const o=e.replace;o.color&&i.color&&u(t,i.color),o.opacity&&(t.opacity?t.opacity.value=i.opacity:t.opacity={value:i.opacity})}else s=null}s&&this.container.particles.addParticle(s,t)}}}class y{constructor(t,i){this.container=i,this._engine=t,this.array=[],this.emitters=[],this.interactivityEmitters={random:{count:1,enable:!1},value:[]};i.getEmitter=t=>void 0===t||(0,a.isNumber)(t)?this.array[t??0]:this.array.find((i=>i.name===t)),i.addEmitter=async(t,i)=>this.addEmitter(t,i),i.removeEmitter=t=>{const e=i.getEmitter(t);e&&this.removeEmitter(e)},i.playEmitter=t=>{const e=i.getEmitter(t);e&&e.externalPlay()},i.pauseEmitter=t=>{const e=i.getEmitter(t);e&&e.externalPause()}}async addEmitter(t,i){const e=new p;e.load(t);const s=new m(this._engine,this,this.container,e,i);return await s.init(),this.array.push(s),s}handleClickMode(t){const i=this.emitters,e=this.interactivityEmitters;if(t!==n.emitter)return;let s;if(e&&(0,a.isArray)(e.value)){const t=0;if(e.value.length>t&&e.random.enable){s=[];const t=[];for(let i=0;i<e.random.count;i++){const o=(0,a.arrayRandomIndex)(e.value);t.includes(o)&&t.length<e.value.length?i--:(t.push(o),s.push((0,a.itemFromArray)(e.value,o)))}}else s=e.value}else s=e?.value;const o=s??i,r=this.container.interactivity.mouse.clickPosition;(0,a.executeOnSingleOrMultiple)(o,(async t=>{await this.addEmitter(t,r)}))}async init(){if(this.emitters=this.container.actualOptions.emitters,this.interactivityEmitters=this.container.actualOptions.interactivity.modes.emitters,this.emitters)if((0,a.isArray)(this.emitters))for(const t of this.emitters)await this.addEmitter(t);else await this.addEmitter(this.emitters)}pause(){for(const t of this.array)t.pause()}play(){for(const t of this.array)t.play()}removeEmitter(t){const i=this.array.indexOf(t);i>=0&&this.array.splice(i,1)}resize(){for(const t of this.array)t.resize()}stop(){this.array=[]}update(t){for(const i of this.array)i.update(t)}}class _{constructor(t){this._engine=t,this.id="emitters"}getPlugin(t){return Promise.resolve(new y(this._engine,t))}loadOptions(t,i){if(!this.needsPlugin(t)&&!this.needsPlugin(i))return;i?.emitters&&(t.emitters=(0,a.executeOnSingleOrMultiple)(i.emitters,(t=>{const i=new p;return i.load(t),i})));const e=i?.interactivity?.modes?.emitters;if(e)if((0,a.isArray)(e))t.interactivity.modes.emitters={random:{count:1,enable:!0},value:e.map((t=>{const i=new p;return i.load(t),i}))};else{const i=e;if(void 0!==i.value){const e=1;if((0,a.isArray)(i.value))t.interactivity.modes.emitters={random:{count:i.random.count??e,enable:i.random.enable??!1},value:i.value.map((t=>{const i=new p;return i.load(t),i}))};else{const s=new p;s.load(i.value),t.interactivity.modes.emitters={random:{count:i.random.count??e,enable:i.random.enable??!1},value:s}}}else{(t.interactivity.modes.emitters={random:{count:1,enable:!1},value:new p}).value.load(e)}}}needsPlugin(t){if(!t)return!1;const i=t.emitters;return(0,a.isArray)(i)&&!!i.length||void 0!==i||!!t.interactivity?.events?.onClick?.mode&&(0,a.isInArray)(n.emitter,t.interactivity.events.onClick.mode)}}const v=new Map;class f{constructor(t){this._engine=t}addShapeGenerator(t,i){this.getShapeGenerator(t)||v.set(t,i)}getShapeGenerator(t){return v.get(t)}getSupportedShapeGenerators(){return v.keys()}}class g{constructor(t,i,e,s){this.position=t,this.size=i,this.fill=e,this.options=s}resize(t,i){this.position=t,this.size=i}}async function w(t,i=!0){t.checkVersion("3.8.1"),t.emitterShapeManager||(t.emitterShapeManager=new f(t)),t.addEmitterShapeGenerator||(t.addEmitterShapeGenerator=(i,e)=>{t.emitterShapeManager?.addShapeGenerator(i,e)});const e=new _(t);await t.addPlugin(e,i)}return o})()));