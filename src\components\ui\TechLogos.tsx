'use client';

import { motion } from 'framer-motion';
import <PERSON>que<PERSON> from 'react-fast-marquee';

const techLogos = [
  {
    name: 'React',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <circle cx="12" cy="12" r="2" fill="#61DAFB"/>
        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4Z" fill="#61DAFB"/>
        <ellipse cx="12" cy="12" rx="8" ry="3" fill="none" stroke="#61DAFB" strokeWidth="1"/>
        <ellipse cx="12" cy="12" rx="8" ry="3" fill="none" stroke="#61DAFB" strokeWidth="1" transform="rotate(60 12 12)"/>
        <ellipse cx="12" cy="12" rx="8" ry="3" fill="none" stroke="#61DAFB" strokeWidth="1" transform="rotate(120 12 12)"/>
      </svg>
    ),
    color: '#61DAFB'
  },
  {
    name: 'Node.js',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <path d="M12,1.85C11.73,1.85 11.45,1.92 11.22,2.05L3.78,6.35C3.32,6.61 3.05,7.11 3.05,7.65V16.35C3.05,16.89 3.32,17.39 3.78,17.65L11.22,21.95C11.45,22.08 11.73,22.15 12,22.15C12.27,22.15 12.55,22.08 12.78,21.95L20.22,17.65C20.68,17.39 20.95,16.89 20.95,16.35V7.65C20.95,7.11 20.68,6.61 20.22,6.35L12.78,2.05C12.55,1.92 12.27,1.85 12,1.85M12,3.05L19,7V17L12,21L5,17V7L12,3.05Z" fill="#339933"/>
      </svg>
    ),
    color: '#339933'
  },
  {
    name: 'JavaScript',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <rect width="24" height="24" rx="3" fill="#F7DF1E"/>
        <path d="M7.5,16.5V14.5H9V16.5C9,17.33 9.67,18 10.5,18C11.33,18 12,17.33 12,16.5V10H13.5V16.5C13.5,18.16 12.16,19.5 10.5,19.5C8.84,19.5 7.5,18.16 7.5,16.5M15,14.5H16.5V15.5H17.5V14H15V12.5H19V16H17.5V15H16.5V16.5H19V18H15V14.5Z" fill="#000"/>
      </svg>
    ),
    color: '#F7DF1E'
  },
  {
    name: 'MongoDB',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12.5,17V19H11.5V17C10.9,16.9 10.4,16.6 10,16.2L11.1,15.1C11.4,15.4 11.7,15.5 12,15.5C12.6,15.5 13,15.1 13,14.5V9.5C13,8.9 12.6,8.5 12,8.5C11.4,8.5 11,8.9 11,9.5V14.5C11,15.9 12.1,17 13.5,17H12.5Z" fill="#47A248"/>
      </svg>
    ),
    color: '#47A248'
  },
  {
    name: 'HTML5',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <path d="M3.5,2L4.8,20L12,22L19.2,20L20.5,2H3.5M17.5,8H8.5L8.8,11H17.2L16.5,17L12,18.5L7.5,17L7.2,14H10.2L10.4,15.5L12,16L13.6,15.5L13.8,13H7L6.5,8H17.5V8Z" fill="#E34F26"/>
      </svg>
    ),
    color: '#E34F26'
  },
  {
    name: 'CSS3',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <path d="M3.5,2L4.8,20L12,22L19.2,20L20.5,2H3.5M16.5,8H8.5L8.8,11H16.2L15.5,17L12,18.5L8.5,17L8.2,14H11.2L11.4,15.5L12,16L12.6,15.5L12.8,13H8L7.5,8H16.5V8Z" fill="#1572B6"/>
      </svg>
    ),
    color: '#1572B6'
  },
  {
    name: 'Git',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <path d="M21.62,11.13L12.87,2.38C12.5,2 12,2 11.62,2.38L9.87,4.13L12.5,6.75C13,6.5 13.62,6.5 14.12,7C14.62,7.5 14.62,8.12 14.12,8.62C13.62,9.12 13,9.12 12.5,8.62L10,6.12V17.87C10.5,18.12 11,18.62 11,19.25C11,20.12 10.37,20.75 9.5,20.75C8.62,20.75 8,20.12 8,19.25C8,18.62 8.5,18.12 9,17.87V6.12L7.5,4.62L2.38,9.75C2,10.12 2,10.62 2.38,11L11.13,19.75C11.5,20.12 12,20.12 12.38,19.75L21.62,11.5C22,11.12 22,10.62 21.62,11.13Z" fill="#F05032"/>
      </svg>
    ),
    color: '#F05032'
  },
  {
    name: 'Express.js',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <path d="M24,18.588A1.529,1.529,0,0,1,22.471,20.117H1.529A1.529,1.529,0,0,1,0,18.588V5.412A1.529,1.529,0,0,1,1.529,3.883H22.471A1.529,1.529,0,0,1,24,5.412ZM22.471,5.412H1.529V18.588H22.471Z" fill="#000"/>
        <path d="M5.764,13.588L8.235,8.412H9.765L6.706,14.824H4.824L1.765,8.412H3.294Z" fill="#000"/>
        <path d="M13.588,8.412V14.824H12.059V8.412Z" fill="#000"/>
        <path d="M18.824,8.412V14.824H17.294V8.412Z" fill="#000"/>
      </svg>
    ),
    color: '#000000'
  },
  {
    name: 'Bootstrap',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <path d="M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6A2,2 0 0,0 20,4M20,18H4V6H20V18M13.5,8H10V16H13.5A2.5,2.5 0 0,0 16,13.5A2.5,2.5 0 0,0 13.5,11A2.5,2.5 0 0,0 16,8.5A2.5,2.5 0 0,0 13.5,8M13.5,9.5A1,1 0 0,1 14.5,10.5A1,1 0 0,1 13.5,11.5H11.5V9.5H13.5M13.5,13A1,1 0 0,1 14.5,14A1,1 0 0,1 13.5,15H11.5V13H13.5Z" fill="#7952B3"/>
      </svg>
    ),
    color: '#7952B3'
  },
  {
    name: 'Tailwind CSS',
    icon: (
      <svg viewBox="0 0 24 24" className="w-12 h-12">
        <path d="M12,2C8,2 5.5,4 4.5,8C6,6 7.75,5.25 9.75,5.75C10.6,5.95 11.2,6.55 11.85,7.2C12.95,8.3 14.25,9.5 17,9.5C21,9.5 23.5,7.5 24.5,3.5C23,5.5 21.25,6.25 19.25,5.75C18.4,5.55 17.8,4.95 17.15,4.3C16.05,3.2 14.75,2 12,2M4.5,9.5C0.5,9.5 -2,11.5 -3,15.5C-1.5,13.5 0.25,12.75 2.25,13.25C3.1,13.45 3.7,14.05 4.35,14.7C5.45,15.8 6.75,17 9.5,17C13.5,17 16,15 17,11C15.5,13 13.75,13.75 11.75,13.25C10.9,13.05 10.3,12.45 9.65,11.8C8.55,10.7 7.25,9.5 4.5,9.5Z" fill="#06B6D4"/>
      </svg>
    ),
    color: '#06B6D4'
  }
];

interface TechLogosProps {
  direction?: 'left' | 'right';
  speed?: number;
  className?: string;
}

export default function TechLogos({ direction = 'left', speed = 50, className = '' }: TechLogosProps) {
  return (
    <div className={`py-8 ${className}`}>
      <Marquee
        direction={direction}
        speed={speed}
        gradient={false}
        pauseOnHover={true}
        className="overflow-hidden"
      >
        {techLogos.map((tech, index) => (
          <motion.div
            key={index}
            className="mx-8 flex flex-col items-center group cursor-pointer"
            whileHover={{ scale: 1.1, y: -5 }}
            transition={{ duration: 0.3 }}
          >
            <div className="p-4 glass rounded-xl group-hover:glow-box transition-all duration-300 grayscale group-hover:grayscale-0">
              <div style={{ color: tech.color }}>
                {tech.icon}
              </div>
            </div>
            <span className="mt-2 text-sm font-medium text-gray-400 group-hover:text-white transition-colors duration-300">
              {tech.name}
            </span>
          </motion.div>
        ))}
      </Marquee>
    </div>
  );
}