'use client';

import { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import TechLogos from '@/components/ui/TechLogos';

gsap.registerPlugin(ScrollTrigger);

export default function TechStackSection() {
  const sectionRef = useRef<HTMLDivElement>(null);

  const techStack = {
    frontend: [
      { name: 'React.js', color: '#61DAFB', level: 90 },
      { name: 'HTML5', color: '#E34F26', level: 95 },
      { name: 'CSS3', color: '#1572B6', level: 90 },
      { name: 'JavaScript', color: '#F7DF1E', level: 85 },
      { name: 'Bootstrap', color: '#7952B3', level: 80 },
    ],
    backend: [
      { name: 'Node.js', color: '#339933', level: 85 },
      { name: 'Express.js', color: '#000000', level: 80 },
      { name: 'MongoDB', color: '#47A248', level: 75 },
      { name: 'Mongoose', color: '#880000', level: 75 },
    ],
    tools: [
      { name: 'Git', color: '#F05032', level: 85 },
      { name: 'GitHub', color: '#181717', level: 85 },
      { name: 'JIRA', color: '#0052CC', level: 70 },
      { name: 'Postman', color: '#FF6C37', level: 80 },
      { name: 'Swagger', color: '#85EA2D', level: 70 },
    ],
    familiar: [
      { name: 'CI/CD Pipelines', color: '#326CE5', level: 60 },
      { name: 'Docker', color: '#2496ED', level: 55 },
      { name: 'AWS', color: '#FF9900', level: 50 },
    ],
  };

  const marqueeItems = [
    'React.js', 'Node.js', 'Express.js', 'MongoDB', 'JavaScript', 'HTML5', 'CSS3', 
    'Bootstrap', 'Git', 'GitHub', 'JIRA', 'Postman', 'Swagger', 'Docker', 'AWS'
  ];

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate skill bars
      gsap.fromTo(
        '.skill-bar-fill',
        { width: '0%' },
        {
          width: (i, el) => el.getAttribute('data-level') + '%',
          duration: 1.5,
          ease: 'power2.out',
          stagger: 0.1,
          scrollTrigger: {
            trigger: '.skills-container',
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          },
        }
      );

      // Animate category cards
      gsap.fromTo(
        '.tech-category',
        { opacity: 0, y: 50, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.skills-container',
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const renderSkillCategory = (title: string, skills: typeof techStack.frontend, color: string) => (
    <motion.div
      className="tech-category glass-strong p-6 rounded-xl"
      whileHover={{ scale: 1.02, y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <h3 className="text-xl font-bold mb-6 font-space" style={{ color }}>
        {title}
      </h3>
      <div className="space-y-4">
        {skills.map((skill, index) => (
          <div key={index} className="skill-item">
            <div className="flex justify-between items-center mb-2">
              <span className="text-white font-medium">{skill.name}</span>
              <span className="text-gray-400 text-sm">{skill.level}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
              <div
                className="skill-bar-fill h-full rounded-full transition-all duration-300"
                data-level={skill.level}
                style={{ backgroundColor: skill.color }}
              />
            </div>
          </div>
        ))}
      </div>
    </motion.div>
  );

  return (
    <section
      id="tech-stack"
      ref={sectionRef}
      className="min-h-screen py-20 px-4 sm:px-6 lg:px-8"
    >
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6">
            Tech Stack
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Technologies and tools I work with to bring ideas to life
          </p>
        </motion.div>

        {/* Tech Logos Marquee */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <TechLogos speed={60} />
        </motion.div>

        {/* Reverse Direction Marquee */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <TechLogos direction="right" speed={45} />
        </motion.div>

        {/* Skills Grid */}
        <div className="skills-container grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {renderSkillCategory('Frontend', techStack.frontend, '#00d4ff')}
          {renderSkillCategory('Backend', techStack.backend, '#00ff88')}
          {renderSkillCategory('Tools', techStack.tools, '#8b5cf6')}
          {renderSkillCategory('Familiar With', techStack.familiar, '#f472b6')}
        </div>

        {/* Additional Info */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="glass p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-neon-blue mb-4 font-space">
              Always Learning
            </h3>
            <p className="text-lg text-gray-300 leading-relaxed">
              Technology evolves rapidly, and I'm committed to staying current with the latest trends 
              and best practices. I'm always exploring new frameworks, tools, and methodologies to 
              improve my development skills and deliver better solutions.
            </p>
            <div className="flex flex-wrap justify-center gap-4 mt-6">
              <span className="glass px-4 py-2 rounded-full text-sm text-neon-green">
                Next.js
              </span>
              <span className="glass px-4 py-2 rounded-full text-sm text-neon-green">
                TypeScript
              </span>
              <span className="glass px-4 py-2 rounded-full text-sm text-neon-green">
                GraphQL
              </span>
              <span className="glass px-4 py-2 rounded-full text-sm text-neon-green">
                Microservices
              </span>
              <span className="glass px-4 py-2 rounded-full text-sm text-neon-green">
                Cloud Computing
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
