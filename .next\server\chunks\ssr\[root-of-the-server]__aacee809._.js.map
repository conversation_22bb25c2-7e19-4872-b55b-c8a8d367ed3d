{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\n\nconst navItems = [\n  { href: '/', label: 'Home' },\n  { href: '/projects', label: 'Projects' },\n  { href: '/resume', label: 'Resume' },\n  { href: '/contact', label: 'Contact' },\n];\n\nexport default function Navigation() {\n  const [scrolled, setScrolled] = useState(false);\n  const pathname = usePathname();\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  return (\n    <motion.nav\n      className={`fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${\n        scrolled\n          ? 'glass-strong backdrop-blur-lg'\n          : 'bg-transparent'\n      }`}\n      initial={{ y: -100, opacity: 0 }}\n      animate={{ y: 0, opacity: 1 }}\n      transition={{ delay: 7.5, duration: 0.8, ease: \"easeOut\" }}\n    >\n      <div className=\"max-w-[90vw] mx-auto px-4 md:px-10\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo/Name - Animated from splash */}\n          <motion.div\n            layoutId=\"main-name\"\n            className=\"text-xl font-bold gradient-text font-orbitron cursor-pointer\"\n            whileHover={{ scale: 1.05 }}\n          >\n            <Link href=\"/\">Saurabh Dahariya</Link>\n          </motion.div>\n\n          {/* Center Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n\n              return (\n                <Link key={item.href} href={item.href}>\n                  <motion.div\n                    className={`relative px-4 py-2 font-medium transition-colors duration-300 ${\n                      isActive\n                        ? 'text-neon-blue'\n                        : 'text-gray-300 hover:text-white'\n                    }`}\n                    whileHover={{ y: -2 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    {item.label}\n\n                    {/* Animated underline */}\n                    <motion.div\n                      className=\"absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-neon-blue to-neon-green\"\n                      initial={{ width: 0 }}\n                      animate={{ width: isActive ? '100%' : 0 }}\n                      whileHover={{ width: '100%' }}\n                      transition={{ duration: 0.3, ease: \"easeInOut\" }}\n                    />\n                  </motion.div>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            className=\"md:hidden p-2 text-gray-300 hover:text-white\"\n            whileHover={{ scale: 1.1 }}\n            whileTap={{ scale: 0.9 }}\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </motion.button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <motion.div\n          className=\"md:hidden pb-4\"\n          initial={{ height: 0, opacity: 0 }}\n          animate={{ height: 'auto', opacity: 1 }}\n          transition={{ delay: 8, duration: 0.5 }}\n        >\n          <div className=\"space-y-2\">\n            {navItems.map((item) => {\n              const isActive = pathname === item.href;\n\n              return (\n                <Link key={item.href} href={item.href}>\n                  <motion.div\n                    className={`block px-4 py-2 rounded-lg font-medium transition-colors ${\n                      isActive\n                        ? 'text-neon-blue bg-gray-800/50'\n                        : 'text-gray-300 hover:text-white hover:bg-gray-800/30'\n                    }`}\n                    whileHover={{ x: 5 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    {item.label}\n                  </motion.div>\n                </Link>\n              );\n            })}\n          </div>\n        </motion.div>\n      </div>\n    </motion.nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,WAAW;IACf;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAa,OAAO;IAAW;IACvC;QAAE,MAAM;QAAW,OAAO;IAAS;IACnC;QAAE,MAAM;QAAY,OAAO;IAAU;CACtC;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,4DAA4D,EACtE,WACI,kCACA,kBACJ;QACF,SAAS;YAAE,GAAG,CAAC;YAAK,SAAS;QAAE;QAC/B,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,YAAY;YAAE,OAAO;YAAK,UAAU;YAAK,MAAM;QAAU;kBAEzD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAS;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;sCAE1B,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CAAI;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAAiB,MAAM,KAAK,IAAI;8CACnC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAW,CAAC,8DAA8D,EACxE,WACI,mBACA,kCACJ;wCACF,YAAY;4CAAE,GAAG,CAAC;wCAAE;wCACpB,UAAU;4CAAE,OAAO;wCAAK;;4CAEvB,KAAK,KAAK;0DAGX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO,WAAW,SAAS;gDAAE;gDACxC,YAAY;oDAAE,OAAO;gDAAO;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,MAAM;gDAAY;;;;;;;;;;;;mCAlB1C,KAAK,IAAI;;;;;4BAuBxB;;;;;;sCAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAI;4BACzB,UAAU;gCAAE,OAAO;4BAAI;sCAEvB,cAAA,8OAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBACjC,SAAS;wBAAE,QAAQ;wBAAQ,SAAS;oBAAE;oBACtC,YAAY;wBAAE,OAAO;wBAAG,UAAU;oBAAI;8BAEtC,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,WAAW,aAAa,KAAK,IAAI;4BAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CACnC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAW,CAAC,yDAAyD,EACnE,WACI,kCACA,uDACJ;oCACF,YAAY;wCAAE,GAAG;oCAAE;oCACnB,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,KAAK,KAAK;;;;;;+BAVJ,KAAK,IAAI;;;;;wBAcxB;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/CustomCursor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\n\nexport default function CustomCursor() {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovering, setIsHovering] = useState(false);\n\n  useEffect(() => {\n    const updateMousePosition = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n    };\n\n    const handleMouseEnter = () => setIsHovering(true);\n    const handleMouseLeave = () => setIsHovering(false);\n\n    // Add event listeners for mouse movement\n    window.addEventListener('mousemove', updateMousePosition);\n\n    // Add hover detection for interactive elements\n    const interactiveElements = document.querySelectorAll('button, a, [role=\"button\"]');\n    interactiveElements.forEach(el => {\n      el.addEventListener('mouseenter', handleMouseEnter);\n      el.addEventListener('mouseleave', handleMouseLeave);\n    });\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition);\n      interactiveElements.forEach(el => {\n        el.removeEventListener('mouseenter', handleMouseEnter);\n        el.removeEventListener('mouseleave', handleMouseLeave);\n      });\n    };\n  }, []);\n\n  return (\n    <>\n      {/* Main cursor */}\n      <motion.div\n        className=\"custom-cursor\"\n        style={{\n          left: mousePosition.x - 10,\n          top: mousePosition.y - 10,\n        }}\n        animate={{\n          scale: isHovering ? 1.5 : 1,\n          backgroundColor: isHovering ? '#00ff88' : '#00d4ff',\n        }}\n        transition={{\n          type: 'spring',\n          stiffness: 500,\n          damping: 28,\n        }}\n      />\n      \n      {/* Cursor trail */}\n      <motion.div\n        className=\"custom-cursor-trail\"\n        style={{\n          left: mousePosition.x - 4,\n          top: mousePosition.y - 4,\n        }}\n        animate={{\n          scale: isHovering ? 2 : 1,\n          opacity: isHovering ? 0.8 : 0.6,\n        }}\n        transition={{\n          type: 'spring',\n          stiffness: 300,\n          damping: 30,\n          delay: 0.05,\n        }}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB,CAAC;YAC3B,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;QAChD;QAEA,MAAM,mBAAmB,IAAM,cAAc;QAC7C,MAAM,mBAAmB,IAAM,cAAc;QAE7C,yCAAyC;QACzC,OAAO,gBAAgB,CAAC,aAAa;QAErC,+CAA+C;QAC/C,MAAM,sBAAsB,SAAS,gBAAgB,CAAC;QACtD,oBAAoB,OAAO,CAAC,CAAA;YAC1B,GAAG,gBAAgB,CAAC,cAAc;YAClC,GAAG,gBAAgB,CAAC,cAAc;QACpC;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,aAAa;YACxC,oBAAoB,OAAO,CAAC,CAAA;gBAC1B,GAAG,mBAAmB,CAAC,cAAc;gBACrC,GAAG,mBAAmB,CAAC,cAAc;YACvC;QACF;IACF,GAAG,EAAE;IAEL,qBACE;;0BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,MAAM,cAAc,CAAC,GAAG;oBACxB,KAAK,cAAc,CAAC,GAAG;gBACzB;gBACA,SAAS;oBACP,OAAO,aAAa,MAAM;oBAC1B,iBAAiB,aAAa,YAAY;gBAC5C;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;;;;;0BAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,MAAM,cAAc,CAAC,GAAG;oBACxB,KAAK,cAAc,CAAC,GAAG;gBACzB;gBACA,SAAS;oBACP,OAAO,aAAa,IAAI;oBACxB,SAAS,aAAa,MAAM;gBAC9B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;oBACT,OAAO;gBACT;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/mordern-portfolio-saurabh/src/components/ui/AIAssistant.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Mic, MicOff, Send, Bot, X } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\n\ninterface Message {\n  id: string;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n}\n\nexport default function AIAssistant() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isListening, setIsListening] = useState(false);\n  const [inputText, setInputText] = useState('');\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const router = useRouter();\n\n  const addMessage = (text: string, isUser: boolean) => {\n    const newMessage: Message = {\n      id: Date.now().toString(),\n      text,\n      isUser,\n      timestamp: new Date(),\n    };\n    setMessages(prev => [...prev, newMessage]);\n  };\n\n  const handleSendMessage = async () => {\n    if (!inputText.trim()) return;\n\n    const userMessage = inputText.trim();\n    setInputText('');\n    addMessage(userMessage, true);\n    setIsLoading(true);\n\n    try {\n      // Simulate AI response and navigation logic\n      const response = await processAIQuery(userMessage);\n      addMessage(response.text, false);\n      \n      if (response.navigate) {\n        setTimeout(() => {\n          router.push(response.navigate);\n        }, 1000);\n      }\n    } catch (error) {\n      addMessage('Sorry, I encountered an error. Please try again.', false);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const processAIQuery = async (query: string): Promise<{ text: string; navigate?: string }> => {\n    try {\n      const response = await fetch('/api/ai-chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ message: query }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to get AI response');\n      }\n\n      const data = await response.json();\n      return {\n        text: data.response,\n        navigate: data.navigate,\n      };\n    } catch (error) {\n      console.error('AI query error:', error);\n      return {\n        text: \"I'm sorry, I'm having trouble processing your request right now. Please try again or feel free to explore the site manually!\",\n      };\n    }\n  };\n\n  const toggleListening = () => {\n    setIsListening(!isListening);\n    // Voice recognition would be implemented here\n  };\n\n  useEffect(() => {\n    if (isOpen && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isOpen]);\n\n  return (\n    <>\n      {/* AI Assistant Bar - Always visible at bottom center */}\n      <motion.div\n        className=\"fixed bottom-6 left-1/2 transform -translate-x-1/2 z-[9999]\"\n        initial={{ y: 100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ delay: 8, duration: 0.8 }}\n      >\n        {!isOpen ? (\n          // Collapsed state - floating chat bar\n          <motion.div\n            className=\"backdrop-blur-lg bg-white/10 text-white rounded-full shadow-2xl px-6 py-3 flex gap-3 items-center cursor-pointer border border-white/20\"\n            onClick={() => setIsOpen(true)}\n            whileHover={{ scale: 1.05, y: -2 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <motion.div\n              animate={isListening ? { scale: [1, 1.2, 1] } : {}}\n              transition={{ duration: 1, repeat: isListening ? Infinity : 0 }}\n            >\n              <button\n                onClick={(e) => {\n                  e.stopPropagation();\n                  toggleListening();\n                }}\n                className={`p-2 rounded-full transition-colors ${\n                  isListening\n                    ? 'bg-red-500 text-white'\n                    : 'text-neon-blue hover:text-neon-green'\n                }`}\n              >\n                {isListening ? <MicOff size={16} /> : <Mic size={16} />}\n              </button>\n            </motion.div>\n\n            <input\n              type=\"text\"\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\n              placeholder=\"Ask me anything...\"\n              className=\"bg-transparent border-none outline-none text-white placeholder-gray-300 w-64\"\n              onClick={(e) => e.stopPropagation()}\n            />\n\n            <button\n              onClick={(e) => {\n                e.stopPropagation();\n                handleSendMessage();\n              }}\n              disabled={!inputText.trim() || isLoading}\n              className=\"text-neon-blue hover:text-neon-green disabled:opacity-50 disabled:cursor-not-allowed p-1\"\n            >\n              <Send size={16} />\n            </button>\n          </motion.div>\n        ) : (\n          // Expanded state - full chat interface\n          <motion.div\n            className=\"backdrop-blur-lg bg-white/10 text-white rounded-2xl shadow-2xl border border-white/20 w-96 max-w-[90vw]\"\n            initial={{ scale: 0.9, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ duration: 0.3 }}\n          >\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 border-b border-white/20\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-neon-blue to-neon-green rounded-full flex items-center justify-center\">\n                  <Bot size={16} className=\"text-black\" />\n                </div>\n                <div>\n                  <h3 className=\"font-semibold\">AI Assistant</h3>\n                  <p className=\"text-xs text-gray-300\">Ask about Saurabh's work</p>\n                </div>\n              </div>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"text-gray-400 hover:text-white transition-colors p-1\"\n              >\n                <X size={20} />\n              </button>\n            </div>\n\n            {/* Messages */}\n            <div className=\"flex-1 overflow-y-auto p-4 space-y-3 max-h-64 min-h-[200px]\">\n              {messages.length === 0 && (\n                <div className=\"text-gray-300 text-sm text-center py-8\">\n                  <Bot className=\"mx-auto mb-2 text-neon-blue\" size={32} />\n                  <p>Hi! I'm Saurabh's AI assistant.</p>\n                  <p className=\"text-xs mt-1\">Ask me about his projects, skills, or experience!</p>\n                </div>\n              )}\n              {messages.map((message) => (\n                <motion.div\n                  key={message.id}\n                  className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <div\n                    className={`max-w-[85%] p-3 rounded-2xl text-sm ${\n                      message.isUser\n                        ? 'bg-gradient-to-r from-neon-blue to-neon-green text-black font-medium'\n                        : 'bg-white/10 text-white border border-white/20'\n                    }`}\n                  >\n                    {message.text}\n                  </div>\n                </motion.div>\n              ))}\n              {isLoading && (\n                <motion.div\n                  className=\"flex justify-start\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                >\n                  <div className=\"bg-white/10 border border-white/20 text-white p-3 rounded-2xl text-sm\">\n                    <div className=\"flex space-x-1\">\n                      <motion.div\n                        className=\"w-2 h-2 bg-neon-blue rounded-full\"\n                        animate={{ scale: [1, 1.2, 1] }}\n                        transition={{ duration: 1, repeat: Infinity, delay: 0 }}\n                      />\n                      <motion.div\n                        className=\"w-2 h-2 bg-neon-green rounded-full\"\n                        animate={{ scale: [1, 1.2, 1] }}\n                        transition={{ duration: 1, repeat: Infinity, delay: 0.2 }}\n                      />\n                      <motion.div\n                        className=\"w-2 h-2 bg-neon-purple rounded-full\"\n                        animate={{ scale: [1, 1.2, 1] }}\n                        transition={{ duration: 1, repeat: Infinity, delay: 0.4 }}\n                      />\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n            </div>\n\n            {/* Input Area */}\n            <div className=\"p-4 border-t border-white/20\">\n              <div className=\"flex items-center space-x-3\">\n                <motion.button\n                  onClick={toggleListening}\n                  className={`p-2 rounded-full transition-colors ${\n                    isListening\n                      ? 'bg-red-500 text-white'\n                      : 'text-neon-blue hover:text-neon-green'\n                  }`}\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                  animate={isListening ? { scale: [1, 1.1, 1] } : {}}\n                  transition={{ duration: 1, repeat: isListening ? Infinity : 0 }}\n                >\n                  {isListening ? <MicOff size={16} /> : <Mic size={16} />}\n                </motion.button>\n\n                <div className=\"flex-1 relative\">\n                  <input\n                    ref={inputRef}\n                    type=\"text\"\n                    value={inputText}\n                    onChange={(e) => setInputText(e.target.value)}\n                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}\n                    placeholder=\"Ask me anything...\"\n                    className=\"w-full bg-white/10 border border-white/20 text-white rounded-full px-4 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-neon-blue placeholder-gray-300\"\n                  />\n                  <button\n                    onClick={handleSendMessage}\n                    disabled={!inputText.trim() || isLoading}\n                    className=\"absolute right-2 top-1/2 transform -translate-y-1/2 text-neon-blue hover:text-neon-green disabled:opacity-50 disabled:cursor-not-allowed p-1\"\n                  >\n                    <Send size={14} />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </motion.div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAC,MAAc;QAChC,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA;YACA,WAAW,IAAI;QACjB;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;IAC3C;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU,IAAI,IAAI;QAEvB,MAAM,cAAc,UAAU,IAAI;QAClC,aAAa;QACb,WAAW,aAAa;QACxB,aAAa;QAEb,IAAI;YACF,4CAA4C;YAC5C,MAAM,WAAW,MAAM,eAAe;YACtC,WAAW,SAAS,IAAI,EAAE;YAE1B,IAAI,SAAS,QAAQ,EAAE;gBACrB,WAAW;oBACT,OAAO,IAAI,CAAC,SAAS,QAAQ;gBAC/B,GAAG;YACL;QACF,EAAE,OAAO,OAAO;YACd,WAAW,oDAAoD;QACjE,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS;gBAAM;YACxC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,MAAM,KAAK,QAAQ;gBACnB,UAAU,KAAK,QAAQ;YACzB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBACL,MAAM;YACR;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,eAAe,CAAC;IAChB,8CAA8C;IAChD;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,SAAS,OAAO,EAAE;YAC9B,SAAS,OAAO,CAAC,KAAK;QACxB;IACF,GAAG;QAAC;KAAO;IAEX,qBACE;kBAEE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,GAAG;gBAAK,SAAS;YAAE;YAC9B,SAAS;gBAAE,GAAG;gBAAG,SAAS;YAAE;YAC5B,YAAY;gBAAE,OAAO;gBAAG,UAAU;YAAI;sBAErC,CAAC,SACA,sCAAsC;0BACtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS,IAAM,UAAU;gBACzB,YAAY;oBAAE,OAAO;oBAAM,GAAG,CAAC;gBAAE;gBACjC,UAAU;oBAAE,OAAO;gBAAK;;kCAExB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS,cAAc;4BAAE,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBAAC,IAAI,CAAC;wBACjD,YAAY;4BAAE,UAAU;4BAAG,QAAQ,cAAc,WAAW;wBAAE;kCAE9D,cAAA,8OAAC;4BACC,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;4BACA,WAAW,CAAC,mCAAmC,EAC7C,cACI,0BACA,wCACJ;sCAED,4BAAc,8OAAC,0MAAA,CAAA,SAAM;gCAAC,MAAM;;;;;qDAAS,8OAAC,gMAAA,CAAA,MAAG;gCAAC,MAAM;;;;;;;;;;;;;;;;kCAIrD,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC5C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;wBACxC,aAAY;wBACZ,WAAU;wBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;kCAGnC,8OAAC;wBACC,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB;wBACF;wBACA,UAAU,CAAC,UAAU,IAAI,MAAM;wBAC/B,WAAU;kCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;;;;;;;;;;;uBAIhB,uCAAuC;0BACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;;kCAG5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;kDAE3B,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;;;;;;;;;;;;;kCAKb,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,MAAM,KAAK,mBACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;wCAA8B,MAAM;;;;;;kDACnD,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;4BAG/B,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,GAAG,gBAAgB,iBAAiB;oCACrE,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;8CAE5B,cAAA,8OAAC;wCACC,WAAW,CAAC,oCAAoC,EAC9C,QAAQ,MAAM,GACV,yEACA,iDACJ;kDAED,QAAQ,IAAI;;;;;;mCAbV,QAAQ,EAAE;;;;;4BAiBlB,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;0CAEtB,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,OAAO;gDAAE;;;;;;0DAExD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,OAAO;gDAAI;;;;;;0DAE1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDAAC;gDAC9B,YAAY;oDAAE,UAAU;oDAAG,QAAQ;oDAAU,OAAO;gDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASpE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;oCACT,WAAW,CAAC,mCAAmC,EAC7C,cACI,0BACA,wCACJ;oCACF,YAAY;wCAAE,OAAO;oCAAI;oCACzB,UAAU;wCAAE,OAAO;oCAAI;oCACvB,SAAS,cAAc;wCAAE,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;oCAAC,IAAI,CAAC;oCACjD,YAAY;wCAAE,UAAU;wCAAG,QAAQ,cAAc,WAAW;oCAAE;8CAE7D,4BAAc,8OAAC,0MAAA,CAAA,SAAM;wCAAC,MAAM;;;;;6DAAS,8OAAC,gMAAA,CAAA,MAAG;wCAAC,MAAM;;;;;;;;;;;8CAGnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC5C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4CACxC,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC,UAAU,IAAI,MAAM;4CAC/B,WAAU;sDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhC", "debugId": null}}]}