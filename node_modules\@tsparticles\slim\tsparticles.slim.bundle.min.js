/*! For license information please see tsparticles.slim.bundle.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var s in i)("object"==typeof exports?exports:t)[s]=i[s]}}(this,(()=>(()=>{var t={d:(e,i)=>{for(var s in i)t.o(i,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:i[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{AlterType:()=>di,AnimatableColor:()=>Ps,AnimationMode:()=>ze,AnimationOptions:()=>ks,AnimationStatus:()=>Ce,AnimationValueWithRandom:()=>Rs,Background:()=>rs,BackgroundMask:()=>ls,BackgroundMaskCover:()=>cs,BaseRange:()=>po,Circle:()=>fo,ClickEvent:()=>ds,CollisionMode:()=>mi,Collisions:()=>Ls,CollisionsAbsorb:()=>Os,CollisionsOverlap:()=>Ss,ColorAnimation:()=>zs,DestroyType:()=>Pe,DivEvent:()=>us,DivType:()=>fi,EasingType:()=>To,EventType:()=>pi,Events:()=>vs,ExternalInteractorBase:()=>Co,FullScreen:()=>hs,GradientType:()=>So,HoverEvent:()=>fs,HslAnimation:()=>Cs,Interactivity:()=>ys,InteractivityDetect:()=>ui,InteractorType:()=>wi,LimitMode:()=>bi,ManualParticle:()=>bs,Modes:()=>ms,Move:()=>Gs,MoveAngle:()=>As,MoveAttract:()=>Bs,MoveCenter:()=>qs,MoveDirection:()=>Wt,MoveGravity:()=>Vs,MovePath:()=>Hs,MoveTrail:()=>Ws,Opacity:()=>Qs,OpacityAnimation:()=>Ns,Options:()=>ro,OptionsColor:()=>as,OutMode:()=>yi,OutModeDirection:()=>Oe,OutModes:()=>js,Parallax:()=>ps,ParticleOutType:()=>xi,ParticlesBounce:()=>Is,ParticlesBounceFactor:()=>Es,ParticlesDensity:()=>Xs,ParticlesInteractorBase:()=>Po,ParticlesNumber:()=>Zs,ParticlesNumberLimit:()=>Ys,ParticlesOptions:()=>oo,PixelMode:()=>Se,Point:()=>uo,RangedAnimationOptions:()=>Ms,RangedAnimationValueWithRandom:()=>Ds,Rectangle:()=>go,ResizeEvent:()=>gs,Responsive:()=>ws,ResponsiveMode:()=>gi,RotateDirection:()=>Oo,Shadow:()=>Ks,Shape:()=>Js,Size:()=>eo,SizeAnimation:()=>to,Spin:()=>$s,StartValueType:()=>Te,Stroke:()=>io,Theme:()=>_s,ThemeDefault:()=>xs,ThemeMode:()=>vi,ValueWithRandom:()=>Ts,Vector:()=>Kt,Vector3d:()=>Zt,ZIndex:()=>so,alterHsl:()=>ts,animate:()=>oe,areBoundsInside:()=>$e,arrayRandomIndex:()=>Ue,calcExactPositionOrRandomFromSize:()=>_e,calcExactPositionOrRandomFromSizeRanged:()=>ke,calcPositionFromSize:()=>be,calcPositionOrRandomFromSize:()=>we,calcPositionOrRandomFromSizeRanged:()=>xe,calculateBounds:()=>Ge,cancelAnimation:()=>ne,canvasFirstIndex:()=>at,canvasTag:()=>O,circleBounce:()=>Je,circleBounceDataFromParticle:()=>Ke,clamp:()=>ae,clear:()=>Ni,clickRadius:()=>J,cloneStyle:()=>li,collisionVelocity:()=>ye,colorMix:()=>Fi,colorToHsl:()=>Ci,colorToRgb:()=>zi,countOffset:()=>wt,decayOffset:()=>dt,deepExtend:()=>Ne,defaultAlpha:()=>z,defaultAngle:()=>St,defaultDensityFactor:()=>zt,defaultFps:()=>M,defaultFpsLimit:()=>ot,defaultLoops:()=>Ht,defaultOpacity:()=>K,defaultRadius:()=>vt,defaultRatio:()=>E,defaultReduceFactor:()=>I,defaultRemoveQuantity:()=>D,defaultRetryCount:()=>S,defaultRgbMin:()=>qt,defaultTime:()=>Ut,defaultTransform:()=>b,defaultTransformValue:()=>Q,defaultVelocity:()=>Vt,degToRad:()=>ge,deleteCount:()=>Ct,divMode:()=>Ze,divModeExecute:()=>Xe,double:()=>_,doublePI:()=>k,drawEffect:()=>Xi,drawLine:()=>ji,drawParticle:()=>Qi,drawParticlePlugin:()=>Ji,drawPlugin:()=>Ki,drawShape:()=>Yi,drawShapeAfterDraw:()=>Zi,empty:()=>j,errorPrefix:()=>f,executeOnSingleOrMultiple:()=>ei,findItemFromSingleOrMultiple:()=>si,generatedAttribute:()=>i,generatedFalse:()=>P,generatedTrue:()=>C,getDistance:()=>fe,getDistances:()=>pe,getFullScreenStyle:()=>hi,getHslAnimationFromHsl:()=>Vi,getHslFromAnimation:()=>qi,getLinkColor:()=>Ai,getLinkRandomColor:()=>Bi,getLogger:()=>Ee,getParticleBaseVelocity:()=>me,getParticleDirectionAngle:()=>ve,getPosition:()=>ai,getRandom:()=>ie,getRandomRgbColor:()=>Ei,getRangeMax:()=>de,getRangeMin:()=>he,getRangeValue:()=>le,getSize:()=>ri,getStyleFromHsl:()=>Li,getStyleFromRgb:()=>Ii,hMax:()=>B,hMin:()=>H,hPhase:()=>W,half:()=>v,hasMatchMedia:()=>Fe,hslToRgb:()=>Ri,hslaToRgba:()=>Di,identity:()=>Tt,initParticleNumericAnimationValue:()=>oi,inverseFactorNumerator:()=>F,isArray:()=>Xt,isBoolean:()=>jt,isDivModeEnabled:()=>Qe,isFunction:()=>Nt,isInArray:()=>Ve,isNull:()=>Yt,isNumber:()=>Gt,isObject:()=>Qt,isPointInside:()=>je,isSsr:()=>Le,isString:()=>$t,itemFromArray:()=>We,itemFromSingleOrMultiple:()=>ii,lFactor:()=>Dt,lMax:()=>V,lMin:()=>Et,lengthOffset:()=>Mt,loadFont:()=>He,loadMinIndex:()=>ct,loadOptions:()=>no,loadParticlesOptions:()=>ao,loadRandomFactor:()=>rt,loadSlim:()=>cr,manualCount:()=>kt,manualDefaultPosition:()=>Ot,midColorValue:()=>x,millisecondsToSeconds:()=>m,minCoordinate:()=>et,minCount:()=>xt,minFpsLimit:()=>nt,minIndex:()=>_t,minLimit:()=>bt,minRetries:()=>pt,minStrokeWidth:()=>Rt,minVelocity:()=>N,minZ:()=>gt,minimumLength:()=>Y,minimumSize:()=>X,mix:()=>re,mouseDownEvent:()=>s,mouseLeaveEvent:()=>n,mouseMoveEvent:()=>r,mouseOutEvent:()=>a,mouseUpEvent:()=>o,none:()=>ht,one:()=>lt,originPoint:()=>y,paintBase:()=>$i,paintImage:()=>Gi,parseAlpha:()=>Me,percentDenominator:()=>g,phaseNumerator:()=>Bt,posOffset:()=>mt,qTreeCapacity:()=>R,quarter:()=>$,randomColorValue:()=>w,randomInRange:()=>ce,rangeColorToHsl:()=>Pi,rangeColorToRgb:()=>Mi,rectBounce:()=>ti,removeDeleteCount:()=>it,removeMinIndex:()=>st,resizeEvent:()=>u,rgbFactor:()=>It,rgbMax:()=>A,rgbToHsl:()=>Oi,rollFactor:()=>ft,sMax:()=>q,sMin:()=>U,sNormalizedOffset:()=>At,safeIntersectionObserver:()=>Be,safeMatchMedia:()=>Ae,safeMutationObserver:()=>qe,setAnimationFunctions:()=>se,setLogger:()=>De,setRandom:()=>ee,setRangeValue:()=>ue,sextuple:()=>Ft,singleDivModeExecute:()=>Ye,sizeFactor:()=>yt,squareExp:()=>T,stringToAlpha:()=>Si,stringToRgb:()=>Ti,subdivideCount:()=>L,threeQuarter:()=>G,touchCancelEvent:()=>d,touchDelay:()=>Pt,touchEndEvent:()=>l,touchEndLengthOffset:()=>tt,touchMoveEvent:()=>h,touchStartEvent:()=>c,triple:()=>Lt,tryCountIncrement:()=>ut,tsParticles:()=>Ro,updateAnimation:()=>ci,updateColor:()=>Wi,updateColorValue:()=>Ui,visibilityChangeEvent:()=>p,zIndexFactorOffset:()=>Z});const i="generated",s="pointerdown",o="pointerup",n="pointerleave",a="pointerout",r="pointermove",c="touchstart",l="touchend",h="touchmove",d="touchcancel",u="resize",p="visibilitychange",f="tsParticles - Error",g=100,v=.5,m=1e3,y={x:0,y:0,z:0},b={a:1,b:0,c:0,d:1},w="random",x="mid",_=2,k=Math.PI*_,M=60,z=1,C="true",P="false",O="canvas",S=0,T=2,R=4,D=1,E=1,I=1,L=4,F=1,A=255,B=360,q=100,V=100,H=0,U=0,W=60,j=0,$=.25,G=v+$,N=0,Q=1,X=0,Y=0,Z=1,K=1,J=1,tt=1,et=0,it=1,st=0,ot=120,nt=0,at=0,rt=1e4,ct=0,lt=1,ht=0,dt=1,ut=1,pt=0,ft=1,gt=0,vt=0,mt=-$,yt=1.5,bt=0,wt=1,xt=0,_t=0,kt=0,Mt=1,zt=1,Ct=1,Pt=500,Ot=50,St=0,Tt=1,Rt=0,Dt=1,Et=0,It=255,Lt=3,Ft=6,At=1,Bt=1,qt=0,Vt=0,Ht=0,Ut=0;var Wt;function jt(t){return"boolean"==typeof t}function $t(t){return"string"==typeof t}function Gt(t){return"number"==typeof t}function Nt(t){return"function"==typeof t}function Qt(t){return"object"==typeof t&&null!==t}function Xt(t){return Array.isArray(t)}function Yt(t){return null==t}!function(t){t.bottom="bottom",t.bottomLeft="bottom-left",t.bottomRight="bottom-right",t.left="left",t.none="none",t.right="right",t.top="top",t.topLeft="top-left",t.topRight="top-right",t.outside="outside",t.inside="inside"}(Wt||(Wt={}));class Zt{constructor(t,e,i){if(this._updateFromAngle=(t,e)=>{this.x=Math.cos(t)*e,this.y=Math.sin(t)*e},!Gt(t)&&t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:y.z}else{if(void 0===t||void 0===e)throw new Error(`${f} Vector3d not initialized correctly`);this.x=t,this.y=e,this.z=i??y.z}}static get origin(){return Zt.create(y.x,y.y,y.z)}get angle(){return Math.atan2(this.y,this.x)}set angle(t){this._updateFromAngle(t,this.length)}get length(){return Math.sqrt(this.getLengthSq())}set length(t){this._updateFromAngle(this.angle,t)}static clone(t){return Zt.create(t.x,t.y,t.z)}static create(t,e,i){return new Zt(t,e,i)}add(t){return Zt.create(this.x+t.x,this.y+t.y,this.z+t.z)}addTo(t){this.x+=t.x,this.y+=t.y,this.z+=t.z}copy(){return Zt.clone(this)}distanceTo(t){return this.sub(t).length}distanceToSq(t){return this.sub(t).getLengthSq()}div(t){return Zt.create(this.x/t,this.y/t,this.z/t)}divTo(t){this.x/=t,this.y/=t,this.z/=t}getLengthSq(){return this.x**T+this.y**T}mult(t){return Zt.create(this.x*t,this.y*t,this.z*t)}multTo(t){this.x*=t,this.y*=t,this.z*=t}normalize(){const t=this.length;t!=ht&&this.multTo(F/t)}rotate(t){return Zt.create(this.x*Math.cos(t)-this.y*Math.sin(t),this.x*Math.sin(t)+this.y*Math.cos(t),y.z)}setTo(t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:y.z}sub(t){return Zt.create(this.x-t.x,this.y-t.y,this.z-t.z)}subFrom(t){this.x-=t.x,this.y-=t.y,this.z-=t.z}}class Kt extends Zt{constructor(t,e){super(t,e,y.z)}static get origin(){return Kt.create(y.x,y.y)}static clone(t){return Kt.create(t.x,t.y)}static create(t,e){return new Kt(t,e)}}let Jt=Math.random;const te={nextFrame:t=>requestAnimationFrame(t),cancel:t=>cancelAnimationFrame(t)};function ee(t=Math.random){Jt=t}function ie(){return ae(Jt(),0,1-Number.EPSILON)}function se(t,e){te.nextFrame=e=>t(e),te.cancel=t=>e(t)}function oe(t){return te.nextFrame(t)}function ne(t){te.cancel(t)}function ae(t,e,i){return Math.min(Math.max(t,e),i)}function re(t,e,i,s){return Math.floor((t*i+e*s)/(i+s))}function ce(t){const e=de(t);let i=he(t);return e===i&&(i=0),ie()*(e-i)+i}function le(t){return Gt(t)?t:ce(t)}function he(t){return Gt(t)?t:t.min}function de(t){return Gt(t)?t:t.max}function ue(t,e){if(t===e||void 0===e&&Gt(t))return t;const i=he(t),s=de(t);return void 0!==e?{min:Math.min(i,e),max:Math.max(s,e)}:ue(i,s)}function pe(t,e){const i=t.x-e.x,s=t.y-e.y;return{dx:i,dy:s,distance:Math.sqrt(i**2+s**2)}}function fe(t,e){return pe(t,e).distance}function ge(t){return t*Math.PI/180}function ve(t,e,i){if(Gt(t))return ge(t);switch(t){case Wt.top:return-Math.PI*v;case Wt.topRight:return-Math.PI*$;case Wt.right:return j;case Wt.bottomRight:return Math.PI*$;case Wt.bottom:return Math.PI*v;case Wt.bottomLeft:return Math.PI*G;case Wt.left:return Math.PI;case Wt.topLeft:return-Math.PI*G;case Wt.inside:return Math.atan2(i.y-e.y,i.x-e.x);case Wt.outside:return Math.atan2(e.y-i.y,e.x-i.x);default:return ie()*k}}function me(t){const e=Kt.origin;return e.length=1,e.angle=t,e}function ye(t,e,i,s){return Kt.create(t.x*(i-s)/(i+s)+e.x*_*s/(i+s),t.y)}function be(t){return void 0!==t.position?.x&&void 0!==t.position.y?{x:t.position.x*t.size.width/g,y:t.position.y*t.size.height/g}:void 0}function we(t){return{x:(t.position?.x??ie()*g)*t.size.width/g,y:(t.position?.y??ie()*g)*t.size.height/g}}function xe(t){const e={x:void 0!==t.position?.x?le(t.position.x):void 0,y:void 0!==t.position?.y?le(t.position.y):void 0};return we({size:t.size,position:e})}function _e(t){return{x:t.position?.x??ie()*t.size.width,y:t.position?.y??ie()*t.size.height}}function ke(t){const e={x:void 0!==t.position?.x?le(t.position.x):void 0,y:void 0!==t.position?.y?le(t.position.y):void 0};return _e({size:t.size,position:e})}function Me(t){return t?t.endsWith("%")?parseFloat(t)/g:parseFloat(t):1}var ze,Ce,Pe,Oe,Se,Te;!function(t){t.auto="auto",t.increase="increase",t.decrease="decrease",t.random="random"}(ze||(ze={})),function(t){t.increasing="increasing",t.decreasing="decreasing"}(Ce||(Ce={})),function(t){t.none="none",t.max="max",t.min="min"}(Pe||(Pe={})),function(t){t.bottom="bottom",t.left="left",t.right="right",t.top="top"}(Oe||(Oe={})),function(t){t.precise="precise",t.percent="percent"}(Se||(Se={})),function(t){t.max="max",t.min="min",t.random="random"}(Te||(Te={}));const Re={debug:console.debug,error:console.error,info:console.info,log:console.log,verbose:console.log,warning:console.warn};function De(t){Re.debug=t.debug||Re.debug,Re.error=t.error||Re.error,Re.info=t.info||Re.info,Re.log=t.log||Re.log,Re.verbose=t.verbose||Re.verbose,Re.warning=t.warning||Re.warning}function Ee(){return Re}function Ie(t){const e={bounced:!1},{pSide:i,pOtherSide:s,rectSide:o,rectOtherSide:n,velocity:a,factor:r}=t;return s.min<n.min||s.min>n.max||s.max<n.min||s.max>n.max||(i.max>=o.min&&i.max<=(o.max+o.min)*v&&a>N||i.min<=o.max&&i.min>(o.max+o.min)*v&&a<N)&&(e.velocity=a*-r,e.bounced=!0),e}function Le(){return"undefined"==typeof window||!window||void 0===window.document||!window.document}function Fe(){return!Le()&&"undefined"!=typeof matchMedia}function Ae(t){if(Fe())return matchMedia(t)}function Be(t){if(!Le()&&"undefined"!=typeof IntersectionObserver)return new IntersectionObserver(t)}function qe(t){if(!Le()&&"undefined"!=typeof MutationObserver)return new MutationObserver(t)}function Ve(t,e){return t===e||Xt(e)&&e.indexOf(t)>-1}async function He(t,e){try{await document.fonts.load(`${e??"400"} 36px '${t??"Verdana"}'`)}catch{}}function Ue(t){return Math.floor(ie()*t.length)}function We(t,e,i=!0){return t[void 0!==e&&i?e%t.length:Ue(t)]}function je(t,e,i,s,o){return $e(Ge(t,s??0),e,i,o)}function $e(t,e,i,s){let o=!0;return s&&s!==Oe.bottom||(o=t.top<e.height+i.x),!o||s&&s!==Oe.left||(o=t.right>i.x),!o||s&&s!==Oe.right||(o=t.left<e.width+i.y),!o||s&&s!==Oe.top||(o=t.bottom>i.y),o}function Ge(t,e){return{bottom:t.y+e,left:t.x-e,right:t.x+e,top:t.y-e}}function Ne(t,...e){for(const i of e){if(null==i)continue;if(!Qt(i)){t=i;continue}const e=Array.isArray(i);!e||!Qt(t)&&t&&Array.isArray(t)?e||!Qt(t)&&t&&!Array.isArray(t)||(t={}):t=[];for(const e in i){if("__proto__"===e)continue;const s=i[e],o=t;o[e]=Qt(s)&&Array.isArray(s)?s.map((t=>Ne(o[e],t))):Ne(o[e],s)}}return t}function Qe(t,e){return!!si(e,(e=>e.enable&&Ve(t,e.mode)))}function Xe(t,e,i){ei(e,(e=>{const s=e.mode;e.enable&&Ve(t,s)&&Ye(e,i)}))}function Ye(t,e){ei(t.selectors,(i=>{e(i,t)}))}function Ze(t,e){if(e&&t)return si(t,(t=>function(t,e){const i=ei(e,(e=>t.matches(e)));return Xt(i)?i.some((t=>t)):i}(e,t.selectors)))}function Ke(t){return{position:t.getPosition(),radius:t.getRadius(),mass:t.getMass(),velocity:t.velocity,factor:Kt.create(le(t.options.bounce.horizontal.value),le(t.options.bounce.vertical.value))}}function Je(t,e){const{x:i,y:s}=t.velocity.sub(e.velocity),[o,n]=[t.position,e.position],{dx:a,dy:r}=pe(n,o);if(i*a+s*r<0)return;const c=-Math.atan2(r,a),l=t.mass,h=e.mass,d=t.velocity.rotate(c),u=e.velocity.rotate(c),p=ye(d,u,l,h),f=ye(u,d,l,h),g=p.rotate(-c),v=f.rotate(-c);t.velocity.x=g.x*t.factor.x,t.velocity.y=g.y*t.factor.y,e.velocity.x=v.x*e.factor.x,e.velocity.y=v.y*e.factor.y}function ti(t,e){const i=Ge(t.getPosition(),t.getRadius()),s=t.options.bounce,o=Ie({pSide:{min:i.left,max:i.right},pOtherSide:{min:i.top,max:i.bottom},rectSide:{min:e.left,max:e.right},rectOtherSide:{min:e.top,max:e.bottom},velocity:t.velocity.x,factor:le(s.horizontal.value)});o.bounced&&(void 0!==o.velocity&&(t.velocity.x=o.velocity),void 0!==o.position&&(t.position.x=o.position));const n=Ie({pSide:{min:i.top,max:i.bottom},pOtherSide:{min:i.left,max:i.right},rectSide:{min:e.top,max:e.bottom},rectOtherSide:{min:e.left,max:e.right},velocity:t.velocity.y,factor:le(s.vertical.value)});n.bounced&&(void 0!==n.velocity&&(t.velocity.y=n.velocity),void 0!==n.position&&(t.position.y=n.position))}function ei(t,e){return Xt(t)?t.map(((t,i)=>e(t,i))):e(t,0)}function ii(t,e,i){return Xt(t)?We(t,e,i):t}function si(t,e){if(Xt(t))return t.find(((t,i)=>e(t,i)));return e(t,0)?t:void 0}function oi(t,e){const i=t.value,s=t.animation,o={delayTime:le(s.delay)*m,enable:s.enable,value:le(t.value)*e,max:de(i)*e,min:he(i)*e,loops:0,maxLoops:le(s.count),time:0};if(s.enable){switch(o.decay=1-le(s.decay),s.mode){case ze.increase:o.status=Ce.increasing;break;case ze.decrease:o.status=Ce.decreasing;break;case ze.random:o.status=ie()>=v?Ce.increasing:Ce.decreasing}const t=s.mode===ze.auto;switch(s.startValue){case Te.min:o.value=o.min,t&&(o.status=Ce.increasing);break;case Te.max:o.value=o.max,t&&(o.status=Ce.decreasing);break;case Te.random:default:o.value=ce(o),t&&(o.status=ie()>=v?Ce.increasing:Ce.decreasing)}}return o.initialValue=o.value,o}function ni(t,e){if(!(t.mode===Se.percent)){const{mode:e,...i}=t;return i}return"x"in t?{x:t.x/g*e.width,y:t.y/g*e.height}:{width:t.width/g*e.width,height:t.height/g*e.height}}function ai(t,e){return ni(t,e)}function ri(t,e){return ni(t,e)}function ci(t,e,i,s,o){if(t.destroyed||!e||!e.enable||(e.maxLoops??0)>0&&(e.loops??0)>(e.maxLoops??0))return;const n=(e.velocity??0)*o.factor,a=e.min,r=e.max,c=e.decay??1;if(e.time||(e.time=0),(e.delayTime??0)>0&&e.time<(e.delayTime??0)&&(e.time+=o.value),!((e.delayTime??0)>0&&e.time<(e.delayTime??0))){switch(e.status){case Ce.increasing:e.value>=r?(i?e.status=Ce.decreasing:e.value-=r,e.loops||(e.loops=0),e.loops++):e.value+=n;break;case Ce.decreasing:e.value<=a?(i?e.status=Ce.increasing:e.value+=r,e.loops||(e.loops=0),e.loops++):e.value-=n}e.velocity&&1!==c&&(e.velocity*=c),function(t,e,i,s,o){switch(e){case Pe.max:i>=o&&t.destroy();break;case Pe.min:i<=s&&t.destroy()}}(t,s,e.value,a,r),t.destroyed||(e.value=ae(e.value,a,r))}}function li(t){const e=document.createElement("div").style;if(!t)return e;for(const i in t){const s=t[i];if(!Object.prototype.hasOwnProperty.call(t,i)||Yt(s))continue;const o=t.getPropertyValue?.(s);if(!o)continue;const n=t.getPropertyPriority?.(s);n?e.setProperty?.(s,o,n):e.setProperty?.(s,o)}return e}const hi=function(t){const e=new Map;return(...i)=>{const s=JSON.stringify(i);if(e.has(s))return e.get(s);const o=t(...i);return e.set(s,o),o}}((function(t){const e=document.createElement("div").style,i={width:"100%",height:"100%",margin:"0",padding:"0",borderWidth:"0",position:"fixed",zIndex:t.toString(10),"z-index":t.toString(10),top:"0",left:"0"};for(const t in i){const s=i[t];e.setProperty(t,s)}return e}));var di,ui,pi,fi,gi,vi,mi,yi,bi,wi,xi,_i;function ki(t,e){if(e)for(const i of t.colorManagers.values())if(e.startsWith(i.stringPrefix))return i.parseString(e)}function Mi(t,e,i,s=!0){if(!e)return;const o=$t(e)?{value:e}:e;if($t(o.value))return zi(t,o.value,i,s);if(Xt(o.value))return Mi(t,{value:We(o.value,i,s)});for(const e of t.colorManagers.values()){const t=e.handleRangeColor(o);if(t)return t}}function zi(t,e,i,s=!0){if(!e)return;const o=$t(e)?{value:e}:e;if($t(o.value))return o.value===w?Ei():Ti(t,o.value);if(Xt(o.value))return zi(t,{value:We(o.value,i,s)});for(const e of t.colorManagers.values()){const t=e.handleColor(o);if(t)return t}}function Ci(t,e,i,s=!0){const o=zi(t,e,i,s);return o?Oi(o):void 0}function Pi(t,e,i,s=!0){const o=Mi(t,e,i,s);return o?Oi(o):void 0}function Oi(t){const e=t.r/A,i=t.g/A,s=t.b/A,o=Math.max(e,i,s),n=Math.min(e,i,s),a={h:H,l:(o+n)*v,s:U};return o!==n&&(a.s=a.l<v?(o-n)/(o+n):(o-n)/(_-o-n),a.h=e===o?(i-s)/(o-n):a.h=i===o?_+(s-e)/(o-n):_*_+(e-i)/(o-n)),a.l*=V,a.s*=q,a.h*=W,a.h<H&&(a.h+=B),a.h>=B&&(a.h-=B),a}function Si(t,e){return ki(t,e)?.a}function Ti(t,e){return ki(t,e)}function Ri(t){const e=(t.h%B+B)%B,i=Math.max(U,Math.min(q,t.s)),s=Math.max(Et,Math.min(V,t.l)),o=e/B,n=i/q,a=s/V;if(i===U){const t=Math.round(a*It);return{r:t,g:t,b:t}}const r=(t,e,i)=>{if(i<0&&i++,i>1&&i--,i*Ft<1)return t+(e-t)*Ft*i;if(i*_<1)return e;if(i*Lt<1*_){return t+(e-t)*(_/Lt-i)*Ft}return t},c=a<v?a*(At+n):a+n-a*n,l=_*a-c,h=Bt/Lt,d=Math.min(It,It*r(l,c,o+h)),u=Math.min(It,It*r(l,c,o)),p=Math.min(It,It*r(l,c,o-h));return{r:Math.round(d),g:Math.round(u),b:Math.round(p)}}function Di(t){const e=Ri(t);return{a:t.a,b:e.b,g:e.g,r:e.r}}function Ei(t){const e=t??qt,i=A+Tt;return{b:Math.floor(ce(ue(e,i))),g:Math.floor(ce(ue(e,i))),r:Math.floor(ce(ue(e,i)))}}function Ii(t,e){return`rgba(${t.r}, ${t.g}, ${t.b}, ${e??K})`}function Li(t,e){return`hsla(${t.h}, ${t.s}%, ${t.l}%, ${e??K})`}function Fi(t,e,i,s){let o=t,n=e;return void 0===o.r&&(o=Ri(t)),void 0===n.r&&(n=Ri(e)),{b:re(o.b,n.b,i,s),g:re(o.g,n.g,i,s),r:re(o.r,n.r,i,s)}}function Ai(t,e,i){if(i===w)return Ei();if(i!==x)return i;{const i=t.getFillColor()??t.getStrokeColor(),s=e?.getFillColor()??e?.getStrokeColor();if(i&&s&&e)return Fi(i,s,t.getRadius(),e.getRadius());{const t=i??s;if(t)return Ri(t)}}}function Bi(t,e,i,s){const o=$t(e)?e:e.value;return o===w?s?Mi(t,{value:o}):i?w:x:o===x?x:Mi(t,{value:o})}function qi(t){return void 0!==t?{h:t.h.value,s:t.s.value,l:t.l.value}:void 0}function Vi(t,e,i){const s={h:{enable:!1,value:t.h},s:{enable:!1,value:t.s},l:{enable:!1,value:t.l}};return e&&(Hi(s.h,e.h,i),Hi(s.s,e.s,i),Hi(s.l,e.l,i)),s}function Hi(t,e,i){t.enable=e.enable,t.enable?(t.velocity=le(e.speed)/g*i,t.decay=dt-le(e.decay),t.status=Ce.increasing,t.loops=Ht,t.maxLoops=le(e.count),t.time=Ut,t.delayTime=le(e.delay)*m,e.sync||(t.velocity*=ie(),t.value*=ie()),t.initialValue=t.value,t.offset=ue(e.offset)):t.velocity=Vt}function Ui(t,e,i,s){if(!t||!t.enable||(t.maxLoops??0)>0&&(t.loops??0)>(t.maxLoops??0))return;if(t.time||(t.time=0),(t.delayTime??0)>0&&t.time<(t.delayTime??0)&&(t.time+=s.value),(t.delayTime??0)>0&&t.time<(t.delayTime??0))return;const o=t.offset?ce(t.offset):0,n=(t.velocity??0)*s.factor+3.6*o,a=t.decay??1,r=de(e),c=he(e);if(i&&t.status!==Ce.increasing){t.value-=n;const e=0;t.value<e&&(t.loops||(t.loops=0),t.loops++,t.status=Ce.increasing)}else t.value+=n,t.value>r&&(t.loops||(t.loops=0),t.loops++,i?t.status=Ce.decreasing:t.value-=r);t.velocity&&1!==a&&(t.velocity*=a),t.value=ae(t.value,c,r)}function Wi(t,e){if(!t)return;const{h:i,s,l:o}=t,n={min:0,max:100},a={min:0,max:100};i&&Ui(i,{min:0,max:360},!1,e),s&&Ui(s,n,!0,e),o&&Ui(o,a,!0,e)}function ji(t,e,i){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.closePath()}function $i(t,e,i){t.fillStyle=i??"rgba(0,0,0,0)",t.fillRect(y.x,y.y,e.width,e.height)}function Gi(t,e,i,s){i&&(t.globalAlpha=s,t.drawImage(i,y.x,y.y,e.width,e.height),t.globalAlpha=1)}function Ni(t,e){t.clearRect(y.x,y.y,e.width,e.height)}function Qi(t){const{container:e,context:i,particle:s,delta:o,colorStyles:n,backgroundMask:a,composite:r,radius:c,opacity:l,shadow:h,transform:d}=t,u=s.getPosition(),p=s.rotation+(s.pathRotation?s.velocity.angle:St),f=Math.sin(p),g=Math.cos(p),v=!!p,m={a:g*(d.a??b.a),b:v?f*(d.b??Tt):d.b??b.b,c:v?-f*(d.c??Tt):d.c??b.c,d:g*(d.d??b.d)};i.setTransform(m.a,m.b,m.c,m.d,u.x,u.y),a&&(i.globalCompositeOperation=r);const y=s.shadowColor;h.enable&&y&&(i.shadowBlur=h.blur,i.shadowColor=Ii(y),i.shadowOffsetX=h.offset.x,i.shadowOffsetY=h.offset.y),n.fill&&(i.fillStyle=n.fill);const w=s.strokeWidth??Rt;i.lineWidth=w,n.stroke&&(i.strokeStyle=n.stroke);const x={container:e,context:i,particle:s,radius:c,opacity:l,delta:o,transformData:m,strokeWidth:w};Yi(x),Zi(x),Xi(x),i.globalCompositeOperation="source-over",i.resetTransform()}function Xi(t){const{container:e,context:i,particle:s,radius:o,opacity:n,delta:a,transformData:r}=t;if(!s.effect)return;const c=e.effectDrawers.get(s.effect);c&&c.draw({context:i,particle:s,radius:o,opacity:n,delta:a,pixelRatio:e.retina.pixelRatio,transformData:{...r}})}function Yi(t){const{container:e,context:i,particle:s,radius:o,opacity:n,delta:a,strokeWidth:r,transformData:c}=t;if(!s.shape)return;const l=e.shapeDrawers.get(s.shape);l&&(i.beginPath(),l.draw({context:i,particle:s,radius:o,opacity:n,delta:a,pixelRatio:e.retina.pixelRatio,transformData:{...c}}),s.shapeClose&&i.closePath(),r>Rt&&i.stroke(),s.shapeFill&&i.fill())}function Zi(t){const{container:e,context:i,particle:s,radius:o,opacity:n,delta:a,transformData:r}=t;if(!s.shape)return;const c=e.shapeDrawers.get(s.shape);c?.afterDraw&&c.afterDraw({context:i,particle:s,radius:o,opacity:n,delta:a,pixelRatio:e.retina.pixelRatio,transformData:{...r}})}function Ki(t,e,i){e.draw&&e.draw(t,i)}function Ji(t,e,i,s){e.drawParticle&&e.drawParticle(t,i,s)}function ts(t,e,i){return{h:t.h,s:t.s,l:t.l+(e===di.darken?-Dt:Dt)*i}}function es(t,e,i){const s=e[i];void 0!==s&&(t[i]=(t[i]??Q)*s)}function is(t,e,i=!1){if(!e)return;if(!t)return;const s=t.style;if(!s)return;const o=new Set;for(const t in s)Object.prototype.hasOwnProperty.call(s,t)&&o.add(s[t]);for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&o.add(e[t]);for(const t of o){const o=e.getPropertyValue(t);o?s.setProperty(t,o,i?"important":""):s.removeProperty(t)}}!function(t){t.darken="darken",t.enlighten="enlighten"}(di||(di={}));class ss{constructor(t,e){this.container=t,this._applyPostDrawUpdaters=t=>{for(const e of this._postDrawUpdaters)e.afterDraw?.(t)},this._applyPreDrawUpdaters=(t,e,i,s,o,n)=>{for(const a of this._preDrawUpdaters){if(a.getColorStyles){const{fill:n,stroke:r}=a.getColorStyles(e,t,i,s);n&&(o.fill=n),r&&(o.stroke=r)}if(a.getTransformValues){const t=a.getTransformValues(e);for(const e in t)es(n,t,e)}a.beforeDraw?.(e)}},this._applyResizePlugins=()=>{for(const t of this._resizePlugins)t.resize?.()},this._getPluginParticleColors=t=>{let e,i;for(const s of this._colorPlugins)if(!e&&s.particleFillColor&&(e=Pi(this._engine,s.particleFillColor(t))),!i&&s.particleStrokeColor&&(i=Pi(this._engine,s.particleStrokeColor(t))),e&&i)break;return[e,i]},this._initCover=async()=>{const t=this.container.actualOptions.backgroundMask.cover,e=t.color;if(e){const i=Mi(this._engine,e);if(i){const e={...i,a:t.opacity};this._coverColorStyle=Ii(e,e.a)}}else await new Promise(((e,i)=>{if(!t.image)return;const s=document.createElement("img");s.addEventListener("load",(()=>{this._coverImage={image:s,opacity:t.opacity},e()})),s.addEventListener("error",(t=>{i(t.error)})),s.src=t.image}))},this._initStyle=()=>{const t=this.element,e=this.container.actualOptions;if(t){this._fullScreen?this._setFullScreenStyle():this._resetOriginalStyle();for(const i in e.style){if(!i||!e.style||!Object.prototype.hasOwnProperty.call(e.style,i))continue;const s=e.style[i];s&&t.style.setProperty(i,s,"important")}}},this._initTrail=async()=>{const t=this.container.actualOptions.particles.move.trail,e=t.fill;if(!t.enable)return;const i=F/t.length;if(e.color){const t=Mi(this._engine,e.color);if(!t)return;this._trailFill={color:{...t},opacity:i}}else await new Promise(((t,s)=>{if(!e.image)return;const o=document.createElement("img");o.addEventListener("load",(()=>{this._trailFill={image:o,opacity:i},t()})),o.addEventListener("error",(t=>{s(t.error)})),o.src=e.image}))},this._paintBase=t=>{this.draw((e=>$i(e,this.size,t)))},this._paintImage=(t,e)=>{this.draw((i=>Gi(i,this.size,t,e)))},this._repairStyle=()=>{const t=this.element;t&&(this._safeMutationObserver((t=>t.disconnect())),this._initStyle(),this.initBackground(),this._safeMutationObserver((e=>{t&&t instanceof Node&&e.observe(t,{attributes:!0})})))},this._resetOriginalStyle=()=>{const t=this.element,e=this._originalStyle;t&&e&&is(t,e,!0)},this._safeMutationObserver=t=>{this._mutationObserver&&t(this._mutationObserver)},this._setFullScreenStyle=()=>{const t=this.element;t&&is(t,hi(this.container.actualOptions.fullScreen.zIndex),!0)},this._engine=e,this._standardSize={height:0,width:0};const i=t.retina.pixelRatio,s=this._standardSize;this.size={height:s.height*i,width:s.width*i},this._context=null,this._generated=!1,this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}get _fullScreen(){return this.container.actualOptions.fullScreen.enable}clear(){const t=this.container.actualOptions,e=t.particles.move.trail,i=this._trailFill;t.backgroundMask.enable?this.paint():e.enable&&e.length>Y&&i?i.color?this._paintBase(Ii(i.color,i.opacity)):i.image&&this._paintImage(i.image,i.opacity):t.clear&&this.draw((t=>{Ni(t,this.size)}))}destroy(){if(this.stop(),this._generated){const t=this.element;t?.remove(),this.element=void 0}else this._resetOriginalStyle();this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}draw(t){const e=this._context;if(e)return t(e)}drawAsync(t){const e=this._context;if(e)return t(e)}drawParticle(t,e){if(t.spawning||t.destroyed)return;const i=t.getRadius();if(i<=X)return;const s=t.getFillColor(),o=t.getStrokeColor()??s;let[n,a]=this._getPluginParticleColors(t);n||(n=s),a||(a=o),(n||a)&&this.draw((s=>{const o=this.container,r=o.actualOptions,c=t.options.zIndex,l=Z-t.zIndexFactor,h=l**c.opacityRate,d=t.bubble.opacity??t.opacity?.value??K,u=d*h,p=(t.strokeOpacity??d)*h,f={},g={fill:n?Li(n,u):void 0};g.stroke=a?Li(a,p):g.fill,this._applyPreDrawUpdaters(s,t,i,u,g,f),Qi({container:o,context:s,particle:t,delta:e,colorStyles:g,backgroundMask:r.backgroundMask.enable,composite:r.backgroundMask.composite,radius:i*l**c.sizeRate,opacity:u,shadow:t.options.shadow,transform:f}),this._applyPostDrawUpdaters(t)}))}drawParticlePlugin(t,e,i){this.draw((s=>Ji(s,t,e,i)))}drawPlugin(t,e){this.draw((i=>Ki(i,t,e)))}async init(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=qe((t=>{for(const e of t)"attributes"===e.type&&"style"===e.attributeName&&this._repairStyle()})),this.resize(),this._initStyle(),await this._initCover();try{await this._initTrail()}catch(t){Ee().error(t)}this.initBackground(),this._safeMutationObserver((t=>{this.element&&this.element instanceof Node&&t.observe(this.element,{attributes:!0})})),this.initUpdaters(),this.initPlugins(),this.paint()}initBackground(){const t=this.container.actualOptions.background,e=this.element;if(!e)return;const i=e.style;if(i){if(t.color){const e=Mi(this._engine,t.color);i.backgroundColor=e?Ii(e,t.opacity):""}else i.backgroundColor="";i.backgroundImage=t.image||"",i.backgroundPosition=t.position||"",i.backgroundRepeat=t.repeat||"",i.backgroundSize=t.size||""}}initPlugins(){this._resizePlugins=[];for(const t of this.container.plugins.values())t.resize&&this._resizePlugins.push(t),(t.particleFillColor??t.particleStrokeColor)&&this._colorPlugins.push(t)}initUpdaters(){this._preDrawUpdaters=[],this._postDrawUpdaters=[];for(const t of this.container.particles.updaters)t.afterDraw&&this._postDrawUpdaters.push(t),(t.getColorStyles??t.getTransformValues??t.beforeDraw)&&this._preDrawUpdaters.push(t)}loadCanvas(t){this._generated&&this.element&&this.element.remove(),this._generated=t.dataset&&i in t.dataset?"true"===t.dataset[i]:this._generated,this.element=t,this.element.ariaHidden="true",this._originalStyle=li(this.element.style);const e=this._standardSize;e.height=t.offsetHeight,e.width=t.offsetWidth;const s=this.container.retina.pixelRatio,o=this.size;t.height=o.height=e.height*s,t.width=o.width=e.width*s,this._context=this.element.getContext("2d"),this._safeMutationObserver((t=>t.disconnect())),this.container.retina.init(),this.initBackground(),this._safeMutationObserver((t=>{this.element&&this.element instanceof Node&&t.observe(this.element,{attributes:!0})}))}paint(){const t=this.container.actualOptions;this.draw((e=>{t.backgroundMask.enable&&t.backgroundMask.cover?(Ni(e,this.size),this._coverImage?this._paintImage(this._coverImage.image,this._coverImage.opacity):this._coverColorStyle?this._paintBase(this._coverColorStyle):this._paintBase()):this._paintBase()}))}resize(){if(!this.element)return!1;const t=this.container,e=t.canvas._standardSize,i={width:this.element.offsetWidth,height:this.element.offsetHeight},s=t.retina.pixelRatio,o={width:i.width*s,height:i.height*s};if(i.height===e.height&&i.width===e.width&&o.height===this.element.height&&o.width===this.element.width)return!1;const n={...e};e.height=i.height,e.width=i.width;const a=this.size;return this.element.width=a.width=o.width,this.element.height=a.height=o.height,this.container.started&&t.particles.setResizeFactor({width:e.width/n.width,height:e.height/n.height}),!0}stop(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=void 0,this.draw((t=>Ni(t,this.size)))}async windowResize(){if(!this.element||!this.resize())return;const t=this.container,e=t.updateActualOptions();t.particles.setDensity(),this._applyResizePlugins(),e&&await t.refresh()}}function os(t,e,i,s,o){if(s){let s={passive:!0};jt(o)?s.capture=o:void 0!==o&&(s=o),t.addEventListener(e,i,s)}else{const s=o;t.removeEventListener(e,i,s)}}!function(t){t.canvas="canvas",t.parent="parent",t.window="window"}(ui||(ui={}));class ns{constructor(t){this.container=t,this._doMouseTouchClick=t=>{const e=this.container,i=e.actualOptions;if(this._canPush){const t=e.interactivity.mouse,s=t.position;if(!s)return;t.clickPosition={...s},t.clickTime=(new Date).getTime();ei(i.interactivity.events.onClick.mode,(t=>this.container.handleClickMode(t)))}"touchend"===t.type&&setTimeout((()=>this._mouseTouchFinish()),Pt)},this._handleThemeChange=t=>{const e=t,i=this.container,s=i.options,o=s.defaultThemes,n=e.matches?o.dark:o.light,a=s.themes.find((t=>t.name===n));a?.default.auto&&i.loadTheme(n)},this._handleVisibilityChange=()=>{const t=this.container,e=t.actualOptions;this._mouseTouchFinish(),e.pauseOnBlur&&(document?.hidden?(t.pageHidden=!0,t.pause()):(t.pageHidden=!1,t.animationStatus?t.play(!0):t.draw(!0)))},this._handleWindowResize=()=>{this._resizeTimeout&&(clearTimeout(this._resizeTimeout),delete this._resizeTimeout);const t=async()=>{const t=this.container.canvas;await(t?.windowResize())};this._resizeTimeout=setTimeout((()=>{t()}),this.container.actualOptions.interactivity.events.resize.delay*m)},this._manageInteractivityListeners=(t,e)=>{const i=this._handlers,n=this.container,a=n.actualOptions,u=n.interactivity.element;if(!u)return;const p=u,f=n.canvas.element;f&&(f.style.pointerEvents=p===f?"initial":"none"),(a.interactivity.events.onHover.enable||a.interactivity.events.onClick.enable)&&(os(u,r,i.mouseMove,e),os(u,c,i.touchStart,e),os(u,h,i.touchMove,e),a.interactivity.events.onClick.enable?(os(u,l,i.touchEndClick,e),os(u,o,i.mouseUp,e),os(u,s,i.mouseDown,e)):os(u,l,i.touchEnd,e),os(u,t,i.mouseLeave,e),os(u,d,i.touchCancel,e))},this._manageListeners=t=>{const e=this._handlers,i=this.container,s=i.actualOptions.interactivity.detectsOn,o=i.canvas.element;let r=n;s===ui.window?(i.interactivity.element=window,r=a):s===ui.parent&&o?i.interactivity.element=o.parentElement??o.parentNode:i.interactivity.element=o,this._manageMediaMatch(t),this._manageResize(t),this._manageInteractivityListeners(r,t),document&&os(document,p,e.visibilityChange,t,!1)},this._manageMediaMatch=t=>{const e=this._handlers,i=Ae("(prefers-color-scheme: dark)");i&&(void 0===i.addEventListener?void 0!==i.addListener&&(t?i.addListener(e.oldThemeChange):i.removeListener(e.oldThemeChange)):os(i,"change",e.themeChange,t))},this._manageResize=t=>{const e=this._handlers,i=this.container;if(!i.actualOptions.interactivity.events.resize)return;if("undefined"==typeof ResizeObserver)return void os(window,u,e.resize,t);const s=i.canvas.element;this._resizeObserver&&!t?(s&&this._resizeObserver.unobserve(s),this._resizeObserver.disconnect(),delete this._resizeObserver):!this._resizeObserver&&t&&s&&(this._resizeObserver=new ResizeObserver((t=>{t.find((t=>t.target===s))&&this._handleWindowResize()})),this._resizeObserver.observe(s))},this._mouseDown=()=>{const{interactivity:t}=this.container;if(!t)return;const{mouse:e}=t;e.clicking=!0,e.downPosition=e.position},this._mouseTouchClick=t=>{const e=this.container,i=e.actualOptions,{mouse:s}=e.interactivity;s.inside=!0;let o=!1;const n=s.position;if(n&&i.interactivity.events.onClick.enable){for(const t of e.plugins.values())if(t.clickPositionValid&&(o=t.clickPositionValid(n),o))break;o||this._doMouseTouchClick(t),s.clicking=!1}},this._mouseTouchFinish=()=>{const t=this.container.interactivity;if(!t)return;const e=t.mouse;delete e.position,delete e.clickPosition,delete e.downPosition,t.status=n,e.inside=!1,e.clicking=!1},this._mouseTouchMove=t=>{const e=this.container,i=e.actualOptions,s=e.interactivity,o=e.canvas.element;if(!s?.element)return;let n;if(s.mouse.inside=!0,t.type.startsWith("pointer")){this._canPush=!0;const e=t;if(s.element===window){if(o){const t=o.getBoundingClientRect();n={x:e.clientX-t.left,y:e.clientY-t.top}}}else if(i.interactivity.detectsOn===ui.parent){const t=e.target,i=e.currentTarget;if(t&&i&&o){const s=t.getBoundingClientRect(),a=i.getBoundingClientRect(),r=o.getBoundingClientRect();n={x:e.offsetX+_*s.left-(a.left+r.left),y:e.offsetY+_*s.top-(a.top+r.top)}}else n={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY}}else e.target===o&&(n={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY})}else if(this._canPush="touchmove"!==t.type,o){const e=t,i=e.touches[e.touches.length-Mt],s=o.getBoundingClientRect();n={x:i.clientX-(s.left??et),y:i.clientY-(s.top??et)}}const a=e.retina.pixelRatio;n&&(n.x*=a,n.y*=a),s.mouse.position=n,s.status=r},this._touchEnd=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchFinish()},this._touchEndClick=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchClick(t)},this._touchStart=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.set(t.identifier,performance.now());this._mouseTouchMove(t)},this._canPush=!0,this._touches=new Map,this._handlers={mouseDown:()=>this._mouseDown(),mouseLeave:()=>this._mouseTouchFinish(),mouseMove:t=>this._mouseTouchMove(t),mouseUp:t=>this._mouseTouchClick(t),touchStart:t=>this._touchStart(t),touchMove:t=>this._mouseTouchMove(t),touchEnd:t=>this._touchEnd(t),touchCancel:t=>this._touchEnd(t),touchEndClick:t=>this._touchEndClick(t),visibilityChange:()=>this._handleVisibilityChange(),themeChange:t=>this._handleThemeChange(t),oldThemeChange:t=>this._handleThemeChange(t),resize:()=>{this._handleWindowResize()}}}addListeners(){this._manageListeners(!0)}removeListeners(){this._manageListeners(!1)}}!function(t){t.configAdded="configAdded",t.containerInit="containerInit",t.particlesSetup="particlesSetup",t.containerStarted="containerStarted",t.containerStopped="containerStopped",t.containerDestroyed="containerDestroyed",t.containerPaused="containerPaused",t.containerPlay="containerPlay",t.containerBuilt="containerBuilt",t.particleAdded="particleAdded",t.particleDestroyed="particleDestroyed",t.particleRemoved="particleRemoved"}(pi||(pi={}));class as{constructor(){this.value=""}static create(t,e){const i=new as;return i.load(t),void 0!==e&&($t(e)||Xt(e)?i.load({value:e}):i.load(e)),i}load(t){Yt(t)||Yt(t.value)||(this.value=t.value)}}class rs{constructor(){this.color=new as,this.color.value="",this.image="",this.position="",this.repeat="",this.size="",this.opacity=1}load(t){Yt(t)||(void 0!==t.color&&(this.color=as.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.position&&(this.position=t.position),void 0!==t.repeat&&(this.repeat=t.repeat),void 0!==t.size&&(this.size=t.size),void 0!==t.opacity&&(this.opacity=t.opacity))}}class cs{constructor(){this.opacity=1}load(t){Yt(t)||(void 0!==t.color&&(this.color=as.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.opacity&&(this.opacity=t.opacity))}}class ls{constructor(){this.composite="destination-out",this.cover=new cs,this.enable=!1}load(t){if(!Yt(t)){if(void 0!==t.composite&&(this.composite=t.composite),void 0!==t.cover){const e=t.cover,i=$t(t.cover)?{color:t.cover}:t.cover;this.cover.load(void 0!==e.color||void 0!==e.image?e:{color:i})}void 0!==t.enable&&(this.enable=t.enable)}}}class hs{constructor(){this.enable=!0,this.zIndex=0}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.zIndex&&(this.zIndex=t.zIndex))}}class ds{constructor(){this.enable=!1,this.mode=[]}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode))}}!function(t){t.circle="circle",t.rectangle="rectangle"}(fi||(fi={}));class us{constructor(){this.selectors=[],this.enable=!1,this.mode=[],this.type=fi.circle}load(t){Yt(t)||(void 0!==t.selectors&&(this.selectors=t.selectors),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.type&&(this.type=t.type))}}class ps{constructor(){this.enable=!1,this.force=2,this.smooth=10}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.force&&(this.force=t.force),void 0!==t.smooth&&(this.smooth=t.smooth))}}class fs{constructor(){this.enable=!1,this.mode=[],this.parallax=new ps}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),this.parallax.load(t.parallax))}}class gs{constructor(){this.delay=.5,this.enable=!0}load(t){Yt(t)||(void 0!==t.delay&&(this.delay=t.delay),void 0!==t.enable&&(this.enable=t.enable))}}class vs{constructor(){this.onClick=new ds,this.onDiv=new us,this.onHover=new fs,this.resize=new gs}load(t){if(Yt(t))return;this.onClick.load(t.onClick);const e=t.onDiv;void 0!==e&&(this.onDiv=ei(e,(t=>{const e=new us;return e.load(t),e}))),this.onHover.load(t.onHover),this.resize.load(t.resize)}}class ms{constructor(t,e){this._engine=t,this._container=e}load(t){if(Yt(t))return;if(!this._container)return;const e=this._engine.interactors.get(this._container);if(e)for(const i of e)i.loadModeOptions&&i.loadModeOptions(this,t)}}class ys{constructor(t,e){this.detectsOn=ui.window,this.events=new vs,this.modes=new ms(t,e)}load(t){if(Yt(t))return;const e=t.detectsOn;void 0!==e&&(this.detectsOn=e),this.events.load(t.events),this.modes.load(t.modes)}}class bs{load(t){Yt(t)||(t.position&&(this.position={x:t.position.x??Ot,y:t.position.y??Ot,mode:t.position.mode??Se.percent}),t.options&&(this.options=Ne({},t.options)))}}!function(t){t.screen="screen",t.canvas="canvas"}(gi||(gi={}));class ws{constructor(){this.maxWidth=1/0,this.options={},this.mode=gi.canvas}load(t){Yt(t)||(Yt(t.maxWidth)||(this.maxWidth=t.maxWidth),Yt(t.mode)||(t.mode===gi.screen?this.mode=gi.screen:this.mode=gi.canvas),Yt(t.options)||(this.options=Ne({},t.options)))}}!function(t){t.any="any",t.dark="dark",t.light="light"}(vi||(vi={}));class xs{constructor(){this.auto=!1,this.mode=vi.any,this.value=!1}load(t){Yt(t)||(void 0!==t.auto&&(this.auto=t.auto),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.value&&(this.value=t.value))}}class _s{constructor(){this.name="",this.default=new xs}load(t){Yt(t)||(void 0!==t.name&&(this.name=t.name),this.default.load(t.default),void 0!==t.options&&(this.options=Ne({},t.options)))}}class ks{constructor(){this.count=0,this.enable=!1,this.speed=1,this.decay=0,this.delay=0,this.sync=!1}load(t){Yt(t)||(void 0!==t.count&&(this.count=ue(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=ue(t.speed)),void 0!==t.decay&&(this.decay=ue(t.decay)),void 0!==t.delay&&(this.delay=ue(t.delay)),void 0!==t.sync&&(this.sync=t.sync))}}class Ms extends ks{constructor(){super(),this.mode=ze.auto,this.startValue=Te.random}load(t){super.load(t),Yt(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.startValue&&(this.startValue=t.startValue))}}class zs extends ks{constructor(){super(),this.offset=0,this.sync=!0}load(t){super.load(t),Yt(t)||void 0!==t.offset&&(this.offset=ue(t.offset))}}class Cs{constructor(){this.h=new zs,this.s=new zs,this.l=new zs}load(t){Yt(t)||(this.h.load(t.h),this.s.load(t.s),this.l.load(t.l))}}class Ps extends as{constructor(){super(),this.animation=new Cs}static create(t,e){const i=new Ps;return i.load(t),void 0!==e&&($t(e)||Xt(e)?i.load({value:e}):i.load(e)),i}load(t){if(super.load(t),Yt(t))return;const e=t.animation;void 0!==e&&(void 0!==e.enable?this.animation.h.load(e):this.animation.load(t.animation))}}!function(t){t.absorb="absorb",t.bounce="bounce",t.destroy="destroy"}(mi||(mi={}));class Os{constructor(){this.speed=2}load(t){Yt(t)||void 0!==t.speed&&(this.speed=t.speed)}}class Ss{constructor(){this.enable=!0,this.retries=0}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.retries&&(this.retries=t.retries))}}class Ts{constructor(){this.value=0}load(t){Yt(t)||Yt(t.value)||(this.value=ue(t.value))}}class Rs extends Ts{constructor(){super(),this.animation=new ks}load(t){if(super.load(t),Yt(t))return;const e=t.animation;void 0!==e&&this.animation.load(e)}}class Ds extends Rs{constructor(){super(),this.animation=new Ms}load(t){super.load(t)}}class Es extends Ts{constructor(){super(),this.value=1}}class Is{constructor(){this.horizontal=new Es,this.vertical=new Es}load(t){Yt(t)||(this.horizontal.load(t.horizontal),this.vertical.load(t.vertical))}}class Ls{constructor(){this.absorb=new Os,this.bounce=new Is,this.enable=!1,this.maxSpeed=50,this.mode=mi.bounce,this.overlap=new Ss}load(t){Yt(t)||(this.absorb.load(t.absorb),this.bounce.load(t.bounce),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.maxSpeed&&(this.maxSpeed=ue(t.maxSpeed)),void 0!==t.mode&&(this.mode=t.mode),this.overlap.load(t.overlap))}}class Fs{constructor(){this.close=!0,this.fill=!0,this.options={},this.type=[]}load(t){if(Yt(t))return;const e=t.options;if(void 0!==e)for(const t in e){const i=e[t];i&&(this.options[t]=Ne(this.options[t]??{},i))}void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class As{constructor(){this.offset=0,this.value=90}load(t){Yt(t)||(void 0!==t.offset&&(this.offset=ue(t.offset)),void 0!==t.value&&(this.value=ue(t.value)))}}class Bs{constructor(){this.distance=200,this.enable=!1,this.rotate={x:3e3,y:3e3}}load(t){if(!Yt(t)&&(void 0!==t.distance&&(this.distance=ue(t.distance)),void 0!==t.enable&&(this.enable=t.enable),t.rotate)){const e=t.rotate.x;void 0!==e&&(this.rotate.x=e);const i=t.rotate.y;void 0!==i&&(this.rotate.y=i)}}}class qs{constructor(){this.x=50,this.y=50,this.mode=Se.percent,this.radius=0}load(t){Yt(t)||(void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.radius&&(this.radius=t.radius))}}class Vs{constructor(){this.acceleration=9.81,this.enable=!1,this.inverse=!1,this.maxSpeed=50}load(t){Yt(t)||(void 0!==t.acceleration&&(this.acceleration=ue(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.inverse&&(this.inverse=t.inverse),void 0!==t.maxSpeed&&(this.maxSpeed=ue(t.maxSpeed)))}}class Hs{constructor(){this.clamp=!0,this.delay=new Ts,this.enable=!1,this.options={}}load(t){Yt(t)||(void 0!==t.clamp&&(this.clamp=t.clamp),this.delay.load(t.delay),void 0!==t.enable&&(this.enable=t.enable),this.generator=t.generator,t.options&&(this.options=Ne(this.options,t.options)))}}class Us{load(t){Yt(t)||(void 0!==t.color&&(this.color=as.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image))}}class Ws{constructor(){this.enable=!1,this.length=10,this.fill=new Us}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.fill&&this.fill.load(t.fill),void 0!==t.length&&(this.length=t.length))}}!function(t){t.bounce="bounce",t.none="none",t.out="out",t.destroy="destroy",t.split="split"}(yi||(yi={}));class js{constructor(){this.default=yi.out}load(t){Yt(t)||(void 0!==t.default&&(this.default=t.default),this.bottom=t.bottom??t.default,this.left=t.left??t.default,this.right=t.right??t.default,this.top=t.top??t.default)}}class $s{constructor(){this.acceleration=0,this.enable=!1}load(t){Yt(t)||(void 0!==t.acceleration&&(this.acceleration=ue(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),t.position&&(this.position=Ne({},t.position)))}}class Gs{constructor(){this.angle=new As,this.attract=new Bs,this.center=new qs,this.decay=0,this.distance={},this.direction=Wt.none,this.drift=0,this.enable=!1,this.gravity=new Vs,this.path=new Hs,this.outModes=new js,this.random=!1,this.size=!1,this.speed=2,this.spin=new $s,this.straight=!1,this.trail=new Ws,this.vibrate=!1,this.warp=!1}load(t){if(Yt(t))return;this.angle.load(Gt(t.angle)?{value:t.angle}:t.angle),this.attract.load(t.attract),this.center.load(t.center),void 0!==t.decay&&(this.decay=ue(t.decay)),void 0!==t.direction&&(this.direction=t.direction),void 0!==t.distance&&(this.distance=Gt(t.distance)?{horizontal:t.distance,vertical:t.distance}:{...t.distance}),void 0!==t.drift&&(this.drift=ue(t.drift)),void 0!==t.enable&&(this.enable=t.enable),this.gravity.load(t.gravity);const e=t.outModes;void 0!==e&&(Qt(e)?this.outModes.load(e):this.outModes.load({default:e})),this.path.load(t.path),void 0!==t.random&&(this.random=t.random),void 0!==t.size&&(this.size=t.size),void 0!==t.speed&&(this.speed=ue(t.speed)),this.spin.load(t.spin),void 0!==t.straight&&(this.straight=t.straight),this.trail.load(t.trail),void 0!==t.vibrate&&(this.vibrate=t.vibrate),void 0!==t.warp&&(this.warp=t.warp)}}class Ns extends Ms{constructor(){super(),this.destroy=Pe.none,this.speed=2}load(t){super.load(t),Yt(t)||void 0!==t.destroy&&(this.destroy=t.destroy)}}class Qs extends Ds{constructor(){super(),this.animation=new Ns,this.value=1}load(t){if(Yt(t))return;super.load(t);const e=t.animation;void 0!==e&&this.animation.load(e)}}class Xs{constructor(){this.enable=!1,this.width=1920,this.height=1080}load(t){if(Yt(t))return;void 0!==t.enable&&(this.enable=t.enable);const e=t.width;void 0!==e&&(this.width=e);const i=t.height;void 0!==i&&(this.height=i)}}!function(t){t.delete="delete",t.wait="wait"}(bi||(bi={}));class Ys{constructor(){this.mode=bi.delete,this.value=0}load(t){Yt(t)||(void 0!==t.mode&&(this.mode=t.mode),void 0!==t.value&&(this.value=t.value))}}class Zs{constructor(){this.density=new Xs,this.limit=new Ys,this.value=0}load(t){Yt(t)||(this.density.load(t.density),this.limit.load(t.limit),void 0!==t.value&&(this.value=t.value))}}class Ks{constructor(){this.blur=0,this.color=new as,this.enable=!1,this.offset={x:0,y:0},this.color.value="#000"}load(t){Yt(t)||(void 0!==t.blur&&(this.blur=t.blur),this.color=as.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(void 0!==t.offset.x&&(this.offset.x=t.offset.x),void 0!==t.offset.y&&(this.offset.y=t.offset.y)))}}class Js{constructor(){this.close=!0,this.fill=!0,this.options={},this.type="circle"}load(t){if(Yt(t))return;const e=t.options;if(void 0!==e)for(const t in e){const i=e[t];i&&(this.options[t]=Ne(this.options[t]??{},i))}void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class to extends Ms{constructor(){super(),this.destroy=Pe.none,this.speed=5}load(t){super.load(t),Yt(t)||void 0!==t.destroy&&(this.destroy=t.destroy)}}class eo extends Ds{constructor(){super(),this.animation=new to,this.value=3}load(t){if(super.load(t),Yt(t))return;const e=t.animation;void 0!==e&&this.animation.load(e)}}class io{constructor(){this.width=0}load(t){Yt(t)||(void 0!==t.color&&(this.color=Ps.create(this.color,t.color)),void 0!==t.width&&(this.width=ue(t.width)),void 0!==t.opacity&&(this.opacity=ue(t.opacity)))}}class so extends Ts{constructor(){super(),this.opacityRate=1,this.sizeRate=1,this.velocityRate=1}load(t){super.load(t),Yt(t)||(void 0!==t.opacityRate&&(this.opacityRate=t.opacityRate),void 0!==t.sizeRate&&(this.sizeRate=t.sizeRate),void 0!==t.velocityRate&&(this.velocityRate=t.velocityRate))}}class oo{constructor(t,e){this._engine=t,this._container=e,this.bounce=new Is,this.collisions=new Ls,this.color=new Ps,this.color.value="#fff",this.effect=new Fs,this.groups={},this.move=new Gs,this.number=new Zs,this.opacity=new Qs,this.reduceDuplicates=!1,this.shadow=new Ks,this.shape=new Js,this.size=new eo,this.stroke=new io,this.zIndex=new so}load(t){if(Yt(t))return;if(void 0!==t.groups)for(const e of Object.keys(t.groups)){if(!Object.hasOwn(t.groups,e))continue;const i=t.groups[e];void 0!==i&&(this.groups[e]=Ne(this.groups[e]??{},i))}void 0!==t.reduceDuplicates&&(this.reduceDuplicates=t.reduceDuplicates),this.bounce.load(t.bounce),this.color.load(Ps.create(this.color,t.color)),this.effect.load(t.effect),this.move.load(t.move),this.number.load(t.number),this.opacity.load(t.opacity),this.shape.load(t.shape),this.size.load(t.size),this.shadow.load(t.shadow),this.zIndex.load(t.zIndex),this.collisions.load(t.collisions),void 0!==t.interactivity&&(this.interactivity=Ne({},t.interactivity));const e=t.stroke;if(e&&(this.stroke=ei(e,(t=>{const e=new io;return e.load(t),e}))),this._container){const e=this._engine.updaters.get(this._container);if(e)for(const i of e)i.loadOptions&&i.loadOptions(this,t);const i=this._engine.interactors.get(this._container);if(i)for(const e of i)e.loadParticlesOptions&&e.loadParticlesOptions(this,t)}}}function no(t,...e){for(const i of e)t.load(i)}function ao(t,e,...i){const s=new oo(t,e);return no(s,...i),s}class ro{constructor(t,e){this._findDefaultTheme=t=>this.themes.find((e=>e.default.value&&e.default.mode===t))??this.themes.find((t=>t.default.value&&t.default.mode===vi.any)),this._importPreset=t=>{this.load(this._engine.getPreset(t))},this._engine=t,this._container=e,this.autoPlay=!0,this.background=new rs,this.backgroundMask=new ls,this.clear=!0,this.defaultThemes={},this.delay=0,this.fullScreen=new hs,this.detectRetina=!0,this.duration=0,this.fpsLimit=120,this.interactivity=new ys(t,e),this.manualParticles=[],this.particles=ao(this._engine,this._container),this.pauseOnBlur=!0,this.pauseOnOutsideViewport=!0,this.responsive=[],this.smooth=!1,this.style={},this.themes=[],this.zLayers=100}load(t){if(Yt(t))return;void 0!==t.preset&&ei(t.preset,(t=>this._importPreset(t))),void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.clear&&(this.clear=t.clear),void 0!==t.key&&(this.key=t.key),void 0!==t.name&&(this.name=t.name),void 0!==t.delay&&(this.delay=ue(t.delay));const e=t.detectRetina;void 0!==e&&(this.detectRetina=e),void 0!==t.duration&&(this.duration=ue(t.duration));const i=t.fpsLimit;void 0!==i&&(this.fpsLimit=i),void 0!==t.pauseOnBlur&&(this.pauseOnBlur=t.pauseOnBlur),void 0!==t.pauseOnOutsideViewport&&(this.pauseOnOutsideViewport=t.pauseOnOutsideViewport),void 0!==t.zLayers&&(this.zLayers=t.zLayers),this.background.load(t.background);const s=t.fullScreen;jt(s)?this.fullScreen.enable=s:this.fullScreen.load(s),this.backgroundMask.load(t.backgroundMask),this.interactivity.load(t.interactivity),t.manualParticles&&(this.manualParticles=t.manualParticles.map((t=>{const e=new bs;return e.load(t),e}))),this.particles.load(t.particles),this.style=Ne(this.style,t.style),this._engine.loadOptions(this,t),void 0!==t.smooth&&(this.smooth=t.smooth);const o=this._engine.interactors.get(this._container);if(o)for(const e of o)e.loadOptions&&e.loadOptions(this,t);if(void 0!==t.responsive)for(const e of t.responsive){const t=new ws;t.load(e),this.responsive.push(t)}if(this.responsive.sort(((t,e)=>t.maxWidth-e.maxWidth)),void 0!==t.themes)for(const e of t.themes){const t=this.themes.find((t=>t.name===e.name));if(t)t.load(e);else{const t=new _s;t.load(e),this.themes.push(t)}}this.defaultThemes.dark=this._findDefaultTheme(vi.dark)?.name,this.defaultThemes.light=this._findDefaultTheme(vi.light)?.name}setResponsive(t,e,i){this.load(i);const s=this.responsive.find((i=>i.mode===gi.screen&&screen?i.maxWidth>screen.availWidth:i.maxWidth*e>t));return this.load(s?.options),s?.maxWidth}setTheme(t){if(t){const e=this.themes.find((e=>e.name===t));e&&this.load(e.options)}else{const t=Ae("(prefers-color-scheme: dark)"),e=t?.matches,i=this._findDefaultTheme(e?vi.dark:vi.light);i&&this.load(i.options)}}}!function(t){t.external="external",t.particles="particles"}(wi||(wi={}));class co{constructor(t,e){this.container=e,this._engine=t,this._interactors=[],this._externalInteractors=[],this._particleInteractors=[]}externalInteract(t){for(const e of this._externalInteractors)e.isEnabled()&&e.interact(t)}handleClickMode(t){for(const e of this._externalInteractors)e.handleClickMode?.(t)}async init(){this._interactors=await this._engine.getInteractors(this.container,!0),this._externalInteractors=[],this._particleInteractors=[];for(const t of this._interactors){switch(t.type){case wi.external:this._externalInteractors.push(t);break;case wi.particles:this._particleInteractors.push(t)}t.init()}}particlesInteract(t,e){for(const i of this._externalInteractors)i.clear(t,e);for(const i of this._particleInteractors)i.isEnabled(t)&&i.interact(t,e)}reset(t){for(const e of this._externalInteractors)e.isEnabled()&&e.reset(t);for(const e of this._particleInteractors)e.isEnabled(t)&&e.reset(t)}}function lo(t){if(!Ve(t.outMode,t.checkModes))return;const e=t.radius*_;t.coord>t.maxCoord-e?t.setCb(-t.radius):t.coord<e&&t.setCb(t.radius)}!function(t){t.normal="normal",t.inside="inside",t.outside="outside"}(xi||(xi={}));class ho{constructor(t,e){this.container=e,this._calcPosition=(t,e,i,s=S)=>{for(const s of t.plugins.values()){const t=void 0!==s.particlePosition?s.particlePosition(e,this):void 0;if(t)return Zt.create(t.x,t.y,i)}const o=_e({size:t.canvas.size,position:e}),n=Zt.create(o.x,o.y,i),a=this.getRadius(),r=this.options.move.outModes,c=e=>{lo({outMode:e,checkModes:[yi.bounce],coord:n.x,maxCoord:t.canvas.size.width,setCb:t=>n.x+=t,radius:a})},l=e=>{lo({outMode:e,checkModes:[yi.bounce],coord:n.y,maxCoord:t.canvas.size.height,setCb:t=>n.y+=t,radius:a})};return c(r.left??r.default),c(r.right??r.default),l(r.top??r.default),l(r.bottom??r.default),this._checkOverlap(n,s)?this._calcPosition(t,void 0,i,s+ut):n},this._calculateVelocity=()=>{const t=me(this.direction).copy(),e=this.options.move;if(e.direction===Wt.inside||e.direction===Wt.outside)return t;const i=ge(le(e.angle.value)),s=ge(le(e.angle.offset)),o={left:s-i*v,right:s+i*v};return e.straight||(t.angle+=ce(ue(o.left,o.right))),e.random&&"number"==typeof e.speed&&(t.length*=ie()),t},this._checkOverlap=(t,e=S)=>{const i=this.options.collisions,s=this.getRadius();if(!i.enable)return!1;const o=i.overlap;if(o.enable)return!1;const n=o.retries;if(n>=pt&&e>n)throw new Error(`${f} particle is overlapping and can't be placed`);return!!this.container.particles.find((e=>fe(t,e.position)<s+e.getRadius()))},this._getRollColor=t=>{if(!t||!this.roll||!this.backColor&&!this.roll.alter)return t;const e=this.roll.horizontal&&this.roll.vertical?_*ft:ft,i=this.roll.horizontal?Math.PI*v:ht;return Math.floor(((this.roll.angle??ht)+i)/(Math.PI/e))%_?this.backColor?this.backColor:this.roll.alter?ts(t,this.roll.alter.type,this.roll.alter.value):t:t},this._initPosition=t=>{const e=this.container,i=le(this.options.zIndex.value);this.position=this._calcPosition(e,t,ae(i,gt,e.zLayers)),this.initialPosition=this.position.copy();const s=e.canvas.size;switch(this.moveCenter={...ai(this.options.move.center,s),radius:this.options.move.center.radius??vt,mode:this.options.move.center.mode??Se.percent},this.direction=ve(this.options.move.direction,this.position,this.moveCenter),this.options.move.direction){case Wt.inside:this.outType=xi.inside;break;case Wt.outside:this.outType=xi.outside}this.offset=Kt.origin},this._engine=t}destroy(t){if(this.unbreakable||this.destroyed)return;this.destroyed=!0,this.bubble.inRange=!1,this.slow.inRange=!1;const e=this.container,i=this.pathGenerator,s=e.shapeDrawers.get(this.shape);s?.particleDestroy?.(this);for(const i of e.plugins.values())i.particleDestroyed?.(this,t);for(const i of e.particles.updaters)i.particleDestroyed?.(this,t);i?.reset(this),this._engine.dispatchEvent(pi.particleDestroyed,{container:this.container,data:{particle:this}})}draw(t){const e=this.container,i=e.canvas;for(const s of e.plugins.values())i.drawParticlePlugin(s,this,t);i.drawParticle(this,t)}getFillColor(){return this._getRollColor(this.bubble.color??qi(this.color))}getMass(){return this.getRadius()**T*Math.PI*v}getPosition(){return{x:this.position.x+this.offset.x,y:this.position.y+this.offset.y,z:this.position.z}}getRadius(){return this.bubble.radius??this.size.value}getStrokeColor(){return this._getRollColor(this.bubble.color??qi(this.strokeColor))}init(t,e,i,s){const o=this.container,n=this._engine;this.id=t,this.group=s,this.effectClose=!0,this.effectFill=!0,this.shapeClose=!0,this.shapeFill=!0,this.pathRotation=!1,this.lastPathTime=0,this.destroyed=!1,this.unbreakable=!1,this.isRotating=!1,this.rotation=0,this.misplaced=!1,this.retina={maxDistance:{}},this.outType=xi.normal,this.ignoresResizeRatio=!0;const a=o.retina.pixelRatio,r=o.actualOptions,c=ao(this._engine,o,r.particles),{reduceDuplicates:l}=c,h=c.effect.type,d=c.shape.type;this.effect=ii(h,this.id,l),this.shape=ii(d,this.id,l);const u=c.effect,p=c.shape;if(i){if(i.effect?.type){const t=ii(i.effect.type,this.id,l);t&&(this.effect=t,u.load(i.effect))}if(i.shape?.type){const t=ii(i.shape.type,this.id,l);t&&(this.shape=t,p.load(i.shape))}}if(this.effect===w){const t=[...this.container.effectDrawers.keys()];this.effect=t[Math.floor(Math.random()*t.length)]}if(this.shape===w){const t=[...this.container.shapeDrawers.keys()];this.shape=t[Math.floor(Math.random()*t.length)]}this.effectData=function(t,e,i,s){const o=e.options[t];if(o)return Ne({close:e.close,fill:e.fill},ii(o,i,s))}(this.effect,u,this.id,l),this.shapeData=function(t,e,i,s){const o=e.options[t];if(o)return Ne({close:e.close,fill:e.fill},ii(o,i,s))}(this.shape,p,this.id,l),c.load(i);const f=this.effectData;f&&c.load(f.particles);const g=this.shapeData;g&&c.load(g.particles);const v=new ys(n,o);v.load(o.actualOptions.interactivity),v.load(c.interactivity),this.interactivity=v,this.effectFill=f?.fill??c.effect.fill,this.effectClose=f?.close??c.effect.close,this.shapeFill=g?.fill??c.shape.fill,this.shapeClose=g?.close??c.shape.close,this.options=c;const y=this.options.move.path;this.pathDelay=le(y.delay.value)*m,y.generator&&(this.pathGenerator=this._engine.getPathGenerator(y.generator),this.pathGenerator&&o.addPath(y.generator,this.pathGenerator)&&this.pathGenerator.init(o)),o.retina.initParticle(this),this.size=oi(this.options.size,a),this.bubble={inRange:!1},this.slow={inRange:!1,factor:1},this._initPosition(e),this.initialVelocity=this._calculateVelocity(),this.velocity=this.initialVelocity.copy(),this.moveDecay=dt-le(this.options.move.decay);const b=o.particles;b.setLastZIndex(this.position.z),this.zIndexFactor=this.position.z/o.zLayers,this.sides=24;let x=o.effectDrawers.get(this.effect);x||(x=this._engine.getEffectDrawer(this.effect),x&&o.effectDrawers.set(this.effect,x)),x?.loadEffect&&x.loadEffect(this);let _=o.shapeDrawers.get(this.shape);_||(_=this._engine.getShapeDrawer(this.shape),_&&o.shapeDrawers.set(this.shape,_)),_?.loadShape&&_.loadShape(this);const k=_?.getSidesCount;k&&(this.sides=k(this)),this.spawning=!1,this.shadowColor=Mi(this._engine,this.options.shadow.color);for(const t of b.updaters)t.init(this);for(const t of b.movers)t.init?.(this);x?.particleInit?.(o,this),_?.particleInit?.(o,this);for(const t of o.plugins.values())t.particleCreated?.(this)}isInsideCanvas(){const t=this.getRadius(),e=this.container.canvas.size,i=this.position;return i.x>=-t&&i.y>=-t&&i.y<=e.height+t&&i.x<=e.width+t}isVisible(){return!this.destroyed&&!this.spawning&&this.isInsideCanvas()}reset(){for(const t of this.container.particles.updaters)t.reset?.(this)}}class uo{constructor(t,e){this.position=t,this.particle=e}}!function(t){t.circle="circle",t.rectangle="rectangle"}(_i||(_i={}));class po{constructor(t,e,i){this.position={x:t,y:e},this.type=i}}class fo extends po{constructor(t,e,i){super(t,e,_i.circle),this.radius=i}contains(t){return fe(t,this.position)<=this.radius}intersects(t){const e=this.position,i=t.position,s=Math.abs(i.x-e.x),o=Math.abs(i.y-e.y),n=this.radius;if(t instanceof fo||t.type===_i.circle){return n+t.radius>Math.sqrt(s**T+o**T)}if(t instanceof go||t.type===_i.rectangle){const e=t,{width:i,height:a}=e.size;return Math.pow(s-i,T)+Math.pow(o-a,T)<=n**T||s<=n+i&&o<=n+a||s<=i||o<=a}return!1}}class go extends po{constructor(t,e,i,s){super(t,e,_i.rectangle),this.size={height:s,width:i}}contains(t){const e=this.size.width,i=this.size.height,s=this.position;return t.x>=s.x&&t.x<=s.x+e&&t.y>=s.y&&t.y<=s.y+i}intersects(t){if(t instanceof fo)return t.intersects(this);const e=this.size.width,i=this.size.height,s=this.position,o=t.position,n=t instanceof go?t.size:{width:0,height:0},a=n.width,r=n.height;return o.x<s.x+e&&o.x+a>s.x&&o.y<s.y+i&&o.y+r>s.y}}class vo{constructor(t,e){this.rectangle=t,this.capacity=e,this._subdivide=()=>{const{x:t,y:e}=this.rectangle.position,{width:i,height:s}=this.rectangle.size,{capacity:o}=this;for(let n=0;n<L;n++){const a=n%_;this._subs.push(new vo(new go(t+i*v*a,e+s*v*(Math.round(n*v)-a),i*v,s*v),o))}this._divided=!0},this._points=[],this._divided=!1,this._subs=[]}insert(t){return!!this.rectangle.contains(t.position)&&(this._points.length<this.capacity?(this._points.push(t),!0):(this._divided||this._subdivide(),this._subs.some((e=>e.insert(t)))))}query(t,e){const i=[];if(!t.intersects(this.rectangle))return[];for(const s of this._points)!t.contains(s.position)&&fe(t.position,s.position)>s.particle.getRadius()&&(!e||e(s.particle))||i.push(s.particle);if(this._divided)for(const s of this._subs)i.push(...s.query(t,e));return i}queryCircle(t,e,i){return this.query(new fo(t.x,t.y,e),i)}queryRectangle(t,e,i){return this.query(new go(t.x,t.y,e.width,e.height),i)}}const mo=t=>{const{height:e,width:i}=t;return new go(mt*i,mt*e,yt*i,yt*e)};class yo{constructor(t,e){this._addToPool=(...t)=>{this._pool.push(...t)},this._applyDensity=(t,e,i)=>{const s=t.number;if(!t.number.density?.enable)return void(void 0===i?this._limit=s.limit.value:s.limit&&this._groupLimits.set(i,s.limit.value));const o=this._initDensityFactor(s.density),n=s.value,a=s.limit.value>bt?s.limit.value:n,r=Math.min(n,a)*o+e,c=Math.min(this.count,this.filter((t=>t.group===i)).length);void 0===i?this._limit=s.limit.value*o:this._groupLimits.set(i,s.limit.value*o),c<r?this.push(Math.abs(r-c),void 0,t,i):c>r&&this.removeQuantity(c-r,i)},this._initDensityFactor=t=>{const e=this._container;if(!e.canvas.element||!t.enable)return zt;const i=e.canvas.element,s=e.retina.pixelRatio;return i.width*i.height/(t.height*t.width*s**T)},this._pushParticle=(t,e,i,s)=>{try{let o=this._pool.pop();o||(o=new ho(this._engine,this._container)),o.init(this._nextId,t,e,i);let n=!0;if(s&&(n=s(o)),!n)return;return this._array.push(o),this._zArray.push(o),this._nextId++,this._engine.dispatchEvent(pi.particleAdded,{container:this._container,data:{particle:o}}),o}catch(t){Ee().warning(`${f} adding particle: ${t}`)}},this._removeParticle=(t,e,i)=>{const s=this._array[t];if(!s||s.group!==e)return!1;const o=this._zArray.indexOf(s);return this._array.splice(t,Ct),this._zArray.splice(o,Ct),s.destroy(i),this._engine.dispatchEvent(pi.particleRemoved,{container:this._container,data:{particle:s}}),this._addToPool(s),!0},this._engine=t,this._container=e,this._nextId=0,this._array=[],this._zArray=[],this._pool=[],this._limit=0,this._groupLimits=new Map,this._needsSort=!1,this._lastZIndex=0,this._interactionManager=new co(t,e),this._pluginsInitialized=!1;const i=e.canvas.size;this.quadTree=new vo(mo(i),R),this.movers=[],this.updaters=[]}get count(){return this._array.length}addManualParticles(){const t=this._container;t.actualOptions.manualParticles.forEach((e=>this.addParticle(e.position?ai(e.position,t.canvas.size):void 0,e.options)))}addParticle(t,e,i,s){const o=this._container.actualOptions.particles.number.limit.mode,n=void 0===i?this._limit:this._groupLimits.get(i)??this._limit,a=this.count;if(n>bt)switch(o){case bi.delete:{const t=a+wt-n;t>xt&&this.removeQuantity(t);break}case bi.wait:if(a>=n)return}return this._pushParticle(t,e,i,s)}clear(){this._array=[],this._zArray=[],this._pluginsInitialized=!1}destroy(){this._array=[],this._zArray=[],this.movers=[],this.updaters=[]}draw(t){const e=this._container,i=e.canvas;i.clear(),this.update(t);for(const s of e.plugins.values())i.drawPlugin(s,t);for(const e of this._zArray)e.draw(t)}filter(t){return this._array.filter(t)}find(t){return this._array.find(t)}get(t){return this._array[t]}handleClickMode(t){this._interactionManager.handleClickMode(t)}async init(){const t=this._container,e=t.actualOptions;this._lastZIndex=0,this._needsSort=!1,await this.initPlugins();let i=!1;for(const e of t.plugins.values())if(i=e.particlesInitialization?.()??i,i)break;if(this.addManualParticles(),!i){const t=e.particles,i=t.groups;for(const e in i){const s=i[e];for(let i=this.count,o=0;o<s.number?.value&&i<t.number.value;i++,o++)this.addParticle(void 0,s,e)}for(let e=this.count;e<t.number.value;e++)this.addParticle()}}async initPlugins(){if(this._pluginsInitialized)return;const t=this._container;this.movers=await this._engine.getMovers(t,!0),this.updaters=await this._engine.getUpdaters(t,!0),await this._interactionManager.init();for(const e of t.pathGenerators.values())e.init(t)}push(t,e,i,s){for(let o=0;o<t;o++)this.addParticle(e?.position,i,s)}async redraw(){this.clear(),await this.init(),this.draw({value:0,factor:0})}remove(t,e,i){this.removeAt(this._array.indexOf(t),void 0,e,i)}removeAt(t,e=D,i,s){if(t<_t||t>this.count)return;let o=0;for(let n=t;o<e&&n<this.count;n++)this._removeParticle(n,i,s)&&(n--,o++)}removeQuantity(t,e){this.removeAt(_t,t,e)}setDensity(){const t=this._container.actualOptions,e=t.particles.groups;for(const t in e)this._applyDensity(e[t],kt,t);this._applyDensity(t.particles,t.manualParticles.length)}setLastZIndex(t){this._lastZIndex=t,this._needsSort=this._needsSort||this._lastZIndex<t}setResizeFactor(t){this._resizeFactor=t}update(t){const e=this._container,i=new Set;this.quadTree=new vo(mo(e.canvas.size),R);for(const t of e.pathGenerators.values())t.update();for(const i of e.plugins.values())i.update?.(t);const s=this._resizeFactor;for(const e of this._array){s&&!e.ignoresResizeRatio&&(e.position.x*=s.width,e.position.y*=s.height,e.initialPosition.x*=s.width,e.initialPosition.y*=s.height),e.ignoresResizeRatio=!1,this._interactionManager.reset(e);for(const i of this._container.plugins.values()){if(e.destroyed)break;i.particleUpdate?.(e,t)}for(const i of this.movers)i.isEnabled(e)&&i.move(e,t);e.destroyed?i.add(e):this.quadTree.insert(new uo(e.getPosition(),e))}if(i.size){const t=t=>!i.has(t);this._array=this.filter(t),this._zArray=this._zArray.filter(t);for(const t of i)this._engine.dispatchEvent(pi.particleRemoved,{container:this._container,data:{particle:t}});this._addToPool(...i)}this._interactionManager.externalInteract(t);for(const e of this._array){for(const i of this.updaters)i.update(e,t);e.destroyed||e.spawning||this._interactionManager.particlesInteract(e,t)}if(delete this._resizeFactor,this._needsSort){const t=this._zArray;t.sort(((t,e)=>e.position.z-t.position.z||t.id-e.id)),this._lastZIndex=t[t.length-Mt].position.z,this._needsSort=!1}}}class bo{constructor(t){this.container=t,this.pixelRatio=E,this.reduceFactor=I}init(){const t=this.container,e=t.actualOptions;this.pixelRatio=!e.detectRetina||Le()?E:window.devicePixelRatio,this.reduceFactor=I;const i=this.pixelRatio,s=t.canvas;if(s.element){const t=s.element;s.size.width=t.offsetWidth*i,s.size.height=t.offsetHeight*i}const o=e.particles,n=o.move;this.maxSpeed=le(n.gravity.maxSpeed)*i,this.sizeAnimationSpeed=le(o.size.animation.speed)*i}initParticle(t){const e=t.options,i=this.pixelRatio,s=e.move,o=s.distance,n=t.retina;n.moveDrift=le(s.drift)*i,n.moveSpeed=le(s.speed)*i,n.sizeAnimationSpeed=le(e.size.animation.speed)*i;const a=n.maxDistance;a.horizontal=void 0!==o.horizontal?o.horizontal*i:void 0,a.vertical=void 0!==o.vertical?o.vertical*i:void 0,n.maxSpeed=le(s.gravity.maxSpeed)*i}}function wo(t){return t&&!t.destroyed}function xo(t,e,...i){const s=new ro(t,e);return no(s,...i),s}class _o{constructor(t,e,i){this._intersectionManager=t=>{if(wo(this)&&this.actualOptions.pauseOnOutsideViewport)for(const e of t)e.target===this.interactivity.element&&(e.isIntersecting?this.play():this.pause())},this._nextFrame=t=>{try{if(!this._smooth&&void 0!==this._lastFrameTime&&t<this._lastFrameTime+m/this.fpsLimit)return void this.draw(!1);this._lastFrameTime??=t;const e=function(t,e=M,i=!1){return{value:t,factor:i?M/e:M*t/m}}(t-this._lastFrameTime,this.fpsLimit,this._smooth);if(this.addLifeTime(e.value),this._lastFrameTime=t,e.value>m)return void this.draw(!1);if(this.particles.draw(e),!this.alive())return void this.destroy();this.animationStatus&&this.draw(!1)}catch(t){Ee().error(`${f} in animation loop`,t)}},this._engine=t,this.id=Symbol(e),this.fpsLimit=120,this._smooth=!1,this._delay=0,this._duration=0,this._lifeTime=0,this._firstStart=!0,this.started=!1,this.destroyed=!1,this._paused=!0,this._lastFrameTime=0,this.zLayers=100,this.pageHidden=!1,this._clickHandlers=new Map,this._sourceOptions=i,this._initialSourceOptions=i,this.retina=new bo(this),this.canvas=new ss(this,this._engine),this.particles=new yo(this._engine,this),this.pathGenerators=new Map,this.interactivity={mouse:{clicking:!1,inside:!1}},this.plugins=new Map,this.effectDrawers=new Map,this.shapeDrawers=new Map,this._options=xo(this._engine,this),this.actualOptions=xo(this._engine,this),this._eventListeners=new ns(this),this._intersectionObserver=Be((t=>this._intersectionManager(t))),this._engine.dispatchEvent(pi.containerBuilt,{container:this})}get animationStatus(){return!this._paused&&!this.pageHidden&&wo(this)}get options(){return this._options}get sourceOptions(){return this._sourceOptions}addClickHandler(t){if(!wo(this))return;const e=this.interactivity.element;if(!e)return;const i=(e,i,s)=>{if(!wo(this))return;const o=this.retina.pixelRatio,n={x:i.x*o,y:i.y*o},a=this.particles.quadTree.queryCircle(n,s*o);t(e,a)};let s=!1,o=!1;this._clickHandlers.set("click",(t=>{if(!wo(this))return;const e=t,s={x:e.offsetX||e.clientX,y:e.offsetY||e.clientY};i(t,s,J)})),this._clickHandlers.set("touchstart",(()=>{wo(this)&&(s=!0,o=!1)})),this._clickHandlers.set("touchmove",(()=>{wo(this)&&(o=!0)})),this._clickHandlers.set("touchend",(t=>{if(wo(this)){if(s&&!o){const e=t;let s=e.touches[e.touches.length-tt];if(!s&&(s=e.changedTouches[e.changedTouches.length-tt],!s))return;const o=this.canvas.element,n=o?o.getBoundingClientRect():void 0,a={x:s.clientX-(n?n.left:et),y:s.clientY-(n?n.top:et)};i(t,a,Math.max(s.radiusX,s.radiusY))}s=!1,o=!1}})),this._clickHandlers.set("touchcancel",(()=>{wo(this)&&(s=!1,o=!1)}));for(const[t,i]of this._clickHandlers)e.addEventListener(t,i)}addLifeTime(t){this._lifeTime+=t}addPath(t,e,i=!1){return!(!wo(this)||!i&&this.pathGenerators.has(t))&&(this.pathGenerators.set(t,e),!0)}alive(){return!this._duration||this._lifeTime<=this._duration}clearClickHandlers(){if(wo(this)){for(const[t,e]of this._clickHandlers)this.interactivity.element?.removeEventListener(t,e);this._clickHandlers.clear()}}destroy(t=!0){if(wo(this)){this.stop(),this.clearClickHandlers(),this.particles.destroy(),this.canvas.destroy();for(const t of this.effectDrawers.values())t.destroy?.(this);for(const t of this.shapeDrawers.values())t.destroy?.(this);for(const t of this.effectDrawers.keys())this.effectDrawers.delete(t);for(const t of this.shapeDrawers.keys())this.shapeDrawers.delete(t);if(this._engine.clearPlugins(this),this.destroyed=!0,t){const t=this._engine.items,e=t.findIndex((t=>t===this));e>=st&&t.splice(e,it)}this._engine.dispatchEvent(pi.containerDestroyed,{container:this})}}draw(t){if(!wo(this))return;let e=t;const i=t=>{e&&(this._lastFrameTime=void 0,e=!1),this._nextFrame(t)};this._drawAnimationFrame=oe((t=>i(t)))}async export(t,e={}){for(const i of this.plugins.values()){if(!i.export)continue;const s=await i.export(t,e);if(s.supported)return s.blob}Ee().error(`${f} - Export plugin with type ${t} not found`)}handleClickMode(t){if(wo(this)){this.particles.handleClickMode(t);for(const e of this.plugins.values())e.handleClickMode?.(t)}}async init(){if(!wo(this))return;const t=this._engine.getSupportedEffects();for(const e of t){const t=this._engine.getEffectDrawer(e);t&&this.effectDrawers.set(e,t)}const e=this._engine.getSupportedShapes();for(const t of e){const e=this._engine.getShapeDrawer(t);e&&this.shapeDrawers.set(t,e)}await this.particles.initPlugins(),this._options=xo(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=xo(this._engine,this,this._options);const i=await this._engine.getAvailablePlugins(this);for(const[t,e]of i)this.plugins.set(t,e);this.retina.init(),await this.canvas.init(),this.updateActualOptions(),this.canvas.initBackground(),this.canvas.resize();const{zLayers:s,duration:o,delay:n,fpsLimit:a,smooth:r}=this.actualOptions;this.zLayers=s,this._duration=le(o)*m,this._delay=le(n)*m,this._lifeTime=0,this.fpsLimit=a>nt?a:ot,this._smooth=r;for(const t of this.effectDrawers.values())await(t.init?.(this));for(const t of this.shapeDrawers.values())await(t.init?.(this));for(const t of this.plugins.values())await(t.init?.());this._engine.dispatchEvent(pi.containerInit,{container:this}),await this.particles.init(),this.particles.setDensity();for(const t of this.plugins.values())t.particlesSetup?.();this._engine.dispatchEvent(pi.particlesSetup,{container:this})}async loadTheme(t){wo(this)&&(this._currentTheme=t,await this.refresh())}pause(){if(wo(this)&&(void 0!==this._drawAnimationFrame&&(ne(this._drawAnimationFrame),delete this._drawAnimationFrame),!this._paused)){for(const t of this.plugins.values())t.pause?.();this.pageHidden||(this._paused=!0),this._engine.dispatchEvent(pi.containerPaused,{container:this})}}play(t){if(!wo(this))return;const e=this._paused||t;if(!this._firstStart||this.actualOptions.autoPlay){if(this._paused&&(this._paused=!1),e)for(const t of this.plugins.values())t.play&&t.play();this._engine.dispatchEvent(pi.containerPlay,{container:this}),this.draw(e??!1)}else this._firstStart=!1}async refresh(){if(wo(this))return this.stop(),this.start()}async reset(t){if(wo(this))return this._initialSourceOptions=t,this._sourceOptions=t,this._options=xo(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=xo(this._engine,this,this._options),this.refresh()}async start(){wo(this)&&!this.started&&(await this.init(),this.started=!0,await new Promise((t=>{const e=async()=>{this._eventListeners.addListeners(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.observe(this.interactivity.element);for(const t of this.plugins.values())await(t.start?.());this._engine.dispatchEvent(pi.containerStarted,{container:this}),this.play(),t()};this._delayTimeout=setTimeout((()=>{e()}),this._delay)})))}stop(){if(wo(this)&&this.started){this._delayTimeout&&(clearTimeout(this._delayTimeout),delete this._delayTimeout),this._firstStart=!0,this.started=!1,this._eventListeners.removeListeners(),this.pause(),this.particles.clear(),this.canvas.stop(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.unobserve(this.interactivity.element);for(const t of this.plugins.values())t.stop?.();for(const t of this.plugins.keys())this.plugins.delete(t);this._sourceOptions=this._options,this._engine.dispatchEvent(pi.containerStopped,{container:this})}}updateActualOptions(){this.actualOptions.responsive=[];const t=this.actualOptions.setResponsive(this.canvas.size.width,this.retina.pixelRatio,this._options);return this.actualOptions.setTheme(this._currentTheme),this._responsiveMaxWidth!==t&&(this._responsiveMaxWidth=t,!0)}}class ko{constructor(){this._listeners=new Map}addEventListener(t,e){this.removeEventListener(t,e);let i=this._listeners.get(t);i||(i=[],this._listeners.set(t,i)),i.push(e)}dispatchEvent(t,e){const i=this._listeners.get(t);i?.forEach((t=>t(e)))}hasEventListener(t){return!!this._listeners.get(t)}removeAllEventListeners(t){t?this._listeners.delete(t):this._listeners=new Map}removeEventListener(t,e){const i=this._listeners.get(t);if(!i)return;const s=i.length,o=i.indexOf(e);o<_t||(s===Ct?this._listeners.delete(t):i.splice(o,Ct))}}async function Mo(t,e,i,s=!1){let o=e.get(t);return o&&!s||(o=await Promise.all([...i.values()].map((e=>e(t)))),e.set(t,o)),o}class zo{constructor(){this._configs=new Map,this._domArray=[],this._eventDispatcher=new ko,this._initialized=!1,this.plugins=[],this.colorManagers=new Map,this.easingFunctions=new Map,this._initializers={interactors:new Map,movers:new Map,updaters:new Map},this.interactors=new Map,this.movers=new Map,this.updaters=new Map,this.presets=new Map,this.effectDrawers=new Map,this.shapeDrawers=new Map,this.pathGenerators=new Map}get configs(){const t={};for(const[e,i]of this._configs)t[e]=i;return t}get items(){return this._domArray}get version(){return"3.8.1"}async addColorManager(t,e=!0){this.colorManagers.set(t.key,t),await this.refresh(e)}addConfig(t){const e=t.key??t.name??"default";this._configs.set(e,t),this._eventDispatcher.dispatchEvent(pi.configAdded,{data:{name:e,config:t}})}async addEasing(t,e,i=!0){this.getEasing(t)||(this.easingFunctions.set(t,e),await this.refresh(i))}async addEffect(t,e,i=!0){ei(t,(t=>{this.getEffectDrawer(t)||this.effectDrawers.set(t,e)})),await this.refresh(i)}addEventListener(t,e){this._eventDispatcher.addEventListener(t,e)}async addInteractor(t,e,i=!0){this._initializers.interactors.set(t,e),await this.refresh(i)}async addMover(t,e,i=!0){this._initializers.movers.set(t,e),await this.refresh(i)}async addParticleUpdater(t,e,i=!0){this._initializers.updaters.set(t,e),await this.refresh(i)}async addPathGenerator(t,e,i=!0){this.getPathGenerator(t)||this.pathGenerators.set(t,e),await this.refresh(i)}async addPlugin(t,e=!0){this.getPlugin(t.id)||this.plugins.push(t),await this.refresh(e)}async addPreset(t,e,i=!1,s=!0){!i&&this.getPreset(t)||this.presets.set(t,e),await this.refresh(s)}async addShape(t,e=!0){for(const e of t.validTypes)this.getShapeDrawer(e)||this.shapeDrawers.set(e,t);await this.refresh(e)}checkVersion(t){if(this.version!==t)throw new Error(`The tsParticles version is different from the loaded plugins version. Engine version: ${this.version}. Plugin version: ${t}`)}clearPlugins(t){this.updaters.delete(t),this.movers.delete(t),this.interactors.delete(t)}dispatchEvent(t,e){this._eventDispatcher.dispatchEvent(t,e)}dom(){return this.items}domItem(t){return this.item(t)}async getAvailablePlugins(t){const e=new Map;for(const i of this.plugins)i.needsPlugin(t.actualOptions)&&e.set(i.id,await i.getPlugin(t));return e}getEasing(t){return this.easingFunctions.get(t)??(t=>t)}getEffectDrawer(t){return this.effectDrawers.get(t)}async getInteractors(t,e=!1){return Mo(t,this.interactors,this._initializers.interactors,e)}async getMovers(t,e=!1){return Mo(t,this.movers,this._initializers.movers,e)}getPathGenerator(t){return this.pathGenerators.get(t)}getPlugin(t){return this.plugins.find((e=>e.id===t))}getPreset(t){return this.presets.get(t)}getShapeDrawer(t){return this.shapeDrawers.get(t)}getSupportedEffects(){return this.effectDrawers.keys()}getSupportedShapes(){return this.shapeDrawers.keys()}async getUpdaters(t,e=!1){return Mo(t,this.updaters,this._initializers.updaters,e)}init(){this._initialized||(this._initialized=!0)}item(t){const{items:e}=this,i=e[t];if(i&&!i.destroyed)return i;e.splice(t,it)}async load(t){const e=t.id??t.element?.id??`tsparticles${Math.floor(ie()*rt)}`,{index:s,url:o}=t,n=o?await async function(t){const e=ii(t.url,t.index);if(!e)return t.fallback;const i=await fetch(e);return i.ok?await i.json():(Ee().error(`${f} ${i.status} while retrieving config file`),t.fallback)}({fallback:t.options,url:o,index:s}):t.options,a=ii(n,s),{items:r}=this,c=r.findIndex((t=>t.id.description===e)),l=new _o(this,e,a);if(c>=ct){const t=this.item(c),e=t?lt:ht;t&&!t.destroyed&&t.destroy(!1),r.splice(c,e,l)}else r.push(l);const h=((t,e)=>{let s=e??document.getElementById(t);return s||(s=document.createElement("div"),s.id=t,s.dataset[i]=C,document.body.append(s),s)})(e,t.element),d=(t=>{let e;if(t instanceof HTMLCanvasElement||t.tagName.toLowerCase()===O)e=t,e.dataset[i]||(e.dataset[i]=P);else{const s=t.getElementsByTagName(O);s.length?(e=s[at],e.dataset[i]=P):(e=document.createElement(O),e.dataset[i]=C,t.appendChild(e))}const s="100%";return e.style.width||(e.style.width=s),e.style.height||(e.style.height=s),e})(h);return l.canvas.loadCanvas(d),await l.start(),l}loadOptions(t,e){this.plugins.forEach((i=>i.loadOptions?.(t,e)))}loadParticlesOptions(t,e,...i){const s=this.updaters.get(t);s&&s.forEach((t=>t.loadOptions?.(e,...i)))}async refresh(t=!0){t&&await Promise.all(this.items.map((t=>t.refresh())))}removeEventListener(t,e){this._eventDispatcher.removeEventListener(t,e)}setOnClickHandler(t){const{items:e}=this;if(!e.length)throw new Error(`${f} can only set click handlers after calling tsParticles.load()`);e.forEach((e=>e.addClickHandler(t)))}}class Co{constructor(t){this.type=wi.external,this.container=t}}class Po{constructor(t){this.type=wi.particles,this.container=t}}var Oo,So,To;!function(t){t.clockwise="clockwise",t.counterClockwise="counter-clockwise",t.random="random"}(Oo||(Oo={})),function(t){t.linear="linear",t.radial="radial",t.random="random"}(So||(So={})),function(t){t.easeInBack="ease-in-back",t.easeInCirc="ease-in-circ",t.easeInCubic="ease-in-cubic",t.easeInLinear="ease-in-linear",t.easeInQuad="ease-in-quad",t.easeInQuart="ease-in-quart",t.easeInQuint="ease-in-quint",t.easeInExpo="ease-in-expo",t.easeInSine="ease-in-sine",t.easeOutBack="ease-out-back",t.easeOutCirc="ease-out-circ",t.easeOutCubic="ease-out-cubic",t.easeOutLinear="ease-out-linear",t.easeOutQuad="ease-out-quad",t.easeOutQuart="ease-out-quart",t.easeOutQuint="ease-out-quint",t.easeOutExpo="ease-out-expo",t.easeOutSine="ease-out-sine",t.easeInOutBack="ease-in-out-back",t.easeInOutCirc="ease-in-out-circ",t.easeInOutCubic="ease-in-out-cubic",t.easeInOutLinear="ease-in-out-linear",t.easeInOutQuad="ease-in-out-quad",t.easeInOutQuart="ease-in-out-quart",t.easeInOutQuint="ease-in-out-quint",t.easeInOutExpo="ease-in-out-expo",t.easeInOutSine="ease-in-out-sine"}(To||(To={}));const Ro=function(){const t=new zo;return t.init(),t}();Le()||(window.tsParticles=Ro);const Do=1,Eo=2*Math.PI;function Io(t,e,i,s,o,n){!function(t,e){const i=t.options,s=i.move.path;if(!s.enable)return;if(t.lastPathTime<=t.pathDelay)return void(t.lastPathTime+=e.value);const o=t.pathGenerator?.generate(t,e);o&&t.velocity.addTo(o);s.clamp&&(t.velocity.x=ae(t.velocity.x,-Do,Do),t.velocity.y=ae(t.velocity.y,-Do,Do));t.lastPathTime-=t.pathDelay}(t,n);const a=t.gravity,r=a?.enable&&a.inverse?-Do:Do;o&&i&&(t.velocity.x+=o*n.factor/(60*i)),a?.enable&&i&&(t.velocity.y+=r*(a.acceleration*n.factor)/(60*i));const c=t.moveDecay;t.velocity.multTo(c);const l=t.velocity.mult(i);a?.enable&&s>0&&(!a.inverse&&l.y>=0&&l.y>=s||a.inverse&&l.y<=0&&l.y<=-s)&&(l.y=r*s,i&&(t.velocity.y=l.y/i));const h=t.options.zIndex,d=(Do-t.zIndexFactor)**h.velocityRate;l.multTo(d);const{position:u}=t;u.addTo(l),e.vibrate&&(u.x+=Math.sin(u.x*Math.cos(u.y)),u.y+=Math.cos(u.y*Math.sin(u.x)))}class Lo{init(t){const e=t.options.move.gravity;t.gravity={enable:e.enable,acceleration:le(e.acceleration),inverse:e.inverse},function(t){const e=t.container,i=t.options.move.spin;if(!i.enable)return;const s=i.position??{x:50,y:50},o={x:.01*s.x*e.canvas.size.width,y:.01*s.y*e.canvas.size.height},n=fe(t.getPosition(),o),a=le(i.acceleration);t.retina.spinAcceleration=a*e.retina.pixelRatio,t.spin={center:o,direction:t.velocity.x>=0?Oo.clockwise:Oo.counterClockwise,angle:ie()*Eo,radius:n,acceleration:t.retina.spinAcceleration}}(t)}isEnabled(t){return!t.destroyed&&t.options.move.enable}move(t,e){const i=t.options,s=i.move;if(!s.enable)return;const o=t.container,n=o.retina.pixelRatio;t.retina.moveSpeed??=le(s.speed)*n,t.retina.moveDrift??=le(t.options.move.drift)*n;const a=function(t){return t.slow.inRange?t.slow.factor:Do}(t),r=t.retina.moveSpeed*o.retina.reduceFactor,c=t.retina.moveDrift,l=de(i.size.value)*n,h=r*(s.size?t.getRadius()/l:1)*a*(e.factor||1)/2,d=t.retina.maxSpeed??o.retina.maxSpeed;s.spin.enable?function(t,e){const i=t.container;if(!t.spin)return;const s=t.spin.direction===Oo.clockwise,o={x:s?Math.cos:Math.sin,y:s?Math.sin:Math.cos};t.position.x=t.spin.center.x+t.spin.radius*o.x(t.spin.angle),t.position.y=t.spin.center.y+t.spin.radius*o.y(t.spin.angle),t.spin.radius+=t.spin.acceleration;const n=Math.max(i.canvas.size.width,i.canvas.size.height),a=.5*n;t.spin.radius>a?(t.spin.radius=a,t.spin.acceleration*=-Do):t.spin.radius<0&&(t.spin.radius=0,t.spin.acceleration*=-Do),t.spin.angle+=.01*e*(Do-t.spin.radius/n)}(t,h):Io(t,s,h,d,c,e),function(t){const e=t.initialPosition,{dx:i,dy:s}=pe(e,t.position),o=Math.abs(i),n=Math.abs(s),{maxDistance:a}=t.retina,r=a.horizontal,c=a.vertical;if(!r&&!c)return;if((r&&o>=r||c&&n>=c)&&!t.misplaced)t.misplaced=!!r&&o>r||!!c&&n>c,r&&(t.velocity.x=.5*t.velocity.y-t.velocity.x),c&&(t.velocity.y=.5*t.velocity.x-t.velocity.y);else if((!r||o<r)&&(!c||n<c)&&t.misplaced)t.misplaced=!1;else if(t.misplaced){const i=t.position,s=t.velocity;r&&(i.x<e.x&&s.x<0||i.x>e.x&&s.x>0)&&(s.x*=-ie()),c&&(i.y<e.y&&s.y<0||i.y>e.y&&s.y>0)&&(s.y*=-ie())}}(t)}}const Fo=2*Math.PI,Ao=0,Bo=0;class qo{constructor(){this.validTypes=["circle"]}draw(t){!function(t){const{context:e,particle:i,radius:s}=t;i.circleRange||(i.circleRange={min:0,max:Fo});const o=i.circleRange;e.arc(Ao,Bo,s,o.min,o.max,!1)}(t)}getSidesCount(){return 12}particleInit(t,e){const i=e.shapeData,s=i?.angle??{max:360,min:0};e.circleRange=Qt(s)?{min:ge(s.min),max:ge(s.max)}:{min:0,max:ge(s)}}}class Vo{constructor(t,e){this._container=t,this._engine=e}init(t){const e=Pi(this._engine,t.options.color,t.id,t.options.reduceDuplicates);e&&(t.color=Vi(e,t.options.color.animation,this._container.retina.reduceFactor))}isEnabled(t){const{h:e,s:i,l:s}=t.options.color.animation,{color:o}=t;return!t.destroyed&&!t.spawning&&(void 0!==o?.h.value&&e.enable||void 0!==o?.s.value&&i.enable||void 0!==o?.l.value&&s.enable)}update(t,e){Wi(t.color,e)}}var Ho;!function(t){t[t.r=1]="r",t[t.g=2]="g",t[t.b=3]="b",t[t.a=4]="a"}(Ho||(Ho={}));const Uo=/^#?([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,Wo=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i;class jo{constructor(){this.key="hex",this.stringPrefix="#"}handleColor(t){return this._parseString(t.value)}handleRangeColor(t){return this._parseString(t.value)}parseString(t){return this._parseString(t)}_parseString(t){if("string"!=typeof t)return;if(!t?.startsWith(this.stringPrefix))return;const e=t.replace(Uo,((t,e,i,s,o)=>e+e+i+i+s+s+(void 0!==o?o+o:""))),i=Wo.exec(e);return i?{a:void 0!==i[Ho.a]?parseInt(i[Ho.a],16)/255:1,b:parseInt(i[Ho.b],16),g:parseInt(i[Ho.g],16),r:parseInt(i[Ho.r],16)}:void 0}}var $o;!function(t){t[t.h=1]="h",t[t.s=2]="s",t[t.l=3]="l",t[t.a=5]="a"}($o||($o={}));class Go{constructor(){this.key="hsl",this.stringPrefix="hsl"}handleColor(t){const e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.s&&void 0!==e.l)return Ri(e)}handleRangeColor(t){const e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.l)return Ri({h:le(e.h),l:le(e.l),s:le(e.s)})}parseString(t){if(!t.startsWith("hsl"))return;const e=/hsla?\(\s*(\d+)\s*[\s,]\s*(\d+)%\s*[\s,]\s*(\d+)%\s*([\s,]\s*(0|1|0?\.\d+|(\d{1,3})%)\s*)?\)/i.exec(t);return e?Di({a:e.length>4?Me(e[$o.a]):1,h:parseInt(e[$o.h],10),l:parseInt(e[$o.l],10),s:parseInt(e[$o.s],10)}):void 0}}class No{constructor(t){this.container=t}init(t){const e=t.options.opacity;t.opacity=oi(e,1);const i=e.animation;i.enable&&(t.opacity.velocity=le(i.speed)/g*this.container.retina.reduceFactor,i.sync||(t.opacity.velocity*=ie()))}isEnabled(t){return!t.destroyed&&!t.spawning&&!!t.opacity&&t.opacity.enable&&((t.opacity.maxLoops??0)<=0||(t.opacity.maxLoops??0)>0&&(t.opacity.loops??0)<(t.opacity.maxLoops??0))}reset(t){t.opacity&&(t.opacity.time=0,t.opacity.loops=0)}update(t,e){this.isEnabled(t)&&t.opacity&&ci(t,t.opacity,!0,t.options.opacity.animation.destroy,e)}}class Qo{constructor(t){this.container=t,this.modes=[yi.bounce,yi.split]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;let n=!1;for(const s of o.plugins.values())if(void 0!==s.particleBounce&&(n=s.particleBounce(t,i,e)),n)break;if(n)return;const a=t.getPosition(),r=t.offset,c=t.getRadius(),l=Ge(a,c),h=o.canvas.size;!function(t){if(t.outMode!==yi.bounce&&t.outMode!==yi.split||t.direction!==Oe.left&&t.direction!==Oe.right)return;t.bounds.right<0&&t.direction===Oe.left?t.particle.position.x=t.size+t.offset.x:t.bounds.left>t.canvasSize.width&&t.direction===Oe.right&&(t.particle.position.x=t.canvasSize.width-t.size-t.offset.x);const e=t.particle.velocity.x;let i=!1;if(t.direction===Oe.right&&t.bounds.right>=t.canvasSize.width&&e>0||t.direction===Oe.left&&t.bounds.left<=0&&e<0){const e=le(t.particle.options.bounce.horizontal.value);t.particle.velocity.x*=-e,i=!0}if(!i)return;const s=t.offset.x+t.size;t.bounds.right>=t.canvasSize.width&&t.direction===Oe.right?t.particle.position.x=t.canvasSize.width-s:t.bounds.left<=0&&t.direction===Oe.left&&(t.particle.position.x=s),t.outMode===yi.split&&t.particle.destroy()}({particle:t,outMode:s,direction:e,bounds:l,canvasSize:h,offset:r,size:c}),function(t){if(t.outMode!==yi.bounce&&t.outMode!==yi.split||t.direction!==Oe.bottom&&t.direction!==Oe.top)return;t.bounds.bottom<0&&t.direction===Oe.top?t.particle.position.y=t.size+t.offset.y:t.bounds.top>t.canvasSize.height&&t.direction===Oe.bottom&&(t.particle.position.y=t.canvasSize.height-t.size-t.offset.y);const e=t.particle.velocity.y;let i=!1;if(t.direction===Oe.bottom&&t.bounds.bottom>=t.canvasSize.height&&e>0||t.direction===Oe.top&&t.bounds.top<=0&&e<0){const e=le(t.particle.options.bounce.vertical.value);t.particle.velocity.y*=-e,i=!0}if(!i)return;const s=t.offset.y+t.size;t.bounds.bottom>=t.canvasSize.height&&t.direction===Oe.bottom?t.particle.position.y=t.canvasSize.height-s:t.bounds.top<=0&&t.direction===Oe.top&&(t.particle.position.y=s),t.outMode===yi.split&&t.particle.destroy()}({particle:t,outMode:s,direction:e,bounds:l,canvasSize:h,offset:r,size:c})}}class Xo{constructor(t){this.container=t,this.modes=[yi.destroy]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;switch(t.outType){case xi.normal:case xi.outside:if(je(t.position,o.canvas.size,Kt.origin,t.getRadius(),e))return;break;case xi.inside:{const{dx:e,dy:i}=pe(t.position,t.moveCenter),{x:s,y:o}=t.velocity;if(s<0&&e>t.moveCenter.radius||o<0&&i>t.moveCenter.radius||s>=0&&e<-t.moveCenter.radius||o>=0&&i<-t.moveCenter.radius)return;break}}o.particles.remove(t,t.group,!0)}}class Yo{constructor(t){this.container=t,this.modes=[yi.none]}update(t,e,i,s){if(!this.modes.includes(s))return;if((t.options.move.distance.horizontal&&(e===Oe.left||e===Oe.right))??(t.options.move.distance.vertical&&(e===Oe.top||e===Oe.bottom)))return;const o=t.options.move.gravity,n=this.container,a=n.canvas.size,r=t.getRadius();if(o.enable){const i=t.position;(!o.inverse&&i.y>a.height+r&&e===Oe.bottom||o.inverse&&i.y<-r&&e===Oe.top)&&n.particles.remove(t)}else{if(t.velocity.y>0&&t.position.y<=a.height+r||t.velocity.y<0&&t.position.y>=-r||t.velocity.x>0&&t.position.x<=a.width+r||t.velocity.x<0&&t.position.x>=-r)return;je(t.position,n.canvas.size,Kt.origin,r,e)||n.particles.remove(t)}}}class Zo{constructor(t){this.container=t,this.modes=[yi.out]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;switch(t.outType){case xi.inside:{const{x:e,y:i}=t.velocity,s=Kt.origin;s.length=t.moveCenter.radius,s.angle=t.velocity.angle+Math.PI,s.addTo(Kt.create(t.moveCenter));const{dx:n,dy:a}=pe(t.position,s);if(e<=0&&n>=0||i<=0&&a>=0||e>=0&&n<=0||i>=0&&a<=0)return;t.position.x=Math.floor(ce({min:0,max:o.canvas.size.width})),t.position.y=Math.floor(ce({min:0,max:o.canvas.size.height}));const{dx:r,dy:c}=pe(t.position,t.moveCenter);t.direction=Math.atan2(-c,-r),t.velocity.angle=t.direction;break}default:if(je(t.position,o.canvas.size,Kt.origin,t.getRadius(),e))return;switch(t.outType){case xi.outside:{t.position.x=Math.floor(ce({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.x,t.position.y=Math.floor(ce({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.y;const{dx:e,dy:i}=pe(t.position,t.moveCenter);t.moveCenter.radius&&(t.direction=Math.atan2(i,e),t.velocity.angle=t.direction);break}case xi.normal:{const i=t.options.move.warp,s=o.canvas.size,n={bottom:s.height+t.getRadius()+t.offset.y,left:-t.getRadius()-t.offset.x,right:s.width+t.getRadius()+t.offset.x,top:-t.getRadius()-t.offset.y},a=t.getRadius(),r=Ge(t.position,a);e===Oe.right&&r.left>s.width+t.offset.x?(t.position.x=n.left,t.initialPosition.x=t.position.x,i||(t.position.y=ie()*s.height,t.initialPosition.y=t.position.y)):e===Oe.left&&r.right<-t.offset.x&&(t.position.x=n.right,t.initialPosition.x=t.position.x,i||(t.position.y=ie()*s.height,t.initialPosition.y=t.position.y)),e===Oe.bottom&&r.top>s.height+t.offset.y?(i||(t.position.x=ie()*s.width,t.initialPosition.x=t.position.x),t.position.y=n.top,t.initialPosition.y=t.position.y):e===Oe.top&&r.bottom<-t.offset.y&&(i||(t.position.x=ie()*s.width,t.initialPosition.x=t.position.x),t.position.y=n.bottom,t.initialPosition.y=t.position.y);break}}}}}class Ko{constructor(t){this._addUpdaterIfMissing=(t,e,i)=>{const s=t.options.move.outModes;!this.updaters.has(e)&&((t,e)=>t.default===e||t.bottom===e||t.left===e||t.right===e||t.top===e)(s,e)&&this.updaters.set(e,i(this.container))},this._updateOutMode=(t,e,i,s)=>{for(const o of this.updaters.values())o.update(t,s,e,i)},this.container=t,this.updaters=new Map}init(t){this._addUpdaterIfMissing(t,yi.bounce,(t=>new Qo(t))),this._addUpdaterIfMissing(t,yi.out,(t=>new Zo(t))),this._addUpdaterIfMissing(t,yi.destroy,(t=>new Xo(t))),this._addUpdaterIfMissing(t,yi.none,(t=>new Yo(t)))}isEnabled(t){return!t.destroyed&&!t.spawning}update(t,e){const i=t.options.move.outModes;this._updateOutMode(t,e,i.bottom??i.default,Oe.bottom),this._updateOutMode(t,e,i.left??i.default,Oe.left),this._updateOutMode(t,e,i.right??i.default,Oe.right),this._updateOutMode(t,e,i.top??i.default,Oe.top)}}var Jo;!function(t){t[t.r=1]="r",t[t.g=2]="g",t[t.b=3]="b",t[t.a=5]="a"}(Jo||(Jo={}));class tn{constructor(){this.key="rgb",this.stringPrefix="rgb"}handleColor(t){const e=t.value.rgb??t.value;if(void 0!==e.r)return e}handleRangeColor(t){const e=t.value.rgb??t.value;if(void 0!==e.r)return{r:le(e.r),g:le(e.g),b:le(e.b)}}parseString(t){if(!t.startsWith(this.stringPrefix))return;const e=/rgba?\(\s*(\d{1,3})\s*[\s,]\s*(\d{1,3})\s*[\s,]\s*(\d{1,3})\s*([\s,]\s*(0|1|0?\.\d+|(\d{1,3})%)\s*)?\)/i.exec(t);return e?{a:e.length>4?Me(e[Jo.a]):1,b:parseInt(e[Jo.b],10),g:parseInt(e[Jo.g],10),r:parseInt(e[Jo.r],10)}:void 0}}class en{init(t){const e=t.container,i=t.options.size.animation;i.enable&&(t.size.velocity=(t.retina.sizeAnimationSpeed??e.retina.sizeAnimationSpeed)/g*e.retina.reduceFactor,i.sync||(t.size.velocity*=ie()))}isEnabled(t){return!t.destroyed&&!t.spawning&&t.size.enable&&((t.size.maxLoops??0)<=0||(t.size.maxLoops??0)>0&&(t.size.loops??0)<(t.size.maxLoops??0))}reset(t){t.size.loops=0}update(t,e){this.isEnabled(t)&&ci(t,t.size,!0,t.options.size.animation.destroy,e)}}async function sn(t,e=!0){t.checkVersion("3.8.1"),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new jo,e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new Go,e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addColorManager(new tn,e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addMover("base",(()=>Promise.resolve(new Lo)),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new qo,e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("color",(e=>Promise.resolve(new Vo(e,t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("opacity",(t=>Promise.resolve(new No(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("outModes",(t=>Promise.resolve(new Ko(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("size",(()=>Promise.resolve(new en)),e)}(t,!1),await t.refresh(e)}const on='"Twemoji Mozilla", Apple Color Emoji, "Segoe UI Emoji", "Noto Color Emoji", "EmojiOne Color"';class nn{constructor(){this.validTypes=["emoji"],this._emojiShapeDict=new Map}destroy(){for(const[t,e]of this._emojiShapeDict)e instanceof ImageBitmap&&e?.close(),this._emojiShapeDict.delete(t)}draw(t){const e=t.particle.emojiDataKey;if(!e)return;const i=this._emojiShapeDict.get(e);i&&function(t,e){const{context:i,opacity:s}=t,o=i.globalAlpha;if(!e)return;const n=e.width,a=.5*n;i.globalAlpha=s,i.drawImage(e,-a,-a,n,n),i.globalAlpha=o}(t,i)}async init(t){const e=t.actualOptions,{validTypes:i}=this;if(!i.find((t=>Ve(t,e.particles.shape.type))))return;const s=[He(on)],o=i.map((t=>e.particles.shape.options[t])).find((t=>!!t));o&&ei(o,(t=>{t.font&&s.push(He(t.font))})),await Promise.all(s)}particleDestroy(t){t.emojiDataKey=void 0}particleInit(t,e){const i=e.shapeData;if(!i?.value)return;const s=ii(i.value,e.randomIndexData);if(!s)return;const o="string"==typeof s?{font:i.font??on,padding:i.padding??0,value:s}:{font:on,padding:0,...i,...s},n=o.font,a=o.value,r=`${a}_${n}`;if(this._emojiShapeDict.has(r))return void(e.emojiDataKey=r);const c=2*o.padding,l=de(e.size.value),h=l+c,d=2*h;let u;if("undefined"!=typeof OffscreenCanvas){const t=new OffscreenCanvas(d,d),e=t.getContext("2d");if(!e)return;e.font=`400 ${2*l}px ${n}`,e.textBaseline="middle",e.textAlign="center",e.fillText(a,h,h),u=t.transferToImageBitmap()}else{const t=document.createElement("canvas");t.width=d,t.height=d;const e=t.getContext("2d");if(!e)return;e.font=`400 ${2*l}px ${n}`,e.textBaseline="middle",e.textAlign="center",e.fillText(a,h,h),u=t}this._emojiShapeDict.set(r,u),e.emojiDataKey=r}}const an=1,rn=1;function cn(t,e,i,s,o,n){const a=e.actualOptions.interactivity.modes.attract;if(!a)return;const r=e.particles.quadTree.query(o,n);for(const e of r){const{dx:o,dy:n,distance:r}=pe(e.position,i),c=a.speed*a.factor,l=ae(t.getEasing(a.easing)(rn-r/s)*c,an,a.maxSpeed),h=Kt.create(r?o/r*l:c,r?n/r*l:c);e.position.subFrom(h)}}class ln{constructor(){this.distance=200,this.duration=.4,this.easing=To.easeOutQuad,this.factor=1,this.maxSpeed=50,this.speed=1}load(t){Yt(t)||(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.maxSpeed&&(this.maxSpeed=t.maxSpeed),void 0!==t.speed&&(this.speed=t.speed))}}const hn="attract";class dn extends Co{constructor(t,e){super(e),this._engine=t,e.attract||(e.attract={particles:[]}),this.handleClickMode=t=>{const i=this.container.actualOptions.interactivity.modes.attract;if(i&&t===hn){e.attract||(e.attract={particles:[]}),e.attract.clicking=!0,e.attract.count=0;for(const t of e.attract.particles)this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);e.attract.particles=[],e.attract.finish=!1,setTimeout((()=>{e.destroyed||(e.attract||(e.attract={particles:[]}),e.attract.clicking=!1)}),i.duration*m)}}}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.attract;e&&(t.retina.attractModeDistance=e.distance*t.retina.pixelRatio)}interact(){const t=this.container,e=t.actualOptions,i=t.interactivity.status===r,s=e.interactivity.events,{enable:o,mode:n}=s.onHover,{enable:a,mode:c}=s.onClick;i&&o&&Ve(hn,n)?function(t,e,i){const s=e.interactivity.mouse.position,o=e.retina.attractModeDistance;!o||o<0||!s||cn(t,e,s,o,new fo(s.x,s.y,o),(t=>i(t)))}(this._engine,this.container,(t=>this.isEnabled(t))):a&&Ve(hn,c)&&function(t,e,i){e.attract||(e.attract={particles:[]});const{attract:s}=e;if(s.finish||(s.count||(s.count=0),s.count++,s.count===e.particles.count&&(s.finish=!0)),s.clicking){const s=e.interactivity.mouse.clickPosition,o=e.retina.attractModeDistance;if(!o||o<0||!s)return;cn(t,e,s,o,new fo(s.x,s.y,o),(t=>i(t)))}else!1===s.clicking&&(s.particles=[])}(this._engine,this.container,(t=>this.isEnabled(t)))}isEnabled(t){const e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events;if(!(s.position&&o.onHover.enable||s.clickPosition&&o.onClick.enable))return!1;const n=o.onHover.mode,a=o.onClick.mode;return Ve(hn,n)||Ve(hn,a)}loadModeOptions(t,...e){t.attract||(t.attract=new ln);for(const i of e)t.attract.load(i?.attract)}reset(){}}const un=2,pn=.5,fn=Math.PI*pn,gn=2,vn=10;function mn(t,e,i,s,o){const n=t.particles.quadTree.query(s,o);for(const t of n)s instanceof fo?Je(Ke(t),{position:e,radius:i,mass:i**un*fn,velocity:Kt.origin,factor:Kt.origin}):s instanceof go&&ti(t,Ge(e,i))}function yn(t,e,i,s){Xe(i,e,((e,i)=>function(t,e,i,s){const o=document.querySelectorAll(e);o.length&&o.forEach((e=>{const o=e,n=t.retina.pixelRatio,a={x:(o.offsetLeft+o.offsetWidth*pn)*n,y:(o.offsetTop+o.offsetHeight*pn)*n},r=o.offsetWidth*pn*n,c=vn*n,l=i.type===fi.circle?new fo(a.x,a.y,r+c):new go(o.offsetLeft*n-c,o.offsetTop*n-c,o.offsetWidth*n+c*gn,o.offsetHeight*n+c*gn);s(a,r,l)}))}(t,e,i,((e,i,o)=>mn(t,e,i,o,s)))))}class bn{constructor(){this.distance=200}load(t){Yt(t)||void 0!==t.distance&&(this.distance=t.distance)}}const wn="bounce";class xn extends Co{constructor(t){super(t)}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.bounce;e&&(t.retina.bounceModeDistance=e.distance*t.retina.pixelRatio)}interact(){const t=this.container,e=t.actualOptions.interactivity.events,i=t.interactivity.status===r,s=e.onHover.enable,o=e.onHover.mode,n=e.onDiv;i&&s&&Ve(wn,o)?function(t,e){const i=t.retina.pixelRatio,s=vn*i,o=t.interactivity.mouse.position,n=t.retina.bounceModeDistance;!n||n<0||!o||mn(t,o,n,new fo(o.x,o.y,n+s),e)}(this.container,(t=>this.isEnabled(t))):yn(this.container,n,wn,(t=>this.isEnabled(t)))}isEnabled(t){const e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events,n=o.onDiv;return!!s.position&&o.onHover.enable&&Ve(wn,o.onHover.mode)||Qe(wn,n)}loadModeOptions(t,...e){t.bounce||(t.bounce=new bn);for(const i of e)t.bounce.load(i?.bounce)}reset(){}}class _n{constructor(){this.distance=200,this.duration=.4,this.mix=!1}load(t){if(!Yt(t)){if(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.mix&&(this.mix=t.mix),void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.color){const e=Xt(this.color)?void 0:this.color;this.color=ei(t.color,(t=>as.create(e,t)))}void 0!==t.size&&(this.size=t.size)}}}class kn extends _n{constructor(){super(),this.selectors=[]}load(t){super.load(t),Yt(t)||void 0!==t.selectors&&(this.selectors=t.selectors)}}class Mn extends _n{load(t){super.load(t),Yt(t)||(this.divs=ei(t.divs,(t=>{const e=new kn;return e.load(t),e})))}}var zn;function Cn(t,e,i,s){if(e>=i){return ae(t+(e-i)*s,t,e)}if(e<i){return ae(t-(i-e)*s,e,t)}}!function(t){t.color="color",t.opacity="opacity",t.size="size"}(zn||(zn={}));const Pn="bubble";class On extends Co{constructor(t,e){super(t),this._clickBubble=()=>{const t=this.container,e=t.actualOptions,i=t.interactivity.mouse.clickPosition,s=e.interactivity.modes.bubble;if(!s||!i)return;t.bubble||(t.bubble={});const o=t.retina.bubbleModeDistance;if(!o||o<0)return;const n=t.particles.quadTree.queryCircle(i,o,(t=>this.isEnabled(t))),{bubble:a}=t;for(const e of n){if(!a.clicking)continue;e.bubble.inRange=!a.durationEnd;const n=fe(e.getPosition(),i),r=((new Date).getTime()-(t.interactivity.mouse.clickTime??0))/m;r>s.duration&&(a.durationEnd=!0),r>2*s.duration&&(a.clicking=!1,a.durationEnd=!1);const c={bubbleObj:{optValue:t.retina.bubbleModeSize,value:e.bubble.radius},particlesObj:{optValue:de(e.options.size.value)*t.retina.pixelRatio,value:e.size.value},type:zn.size};this._process(e,n,r,c);const l={bubbleObj:{optValue:s.opacity,value:e.bubble.opacity},particlesObj:{optValue:de(e.options.opacity.value),value:e.opacity?.value??1},type:zn.opacity};this._process(e,n,r,l),!a.durationEnd&&n<=o?this._hoverBubbleColor(e,n):delete e.bubble.color}},this._hoverBubble=()=>{const t=this.container,e=t.interactivity.mouse.position,i=t.retina.bubbleModeDistance;if(!i||i<0||!e)return;const s=t.particles.quadTree.queryCircle(e,i,(t=>this.isEnabled(t)));for(const o of s){o.bubble.inRange=!0;const s=fe(o.getPosition(),e),a=1-s/i;s<=i?a>=0&&t.interactivity.status===r&&(this._hoverBubbleSize(o,a),this._hoverBubbleOpacity(o,a),this._hoverBubbleColor(o,a)):this.reset(o),t.interactivity.status===n&&this.reset(o)}},this._hoverBubbleColor=(t,e,i)=>{const s=this.container.actualOptions,o=i??s.interactivity.modes.bubble;if(o){if(!t.bubble.finalColor){const e=o.color;if(!e)return;const i=ii(e);t.bubble.finalColor=Pi(this._engine,i)}if(t.bubble.finalColor)if(o.mix){t.bubble.color=void 0;const i=t.getFillColor();t.bubble.color=i?Oi(Fi(i,t.bubble.finalColor,1-e,e)):t.bubble.finalColor}else t.bubble.color=t.bubble.finalColor}},this._hoverBubbleOpacity=(t,e,i)=>{const s=this.container.actualOptions,o=i?.opacity??s.interactivity.modes.bubble?.opacity;if(!o)return;const n=t.options.opacity.value,a=Cn(t.opacity?.value??1,o,de(n),e);void 0!==a&&(t.bubble.opacity=a)},this._hoverBubbleSize=(t,e,i)=>{const s=this.container,o=i?.size?i.size*s.retina.pixelRatio:s.retina.bubbleModeSize;if(void 0===o)return;const n=de(t.options.size.value)*s.retina.pixelRatio,a=Cn(t.size.value,o,n,e);void 0!==a&&(t.bubble.radius=a)},this._process=(t,e,i,s)=>{const o=this.container,n=s.bubbleObj.optValue,a=o.actualOptions.interactivity.modes.bubble;if(!a||void 0===n)return;const r=a.duration,c=o.retina.bubbleModeDistance,l=s.particlesObj.optValue,h=s.bubbleObj.value,d=s.particlesObj.value??0,u=s.type;if(c&&!(c<0)&&n!==l)if(o.bubble||(o.bubble={}),o.bubble.durationEnd)h&&(u===zn.size&&delete t.bubble.radius,u===zn.opacity&&delete t.bubble.opacity);else if(e<=c){if((h??d)!==n){const e=d-i*(d-n)/r;u===zn.size&&(t.bubble.radius=e),u===zn.opacity&&(t.bubble.opacity=e)}}else u===zn.size&&delete t.bubble.radius,u===zn.opacity&&delete t.bubble.opacity},this._singleSelectorHover=(t,e,i)=>{const s=this.container,o=document.querySelectorAll(e),n=s.actualOptions.interactivity.modes.bubble;n&&o.length&&o.forEach((e=>{const o=e,a=s.retina.pixelRatio,r={x:(o.offsetLeft+.5*o.offsetWidth)*a,y:(o.offsetTop+.5*o.offsetHeight)*a},c=.5*o.offsetWidth*a,l=i.type===fi.circle?new fo(r.x,r.y,c):new go(o.offsetLeft*a,o.offsetTop*a,o.offsetWidth*a,o.offsetHeight*a),h=s.particles.quadTree.query(l,(t=>this.isEnabled(t)));for(const e of h){if(!l.contains(e.getPosition()))continue;e.bubble.inRange=!0;const i=Ze(n.divs,o);e.bubble.div&&e.bubble.div===o||(this.clear(e,t,!0),e.bubble.div=o),this._hoverBubbleSize(e,1,i),this._hoverBubbleOpacity(e,1,i),this._hoverBubbleColor(e,1,i)}}))},this._engine=e,t.bubble||(t.bubble={}),this.handleClickMode=e=>{e===Pn&&(t.bubble||(t.bubble={}),t.bubble.clicking=!0)}}clear(t,e,i){t.bubble.inRange&&!i||(delete t.bubble.div,delete t.bubble.opacity,delete t.bubble.radius,delete t.bubble.color)}init(){const t=this.container,e=t.actualOptions.interactivity.modes.bubble;e&&(t.retina.bubbleModeDistance=e.distance*t.retina.pixelRatio,void 0!==e.size&&(t.retina.bubbleModeSize=e.size*t.retina.pixelRatio))}interact(t){const e=this.container.actualOptions.interactivity.events,i=e.onHover,s=e.onClick,o=i.enable,n=i.mode,a=s.enable,r=s.mode,c=e.onDiv;o&&Ve(Pn,n)?this._hoverBubble():a&&Ve(Pn,r)?this._clickBubble():Xe(Pn,c,((e,i)=>this._singleSelectorHover(t,e,i)))}isEnabled(t){const e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events,{onClick:n,onDiv:a,onHover:r}=o,c=Qe(Pn,a);return!!(c||r.enable&&s.position||n.enable&&s.clickPosition)&&(Ve(Pn,r.mode)||Ve(Pn,n.mode)||c)}loadModeOptions(t,...e){t.bubble||(t.bubble=new Mn);for(const i of e)t.bubble.load(i?.bubble)}reset(t){t.bubble.inRange=!1}}class Sn{constructor(){this.opacity=.5}load(t){Yt(t)||void 0!==t.opacity&&(this.opacity=t.opacity)}}class Tn{constructor(){this.distance=80,this.links=new Sn,this.radius=60}load(t){Yt(t)||(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links),void 0!==t.radius&&(this.radius=t.radius))}}const Rn=0,Dn=1;function En(t,e,i,s){const o=t.actualOptions.interactivity.modes.connect;if(o)return function(t,e,i,s){const o=Math.floor(i.getRadius()/e.getRadius()),n=e.getFillColor(),a=i.getFillColor();if(!n||!a)return;const r=e.getPosition(),c=i.getPosition(),l=Fi(n,a,e.getRadius(),i.getRadius()),h=t.createLinearGradient(r.x,r.y,c.x,c.y);return h.addColorStop(Rn,Li(n,s)),h.addColorStop(ae(o,Rn,Dn),Ii(l,s)),h.addColorStop(Dn,Li(a,s)),h}(e,i,s,o.links.opacity)}function In(t,e,i){t.canvas.draw((s=>{const o=En(t,s,e,i);if(!o)return;const n=e.getPosition(),a=i.getPosition();!function(t,e,i,s,o){ji(t,s,o),t.lineWidth=e,t.strokeStyle=i,t.stroke()}(s,e.retina.linksWidth??0,o,n,a)}))}class Ln extends Co{constructor(t){super(t)}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.connect;e&&(t.retina.connectModeDistance=e.distance*t.retina.pixelRatio,t.retina.connectModeRadius=e.radius*t.retina.pixelRatio)}interact(){const t=this.container;if(t.actualOptions.interactivity.events.onHover.enable&&"pointermove"===t.interactivity.status){const e=t.interactivity.mouse.position,{connectModeDistance:i,connectModeRadius:s}=t.retina;if(!i||i<0||!s||s<0||!e)return;const o=Math.abs(s),n=t.particles.quadTree.queryCircle(e,o,(t=>this.isEnabled(t)));n.forEach(((e,s)=>{const o=e.getPosition();for(const a of n.slice(s+1)){const s=a.getPosition(),n=Math.abs(i),r=Math.abs(o.x-s.x),c=Math.abs(o.y-s.y);r<n&&c<n&&In(t,e,a)}}))}}isEnabled(t){const e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return!(!s.onHover.enable||!i.position)&&Ve("connect",s.onHover.mode)}loadModeOptions(t,...e){t.connect||(t.connect=new Tn);for(const i of e)t.connect.load(i?.connect)}reset(){}}class Fn{constructor(){this.blink=!1,this.consent=!1,this.opacity=1}load(t){Yt(t)||(void 0!==t.blink&&(this.blink=t.blink),void 0!==t.color&&(this.color=as.create(this.color,t.color)),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.opacity&&(this.opacity=t.opacity))}}class An{constructor(){this.distance=100,this.links=new Fn}load(t){Yt(t)||(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links))}}function Bn(t,e,i,s,o){t.canvas.draw((t=>{const n=e.getPosition();!function(t,e,i,s,o,n){ji(t,i,s),t.strokeStyle=Ii(o,n),t.lineWidth=e,t.stroke()}(t,e.retina.linksWidth??0,n,o,i,s)}))}class qn extends Co{constructor(t,e){super(t),this._engine=e}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.grab;e&&(t.retina.grabModeDistance=e.distance*t.retina.pixelRatio)}interact(){const t=this.container,e=t.actualOptions.interactivity;if(!e.modes.grab||!e.events.onHover.enable||t.interactivity.status!==r)return;const i=t.interactivity.mouse.position;if(!i)return;const s=t.retina.grabModeDistance;if(!s||s<0)return;const o=t.particles.quadTree.queryCircle(i,s,(t=>this.isEnabled(t)));for(const n of o){const o=fe(n.getPosition(),i);if(o>s)continue;const a=e.modes.grab.links,r=a.opacity,c=r-o*r/s;if(c<=0)continue;const l=a.color??n.options.links?.color;if(!t.particles.grabLineColor&&l){const i=e.modes.grab.links;t.particles.grabLineColor=Bi(this._engine,l,i.blink,i.consent)}const h=Ai(n,void 0,t.particles.grabLineColor);h&&Bn(t,n,h,c,i)}}isEnabled(t){const e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return s.onHover.enable&&!!i.position&&Ve("grab",s.onHover.mode)}loadModeOptions(t,...e){t.grab||(t.grab=new An);for(const i of e)t.grab.load(i?.grab)}reset(){}}class Vn extends Co{constructor(t){super(t),this.handleClickMode=t=>{if("pause"!==t)return;const e=this.container;e.animationStatus?e.pause():e.play()}}clear(){}init(){}interact(){}isEnabled(){return!0}reset(){}}class Hn{constructor(){this.default=!0,this.groups=[],this.quantity=4}load(t){if(Yt(t))return;void 0!==t.default&&(this.default=t.default),void 0!==t.groups&&(this.groups=t.groups.map((t=>t))),this.groups.length||(this.default=!0);const e=t.quantity;void 0!==e&&(this.quantity=ue(e))}}class Un extends Co{constructor(t){super(t),this.handleClickMode=t=>{if("push"!==t)return;const e=this.container,i=e.actualOptions.interactivity.modes.push;if(!i)return;const s=le(i.quantity);if(s<=0)return;const o=We([void 0,...i.groups]),n=void 0!==o?e.actualOptions.particles.groups[o]:void 0;e.particles.push(s,e.interactivity.mouse,n,o)}}clear(){}init(){}interact(){}isEnabled(){return!0}loadModeOptions(t,...e){t.push||(t.push=new Hn);for(const i of e)t.push.load(i?.push)}reset(){}}class Wn{constructor(){this.quantity=2}load(t){if(Yt(t))return;const e=t.quantity;void 0!==e&&(this.quantity=ue(e))}}class jn extends Co{constructor(t){super(t),this.handleClickMode=t=>{const e=this.container,i=e.actualOptions;if(!i.interactivity.modes.remove||"remove"!==t)return;const s=le(i.interactivity.modes.remove.quantity);e.particles.removeQuantity(s)}}clear(){}init(){}interact(){}isEnabled(){return!0}loadModeOptions(t,...e){t.remove||(t.remove=new Wn);for(const i of e)t.remove.load(i?.remove)}reset(){}}class $n{constructor(){this.distance=200,this.duration=.4,this.factor=100,this.speed=1,this.maxSpeed=50,this.easing=To.easeOutQuad}load(t){Yt(t)||(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.speed&&(this.speed=t.speed),void 0!==t.maxSpeed&&(this.maxSpeed=t.maxSpeed))}}class Gn extends $n{constructor(){super(),this.selectors=[]}load(t){super.load(t),Yt(t)||void 0!==t.selectors&&(this.selectors=t.selectors)}}class Nn extends $n{load(t){super.load(t),Yt(t)||(this.divs=ei(t.divs,(t=>{const e=new Gn;return e.load(t),e})))}}const Qn="repulse";class Xn extends Co{constructor(t,e){super(e),this._clickRepulse=()=>{const t=this.container,e=t.actualOptions.interactivity.modes.repulse;if(!e)return;const i=t.repulse??{particles:[]};if(i.finish||(i.count||(i.count=0),i.count++,i.count===t.particles.count&&(i.finish=!0)),i.clicking){const s=t.retina.repulseModeDistance;if(!s||s<0)return;const o=Math.pow(s/6,3),n=t.interactivity.mouse.clickPosition;if(void 0===n)return;const a=new fo(n.x,n.y,o),r=t.particles.quadTree.query(a,(t=>this.isEnabled(t)));for(const t of r){const{dx:s,dy:a,distance:r}=pe(n,t.position),c=r**2,l=-o*e.speed/c;if(c<=o){i.particles.push(t);const e=Kt.create(s,a);e.length=l,t.velocity.setTo(e)}}}else if(!1===i.clicking){for(const t of i.particles)t.velocity.setTo(t.initialVelocity);i.particles=[]}},this._hoverRepulse=()=>{const t=this.container,e=t.interactivity.mouse.position,i=t.retina.repulseModeDistance;!i||i<0||!e||this._processRepulse(e,i,new fo(e.x,e.y,i))},this._processRepulse=(t,e,i,s)=>{const o=this.container,n=o.particles.quadTree.query(i,(t=>this.isEnabled(t))),a=o.actualOptions.interactivity.modes.repulse;if(!a)return;const{easing:r,speed:c,factor:l,maxSpeed:h}=a,d=this._engine.getEasing(r),u=(s?.speed??c)*l;for(const i of n){const{dx:s,dy:o,distance:n}=pe(i.position,t),a=ae(d(1-n/e)*u,0,h),r=Kt.create(n?s/n*a:u,n?o/n*a:u);i.position.addTo(r)}},this._singleSelectorRepulse=(t,e)=>{const i=this.container,s=i.actualOptions.interactivity.modes.repulse;if(!s)return;const o=document.querySelectorAll(t);o.length&&o.forEach((t=>{const o=t,n=i.retina.pixelRatio,a={x:(o.offsetLeft+.5*o.offsetWidth)*n,y:(o.offsetTop+.5*o.offsetHeight)*n},r=.5*o.offsetWidth*n,c=e.type===fi.circle?new fo(a.x,a.y,r):new go(o.offsetLeft*n,o.offsetTop*n,o.offsetWidth*n,o.offsetHeight*n),l=Ze(s.divs,o);this._processRepulse(a,r,c,l)}))},this._engine=t,e.repulse||(e.repulse={particles:[]}),this.handleClickMode=t=>{const i=this.container.actualOptions.interactivity.modes.repulse;if(!i||t!==Qn)return;e.repulse||(e.repulse={particles:[]});const s=e.repulse;s.clicking=!0,s.count=0;for(const t of e.repulse.particles)this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);s.particles=[],s.finish=!1,setTimeout((()=>{e.destroyed||(s.clicking=!1)}),i.duration*m)}}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.repulse;e&&(t.retina.repulseModeDistance=e.distance*t.retina.pixelRatio)}interact(){const t=this.container,e=t.actualOptions,i=t.interactivity.status===r,s=e.interactivity.events,o=s.onHover,n=o.enable,a=o.mode,c=s.onClick,l=c.enable,h=c.mode,d=s.onDiv;i&&n&&Ve(Qn,a)?this._hoverRepulse():l&&Ve(Qn,h)?this._clickRepulse():Xe(Qn,d,((t,e)=>this._singleSelectorRepulse(t,e)))}isEnabled(t){const e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events,n=o.onDiv,a=o.onHover,r=o.onClick,c=Qe(Qn,n);if(!(c||a.enable&&s.position||r.enable&&s.clickPosition))return!1;const l=a.mode,h=r.mode;return Ve(Qn,l)||Ve(Qn,h)||c}loadModeOptions(t,...e){t.repulse||(t.repulse=new Nn);for(const i of e)t.repulse.load(i?.repulse)}reset(){}}class Yn{constructor(){this.factor=3,this.radius=200}load(t){Yt(t)||(void 0!==t.factor&&(this.factor=t.factor),void 0!==t.radius&&(this.radius=t.radius))}}class Zn extends Co{constructor(t){super(t)}clear(t,e,i){t.slow.inRange&&!i||(t.slow.factor=1)}init(){const t=this.container,e=t.actualOptions.interactivity.modes.slow;e&&(t.retina.slowModeRadius=e.radius*t.retina.pixelRatio)}interact(){}isEnabled(t){const e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return s.onHover.enable&&!!i.position&&Ve("slow",s.onHover.mode)}loadModeOptions(t,...e){t.slow||(t.slow=new Yn);for(const i of e)t.slow.load(i?.slow)}reset(t){t.slow.inRange=!1;const e=this.container,i=e.actualOptions,s=e.interactivity.mouse.position,o=e.retina.slowModeRadius,n=i.interactivity.modes.slow;if(!n||!o||o<0||!s)return;const a=fe(s,t.getPosition()),r=a/o,c=n.factor,{slow:l}=t;a>o||(l.inRange=!0,l.factor=r/c)}}const Kn=0,Jn=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d.]+%?\))|currentcolor/gi;async function ta(t){return new Promise((e=>{t.loading=!0;const i=new Image;t.element=i,i.addEventListener("load",(()=>{t.loading=!1,e()})),i.addEventListener("error",(()=>{t.element=void 0,t.error=!0,t.loading=!1,Ee().error(`${f} loading image: ${t.source}`),e()})),i.src=t.source}))}async function ea(t){if("svg"!==t.type)return void await ta(t);t.loading=!0;const e=await fetch(t.source);e.ok?t.svgData=await e.text():(Ee().error(`${f} Image not found`),t.error=!0),t.loading=!1}function ia(t,e,i,s){const o=function(t,e,i){const{svgData:s}=t;if(!s)return"";const o=Li(e,i);if(s.includes("fill"))return s.replace(Jn,(()=>o));const n=s.indexOf(">");return`${s.substring(Kn,n)} fill="${o}"${s.substring(n)}`}(t,i,s.opacity?.value??1),n={color:i,gif:e.gif,data:{...t,svgData:o},loaded:!1,ratio:e.width/e.height,replaceColor:e.replaceColor,source:e.src};return new Promise((e=>{const i=new Blob([o],{type:"image/svg+xml"}),s=URL||window.URL||window.webkitURL||window,a=s.createObjectURL(i),r=new Image;r.addEventListener("load",(()=>{n.loaded=!0,n.element=r,e(n),s.revokeObjectURL(a)}));r.addEventListener("error",(()=>{(async()=>{s.revokeObjectURL(a);const i={...t,error:!1,loading:!0};await ta(i),n.loaded=!0,n.element=i.element,e(n)})()})),r.src=a}))}const sa=[0,4,2,1],oa=[8,8,4,2];class na{constructor(t){this.pos=0,this.data=new Uint8ClampedArray(t)}getString(t){const e=this.data.slice(this.pos,this.pos+t);return this.pos+=e.length,e.reduce(((t,e)=>t+String.fromCharCode(e)),"")}nextByte(){return this.data[this.pos++]}nextTwoBytes(){return this.pos+=2,this.data[this.pos-2]+(this.data[this.pos-1]<<8)}readSubBlocks(){let t="",e=0;do{e=this.data[this.pos++];for(let i=e;--i>=0;t+=String.fromCharCode(this.data[this.pos++]));}while(0!==e);return t}readSubBlocksBin(){let t=this.data[this.pos],e=0;for(let i=0;0!==t;i+=t+1,t=this.data[this.pos+i])e+=t;const i=new Uint8Array(e);t=this.data[this.pos++];for(let e=0;0!==t;t=this.data[this.pos++])for(let s=t;--s>=0;i[e++]=this.data[this.pos++]);return i}skipSubBlocks(){for(const t=1,e=0;this.data[this.pos]!==e;this.pos+=this.data[this.pos]+t);this.pos++}}var aa,ra;!function(t){t[t.Replace=0]="Replace",t[t.Combine=1]="Combine",t[t.RestoreBackground=2]="RestoreBackground",t[t.RestorePrevious=3]="RestorePrevious",t[t.UndefinedA=4]="UndefinedA",t[t.UndefinedB=5]="UndefinedB",t[t.UndefinedC=6]="UndefinedC",t[t.UndefinedD=7]="UndefinedD"}(aa||(aa={})),function(t){t[t.Extension=33]="Extension",t[t.ApplicationExtension=255]="ApplicationExtension",t[t.GraphicsControlExtension=249]="GraphicsControlExtension",t[t.PlainTextExtension=1]="PlainTextExtension",t[t.CommentExtension=254]="CommentExtension",t[t.Image=44]="Image",t[t.EndOfFile=59]="EndOfFile"}(ra||(ra={}));const ca=0,la=0,ha=0;function da(t,e){const i=[];for(let s=0;s<e;s++)i.push({r:t.data[t.pos],g:t.data[t.pos+1],b:t.data[t.pos+2]}),t.pos+=3;return i}async function ua(t,e,i,s,o,n){switch(t.nextByte()){case ra.EndOfFile:return!0;case ra.Image:await async function(t,e,i,s,o,n){const a=e.frames[s(!0)];a.left=t.nextTwoBytes(),a.top=t.nextTwoBytes(),a.width=t.nextTwoBytes(),a.height=t.nextTwoBytes();const r=t.nextByte(),c=128==(128&r),l=64==(64&r);a.sortFlag=32==(32&r),a.reserved=(24&r)>>>3;const h=1<<1+(7&r);c&&(a.localColorTable=da(t,h));const d=t=>{const{r:s,g:n,b:r}=(c?a.localColorTable:e.globalColorTable)[t];return t!==o(null)?{r:s,g:n,b:r,a:255}:{r:s,g:n,b:r,a:i?~~((s+n+r)/3):0}},u=(()=>{try{return new ImageData(a.width,a.height,{colorSpace:"srgb"})}catch(t){if(t instanceof DOMException&&"IndexSizeError"===t.name)return null;throw t}})();if(null==u)throw new EvalError("GIF frame size is to large");const p=t.nextByte(),f=t.readSubBlocksBin(),g=1<<p,v=(t,e)=>{const i=t>>>3,s=7&t;return(f[i]+(f[i+1]<<8)+(f[i+2]<<16)&(1<<e)-1<<s)>>>s};if(l){for(let i=0,o=p+1,r=0,c=[[0]],l=0;l<4;l++){if(sa[l]<a.height){let t=0,e=0,s=!1;for(;!s;){const n=i;if(i=v(r,o),r+=o+1,i===g){o=p+1,c.length=g+2;for(let t=0;t<c.length;t++)c[t]=t<g?[t]:[]}else{i>=c.length?c.push(c[n].concat(c[n][0])):n!==g&&c.push(c[n].concat(c[i][0]));for(const s of c[i]){const{r:i,g:o,b:n,a:r}=d(s);u.data.set([i,o,n,r],sa[l]*a.width+oa[l]*e+t%(4*a.width)),t+=4}c.length===1<<o&&o<12&&o++}t===4*a.width*(e+1)&&(e++,sa[l]+oa[l]*e>=a.height&&(s=!0))}}n?.(t.pos/(t.data.length-1),s(!1)+1,u,{x:a.left,y:a.top},{width:e.width,height:e.height})}a.image=u,a.bitmap=await createImageBitmap(u)}else{let i=0,o=p+1,r=0,c=-4,l=!1;const h=[[0]];for(;!l;){const t=i;if(i=v(r,o),r+=o,i===g){o=p+1,h.length=g+2;for(let t=0;t<h.length;t++)h[t]=t<g?[t]:[]}else{if(i===g+1){l=!0;break}i>=h.length?h.push(h[t].concat(h[t][0])):t!==g&&h.push(h[t].concat(h[i][0]));for(const t of h[i]){const{r:e,g:i,b:s,a:o}=d(t);u.data.set([e,i,s,o],c+=4)}h.length>=1<<o&&o<12&&o++}}a.image=u,a.bitmap=await createImageBitmap(u),n?.((t.pos+1)/t.data.length,s(!1)+1,a.image,{x:a.left,y:a.top},{width:e.width,height:e.height})}}(t,e,i,s,o,n);break;case ra.Extension:!function(t,e,i,s){switch(t.nextByte()){case ra.GraphicsControlExtension:{const o=e.frames[i(!1)];t.pos++;const n=t.nextByte();o.GCreserved=(224&n)>>>5,o.disposalMethod=(28&n)>>>2,o.userInputDelayFlag=2==(2&n);const a=1==(1&n);o.delayTime=10*t.nextTwoBytes();const r=t.nextByte();a&&s(r),t.pos++;break}case ra.ApplicationExtension:{t.pos++;const i={identifier:t.getString(8),authenticationCode:t.getString(3),data:t.readSubBlocksBin()};e.applicationExtensions.push(i);break}case ra.CommentExtension:e.comments.push([i(!1),t.readSubBlocks()]);break;case ra.PlainTextExtension:if(0===e.globalColorTable.length)throw new EvalError("plain text extension without global color table");t.pos++,e.frames[i(!1)].plainTextData={left:t.nextTwoBytes(),top:t.nextTwoBytes(),width:t.nextTwoBytes(),height:t.nextTwoBytes(),charSize:{width:t.nextTwoBytes(),height:t.nextTwoBytes()},foregroundColor:t.nextByte(),backgroundColor:t.nextByte(),text:t.readSubBlocks()};break;default:t.skipSubBlocks()}}(t,e,s,o);break;default:throw new EvalError("undefined block found")}return!1}async function pa(t){if("gif"===t.type){t.loading=!0;try{t.gifData=await async function(t,e,i){i||(i=!1);const s=await fetch(t);if(!s.ok&&404===s.status)throw new EvalError("file not found");const o=await s.arrayBuffer(),n={width:0,height:0,totalTime:0,colorRes:0,pixelAspectRatio:0,frames:[],sortFlag:!1,globalColorTable:[],backgroundImage:new ImageData(1,1,{colorSpace:"srgb"}),comments:[],applicationExtensions:[]},a=new na(new Uint8ClampedArray(o));if("GIF89a"!==a.getString(6))throw new Error("not a supported GIF file");n.width=a.nextTwoBytes(),n.height=a.nextTwoBytes();const r=a.nextByte(),c=128==(128&r);n.colorRes=(112&r)>>>4,n.sortFlag=8==(8&r);const l=1<<1+(7&r),h=a.nextByte();n.pixelAspectRatio=a.nextByte(),0!==n.pixelAspectRatio&&(n.pixelAspectRatio=(n.pixelAspectRatio+15)/64),c&&(n.globalColorTable=da(a,l));const d=(()=>{try{return new ImageData(n.width,n.height,{colorSpace:"srgb"})}catch(t){if(t instanceof DOMException&&"IndexSizeError"===t.name)return null;throw t}})();if(null==d)throw new Error("GIF frame size is to large");const{r:u,g:p,b:f}=n.globalColorTable[h];d.data.set(c?[u,p,f,255]:[0,0,0,0]);for(let t=4;t<d.data.length;t*=2)d.data.copyWithin(t,0,t);n.backgroundImage=d;let g=-1,v=!0,m=-1;const y=t=>(t&&(v=!0),g),b=t=>(null!=t&&(m=t),m);try{do{v&&(n.frames.push({left:0,top:0,width:0,height:0,disposalMethod:aa.Replace,image:new ImageData(1,1,{colorSpace:"srgb"}),plainTextData:null,userInputDelayFlag:!1,delayTime:0,sortFlag:!1,localColorTable:[],reserved:0,GCreserved:0}),g++,m=-1,v=!1)}while(!await ua(a,n,i,y,b,e));n.frames.length--;for(const t of n.frames){if(t.userInputDelayFlag&&0===t.delayTime){n.totalTime=1/0;break}n.totalTime+=t.delayTime}return n}catch(t){if(t instanceof EvalError)throw new Error(`error while parsing frame ${g} "${t.message}"`);throw t}}(t.source),t.gifLoopCount=function(t){for(const e of t.applicationExtensions)if(e.identifier+e.authenticationCode==="NETSCAPE2.0")return e.data[1]+(e.data[2]<<8);return NaN}(t.gifData)??ha,t.gifLoopCount||(t.gifLoopCount=1/0)}catch{t.error=!0}t.loading=!1}else await ta(t)}class fa{constructor(t){this.validTypes=["image","images"],this.loadImageShape=async t=>{if(!this._engine.loadImage)throw new Error(`${f} image shape not initialized`);await this._engine.loadImage({gif:t.gif,name:t.name,replaceColor:t.replaceColor??!1,src:t.src})},this._engine=t}addImage(t){this._engine.images||(this._engine.images=[]),this._engine.images.push(t)}draw(t){const{context:e,radius:i,particle:s,opacity:o}=t,n=s.image,a=n?.element;if(n){if(e.globalAlpha=o,n.gif&&n.gifData)!function(t){const{context:e,radius:i,particle:s,delta:o}=t,n=s.image;if(!n?.gifData||!n.gif)return;const a=new OffscreenCanvas(n.gifData.width,n.gifData.height),r=a.getContext("2d");if(!r)throw new Error("could not create offscreen canvas context");r.imageSmoothingQuality="low",r.imageSmoothingEnabled=!1,r.clearRect(ca,la,a.width,a.height),void 0===s.gifLoopCount&&(s.gifLoopCount=n.gifLoopCount??ha);let c=s.gifFrame??0;const l={x:.5*-n.gifData.width,y:.5*-n.gifData.height},h=n.gifData.frames[c];if(void 0===s.gifTime&&(s.gifTime=0),h.bitmap){switch(e.scale(i/n.gifData.width,i/n.gifData.height),h.disposalMethod){case aa.UndefinedA:case aa.UndefinedB:case aa.UndefinedC:case aa.UndefinedD:case aa.Replace:r.drawImage(h.bitmap,h.left,h.top),e.drawImage(a,l.x,l.y),r.clearRect(ca,la,a.width,a.height);break;case aa.Combine:r.drawImage(h.bitmap,h.left,h.top),e.drawImage(a,l.x,l.y);break;case aa.RestoreBackground:r.drawImage(h.bitmap,h.left,h.top),e.drawImage(a,l.x,l.y),r.clearRect(ca,la,a.width,a.height),n.gifData.globalColorTable.length?r.putImageData(n.gifData.backgroundImage,l.x,l.y):r.putImageData(n.gifData.frames[0].image,l.x+h.left,l.y+h.top);break;case aa.RestorePrevious:{const t=r.getImageData(ca,la,a.width,a.height);r.drawImage(h.bitmap,h.left,h.top),e.drawImage(a,l.x,l.y),r.clearRect(ca,la,a.width,a.height),r.putImageData(t,ca,la)}}if(s.gifTime+=o.value,s.gifTime>h.delayTime){if(s.gifTime-=h.delayTime,++c>=n.gifData.frames.length){if(--s.gifLoopCount<=ha)return;c=0,r.clearRect(ca,la,a.width,a.height)}s.gifFrame=c}e.scale(n.gifData.width/i,n.gifData.height/i)}}(t);else if(a){const t=n.ratio,s={x:-i,y:-i},o=2*i;e.drawImage(a,s.x,s.y,o,o/t)}e.globalAlpha=1}}getSidesCount(){return 12}async init(t){const e=t.actualOptions;if(e.preload&&this._engine.loadImage)for(const t of e.preload)await this._engine.loadImage(t)}loadShape(t){if("image"!==t.shape&&"images"!==t.shape)return;this._engine.images||(this._engine.images=[]);const e=t.shapeData;if(!e)return;this._engine.images.find((t=>t.name===e.name||t.source===e.src))||this.loadImageShape(e).then((()=>{this.loadShape(t)}))}particleInit(t,e){if("image"!==e.shape&&"images"!==e.shape)return;this._engine.images||(this._engine.images=[]);const i=this._engine.images,s=e.shapeData;if(!s)return;const o=e.getFillColor(),n=i.find((t=>t.name===s.name||t.source===s.src));if(!n)return;const a=s.replaceColor??n.replaceColor;n.loading?setTimeout((()=>{this.particleInit(t,e)})):(async()=>{let t;t=n.svgData&&o?await ia(n,s,o,e):{color:o,data:n,element:n.element,gif:n.gif,gifData:n.gifData,gifLoopCount:n.gifLoopCount,loaded:!0,ratio:s.width&&s.height?s.width/s.height:n.ratio??1,replaceColor:a,source:s.src},t.ratio||(t.ratio=1);const i={image:t,fill:s.fill??e.shapeFill,close:s.close??e.shapeClose};e.image=i.image,e.shapeFill=i.fill,e.shapeClose=i.close})()}}class ga{constructor(){this.src="",this.gif=!1}load(t){Yt(t)||(void 0!==t.gif&&(this.gif=t.gif),void 0!==t.height&&(this.height=t.height),void 0!==t.name&&(this.name=t.name),void 0!==t.replaceColor&&(this.replaceColor=t.replaceColor),void 0!==t.src&&(this.src=t.src),void 0!==t.width&&(this.width=t.width))}}class va{constructor(t){this.id="imagePreloader",this._engine=t}async getPlugin(){return await Promise.resolve(),{}}loadOptions(t,e){if(!e?.preload)return;t.preload||(t.preload=[]);const i=t.preload;for(const t of e.preload){const e=i.find((e=>e.name===t.name||e.src===t.src));if(e)e.load(t);else{const e=new ga;e.load(t),i.push(e)}}}needsPlugin(){return!0}}const ma=3;async function ya(t,e=!0){t.checkVersion("3.8.1"),function(t){t.loadImage||(t.loadImage=async e=>{if(!e.name&&!e.src)throw new Error(`${f} no image source provided`);if(t.images||(t.images=[]),!t.images.find((t=>t.name===e.name||t.source===e.src)))try{const i={gif:e.gif??!1,name:e.name??e.src,source:e.src,type:e.src.substring(e.src.length-ma),error:!1,loading:!0,replaceColor:e.replaceColor,ratio:e.width&&e.height?e.width/e.height:void 0};let s;t.images.push(i),s=e.gif?pa:e.replaceColor?ea:ta,await s(i)}catch{throw new Error(`${f} ${e.name??e.src} not found`)}})}(t);const i=new va(t);await t.addPlugin(i,e),await t.addShape(new fa(t),e)}class ba extends Ts{constructor(){super(),this.sync=!1}load(t){Yt(t)||(super.load(t),void 0!==t.sync&&(this.sync=t.sync))}}class wa extends Ts{constructor(){super(),this.sync=!1}load(t){Yt(t)||(super.load(t),void 0!==t.sync&&(this.sync=t.sync))}}class xa{constructor(){this.count=0,this.delay=new ba,this.duration=new wa}load(t){Yt(t)||(void 0!==t.count&&(this.count=t.count),this.delay.load(t.delay),this.duration.load(t.duration))}}class _a{constructor(t){this.container=t}init(t){const e=this.container,i=t.options.life;i&&(t.life={delay:e.retina.reduceFactor?le(i.delay.value)*(i.delay.sync?1:ie())/e.retina.reduceFactor*m:0,delayTime:0,duration:e.retina.reduceFactor?le(i.duration.value)*(i.duration.sync?1:ie())/e.retina.reduceFactor*m:0,time:0,count:i.count},t.life.duration<=0&&(t.life.duration=-1),t.life.count<=0&&(t.life.count=-1),t.life&&(t.spawning=t.life.delay>0))}isEnabled(t){return!t.destroyed}loadOptions(t,...e){t.life||(t.life=new xa);for(const i of e)t.life.load(i?.life)}update(t,e){this.isEnabled(t)&&t.life&&function(t,e,i){if(!t.life)return;const s=t.life;let o=!1;if(t.spawning){if(s.delayTime+=e.value,!(s.delayTime>=t.life.delay))return;o=!0,t.spawning=!1,s.delayTime=0,s.time=0}if(-1===s.duration)return;if(t.spawning)return;if(o?s.time=0:s.time+=e.value,s.time<s.duration)return;if(s.time=0,t.life.count>0&&t.life.count--,0===t.life.count)return void t.destroy();const n=ue(0,i.width),a=ue(0,i.width);t.position.x=ce(n),t.position.y=ce(a),t.spawning=!0,s.delayTime=0,s.time=0,t.reset();const r=t.options.life;r&&(s.delay=le(r.delay.value)*m,s.duration=le(r.duration.value)*m)}(t,e,this.container.canvas.size)}}class ka{constructor(){this.validTypes=["line"]}draw(t){!function(t){const{context:e,particle:i,radius:s}=t,o=i.shapeData;e.moveTo(-s,0),e.lineTo(s,0),e.lineCap=o?.cap??"butt"}(t)}getSidesCount(){return 1}}class Ma{init(){}isEnabled(t){return!Le()&&!t.destroyed&&t.container.actualOptions.interactivity.events.onHover.parallax.enable}move(t){const e=t.container,i=e.actualOptions.interactivity.events.onHover.parallax;if(Le()||!i.enable)return;const s=i.force,o=e.interactivity.mouse.position;if(!o)return;const n=e.canvas.size,a=.5*n.width,r=.5*n.height,c=i.smooth,l=t.getRadius()/s,h=(o.x-a)*l,d=(o.y-r)*l,{offset:u}=t;u.x+=(h-u.x)/c,u.y+=(d-u.y)/c}}class za extends Po{constructor(t){super(t)}clear(){}init(){}interact(t){const e=this.container;void 0===t.attractDistance&&(t.attractDistance=le(t.options.move.attract.distance)*e.retina.pixelRatio);const i=t.attractDistance,s=t.getPosition(),o=e.particles.quadTree.queryCircle(s,i);for(const e of o){if(t===e||!e.options.move.attract.enable||e.destroyed||e.spawning)continue;const i=e.getPosition(),{dx:o,dy:n}=pe(s,i),a=t.options.move.attract.rotate,r=o/(1e3*a.x),c=n/(1e3*a.y),l=e.size.value/t.size.value,h=1/l;t.velocity.x-=r*l,t.velocity.y-=c*l,e.velocity.x+=r*h,e.velocity.y+=c*h}}isEnabled(t){return t.options.move.attract.enable}reset(){}}const Ca=.5,Pa=10,Oa=0;function Sa(t,e,i,s,o,n){const a=ae(t.options.collisions.absorb.speed*o.factor/Pa,Oa,s);t.size.value+=a*Ca,i.size.value-=a,s<=n&&(i.size.value=0,i.destroy())}const Ta=t=>{void 0===t.collisionMaxSpeed&&(t.collisionMaxSpeed=le(t.options.collisions.maxSpeed)),t.velocity.length>t.collisionMaxSpeed&&(t.velocity.length=t.collisionMaxSpeed)};function Ra(t,e){Je(Ke(t),Ke(e)),Ta(t),Ta(e)}function Da(t,e,i,s){switch(t.options.collisions.mode){case mi.absorb:!function(t,e,i,s){const o=t.getRadius(),n=e.getRadius();void 0===o&&void 0!==n?t.destroy():void 0!==o&&void 0===n?e.destroy():void 0!==o&&void 0!==n&&(o>=n?Sa(t,0,e,n,i,s):Sa(e,0,t,o,i,s))}(t,e,i,s);break;case mi.bounce:Ra(t,e);break;case mi.destroy:!function(t,e){t.unbreakable||e.unbreakable||Ra(t,e),void 0===t.getRadius()&&void 0!==e.getRadius()?t.destroy():void 0!==t.getRadius()&&void 0===e.getRadius()?e.destroy():void 0!==t.getRadius()&&void 0!==e.getRadius()&&(t.getRadius()>=e.getRadius()?e:t).destroy()}(t,e)}}class Ea extends Po{constructor(t){super(t)}clear(){}init(){}interact(t,e){if(t.destroyed||t.spawning)return;const i=this.container,s=t.getPosition(),o=t.getRadius(),n=i.particles.quadTree.queryCircle(s,2*o);for(const a of n){if(t===a||!a.options.collisions.enable||t.options.collisions.mode!==a.options.collisions.mode||a.destroyed||a.spawning)continue;const n=a.getPosition(),r=a.getRadius();if(Math.abs(Math.round(s.z)-Math.round(n.z))>o+r)continue;fe(s,n)>o+r||Da(t,a,e,i.retina.pixelRatio)}}isEnabled(t){return t.options.collisions.enable}reset(){}}class Ia extends fo{constructor(t,e,i,s){super(t,e,i),this.canvasSize=s,this.canvasSize={...s}}contains(t){const{width:e,height:i}=this.canvasSize,{x:s,y:o}=t;return super.contains(t)||super.contains({x:s-e,y:o})||super.contains({x:s-e,y:o-i})||super.contains({x:s,y:o-i})}intersects(t){if(super.intersects(t))return!0;const e=t,i=t,s={x:t.position.x-this.canvasSize.width,y:t.position.y-this.canvasSize.height};if(void 0!==i.radius){const t=new fo(s.x,s.y,2*i.radius);return super.intersects(t)}if(void 0!==e.size){const t=new go(s.x,s.y,2*e.size.width,2*e.size.height);return super.intersects(t)}return!1}}class La{constructor(){this.blur=5,this.color=new as,this.color.value="#000",this.enable=!1}load(t){Yt(t)||(void 0!==t.blur&&(this.blur=t.blur),this.color=as.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable))}}class Fa{constructor(){this.enable=!1,this.frequency=1}load(t){Yt(t)||(void 0!==t.color&&(this.color=as.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity))}}class Aa{constructor(){this.blink=!1,this.color=new as,this.color.value="#fff",this.consent=!1,this.distance=100,this.enable=!1,this.frequency=1,this.opacity=1,this.shadow=new La,this.triangles=new Fa,this.width=1,this.warp=!1}load(t){Yt(t)||(void 0!==t.id&&(this.id=t.id),void 0!==t.blink&&(this.blink=t.blink),this.color=as.create(this.color,t.color),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.distance&&(this.distance=t.distance),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity),this.shadow.load(t.shadow),this.triangles.load(t.triangles),void 0!==t.width&&(this.width=t.width),void 0!==t.warp&&(this.warp=t.warp))}}const Ba=0,qa=0;function Va(t,e,i,s,o){const{dx:n,dy:a,distance:r}=pe(t,e);if(!o||r<=i)return r;const c={x:Math.abs(n),y:Math.abs(a)},l=Math.min(c.x,s.width-c.x),h=Math.min(c.y,s.height-c.y);return Math.sqrt(l**2+h**2)}class Ha extends Po{constructor(t,e){super(t),this._setColor=t=>{if(!t.options.links)return;const e=this._linkContainer,i=t.options.links;let s=void 0===i.id?e.particles.linksColor:e.particles.linksColors.get(i.id);if(s)return;const o=i.color;s=Bi(this._engine,o,i.blink,i.consent),void 0===i.id?e.particles.linksColor=s:e.particles.linksColors.set(i.id,s)},this._linkContainer=t,this._engine=e}clear(){}init(){this._linkContainer.particles.linksColor=void 0,this._linkContainer.particles.linksColors=new Map}interact(t){if(!t.options.links)return;t.links=[];const e=t.getPosition(),i=this.container,s=i.canvas.size;if(e.x<Ba||e.y<qa||e.x>s.width||e.y>s.height)return;const o=t.options.links,n=o.opacity,a=t.retina.linksDistance??0,r=o.warp;let c;c=r?new Ia(e.x,e.y,a,s):new fo(e.x,e.y,a);const l=i.particles.quadTree.query(c);for(const i of l){const c=i.options.links;if(t===i||!c?.enable||o.id!==c.id||i.spawning||i.destroyed||!i.links||t.links.some((t=>t.destination===i))||i.links.some((e=>e.destination===t)))continue;const l=i.getPosition();if(l.x<Ba||l.y<qa||l.x>s.width||l.y>s.height)continue;const h=Va(e,l,a,s,r&&c.warp);if(h>a)continue;const d=(1-h/a)*n;this._setColor(t),t.links.push({destination:i,opacity:d})}}isEnabled(t){return!!t.options.links?.enable}loadParticlesOptions(t,...e){t.links||(t.links=new Aa);for(const i of e)t.links.load(i?.links)}reset(){}}function Ua(t,e){const i=((s=t.map((t=>t.id))).sort(((t,e)=>t-e)),s.join("_"));var s;let o=e.get(i);return void 0===o&&(o=ie(),e.set(i,o)),o}class Wa{constructor(t,e){this._drawLinkLine=(t,e)=>{const i=t.options.links;if(!i?.enable)return;const s=this._container,o=s.actualOptions,n=e.destination,a=t.getPosition(),r=n.getPosition();let c=e.opacity;s.canvas.draw((e=>{let l;const h=t.options.twinkle?.lines;if(h?.enable){const t=h.frequency,e=Mi(this._engine,h.color);ie()<t&&e&&(l=e,c=le(h.opacity))}if(!l){const e=void 0!==i.id?s.particles.linksColors.get(i.id):s.particles.linksColor;l=Ai(t,n,e)}if(!l)return;const d=t.retina.linksWidth??0,u=t.retina.linksDistance??0,{backgroundMask:p}=o;!function(t){let e=!1;const{begin:i,end:s,engine:o,maxDistance:n,context:a,canvasSize:r,width:c,backgroundMask:l,colorLine:h,opacity:d,links:u}=t;if(fe(i,s)<=n)ji(a,i,s),e=!0;else if(u.warp){let t,o;const c=pe(i,{x:s.x-r.width,y:s.y});if(c.distance<=n){const e=i.y-c.dy/c.dx*i.x;t={x:0,y:e},o={x:r.width,y:e}}else{const e=pe(i,{x:s.x,y:s.y-r.height});if(e.distance<=n){const s=-(i.y-e.dy/e.dx*i.x)/(e.dy/e.dx);t={x:s,y:0},o={x:s,y:r.height}}else{const e=pe(i,{x:s.x-r.width,y:s.y-r.height});if(e.distance<=n){const s=i.y-e.dy/e.dx*i.x;t={x:-s/(e.dy/e.dx),y:s},o={x:t.x+r.width,y:t.y+r.height}}}}t&&o&&(ji(a,i,t),ji(a,s,o),e=!0)}if(!e)return;a.lineWidth=c,l.enable&&(a.globalCompositeOperation=l.composite),a.strokeStyle=Ii(h,d);const{shadow:p}=u;if(p.enable){const t=Mi(o,p.color);t&&(a.shadowBlur=p.blur,a.shadowColor=Ii(t))}a.stroke()}({context:e,width:d,begin:a,end:r,engine:this._engine,maxDistance:u,canvasSize:s.canvas.size,links:i,backgroundMask:p,colorLine:l,opacity:c})}))},this._drawLinkTriangle=(t,e,i)=>{const s=t.options.links;if(!s?.enable)return;const o=s.triangles;if(!o.enable)return;const n=this._container,a=n.actualOptions,r=e.destination,c=i.destination,l=o.opacity??.5*(e.opacity+i.opacity);l<=0||n.canvas.draw((e=>{const i=t.getPosition(),h=r.getPosition(),d=c.getPosition(),u=t.retina.linksDistance??0;if(fe(i,h)>u||fe(d,h)>u||fe(d,i)>u)return;let p=Mi(this._engine,o.color);if(!p){const e=void 0!==s.id?n.particles.linksColors.get(s.id):n.particles.linksColor;p=Ai(t,r,e)}p&&function(t){const{context:e,pos1:i,pos2:s,pos3:o,backgroundMask:n,colorTriangle:a,opacityTriangle:r}=t;!function(t,e,i,s){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.lineTo(s.x,s.y),t.closePath()}(e,i,s,o),n.enable&&(e.globalCompositeOperation=n.composite),e.fillStyle=Ii(a,r),e.fill()}({context:e,pos1:i,pos2:h,pos3:d,backgroundMask:a.backgroundMask,colorTriangle:p,opacityTriangle:l})}))},this._drawTriangles=(t,e,i,s)=>{const o=i.destination;if(!t.links?.triangles.enable||!o.options.links?.triangles.enable)return;const n=o.links?.filter((t=>{const e=this._getLinkFrequency(o,t.destination);return o.options.links&&e<=o.options.links.frequency&&s.findIndex((e=>e.destination===t.destination))>=0}));if(n?.length)for(const s of n){const n=s.destination;this._getTriangleFrequency(e,o,n)>t.links.triangles.frequency||this._drawLinkTriangle(e,i,s)}},this._getLinkFrequency=(t,e)=>Ua([t,e],this._freqs.links),this._getTriangleFrequency=(t,e,i)=>Ua([t,e,i],this._freqs.triangles),this._container=t,this._engine=e,this._freqs={links:new Map,triangles:new Map}}drawParticle(t,e){const{links:i,options:s}=e;if(!i?.length)return;const o=i.filter((t=>s.links&&(s.links.frequency>=1||this._getLinkFrequency(e,t.destination)<=s.links.frequency)));for(const t of o)this._drawTriangles(s,e,t,o),t.opacity>0&&(e.retina.linksWidth??0)>0&&this._drawLinkLine(e,t)}async init(){this._freqs.links=new Map,this._freqs.triangles=new Map,await Promise.resolve()}particleCreated(t){if(t.links=[],!t.options.links)return;const e=this._container.retina.pixelRatio,{retina:i}=t,{distance:s,width:o}=t.options.links;i.linksDistance=s*e,i.linksWidth=o*e}particleDestroyed(t){t.links=[]}}class ja{constructor(t){this.id="links",this._engine=t}getPlugin(t){return Promise.resolve(new Wa(t,this._engine))}loadOptions(){}needsPlugin(){return!0}}async function $a(t,e=!0){t.checkVersion("3.8.1"),await async function(t,e=!0){await t.addInteractor("particlesLinks",(async e=>Promise.resolve(new Ha(e,t))),e)}(t,e),await async function(t,e=!0){const i=new ja(t);await t.addPlugin(i,e)}(t,e)}const Ga=0,Na=0;class Qa{draw(t){const{particle:e,radius:i}=t;!function(t,e,i){const{context:s}=t,o=i.count.numerator*i.count.denominator,n=i.count.numerator/i.count.denominator,a=180*(n-2)/n,r=Math.PI-ge(a);if(s){s.beginPath(),s.translate(e.x,e.y),s.moveTo(Ga,Na);for(let t=0;t<o;t++)s.lineTo(i.length,Na),s.translate(i.length,Na),s.rotate(r)}}(t,this.getCenter(e,i),this.getSidesData(e,i))}getSidesCount(t){const e=t.shapeData;return Math.round(le(e?.sides??5))}}class Xa extends Qa{constructor(){super(...arguments),this.validTypes=["polygon"]}getCenter(t,e){return{x:-e/(t.sides/3.5),y:-e/.76}}getSidesData(t,e){const i=t.sides;return{count:{denominator:1,numerator:i},length:2.66*e/(i/3)}}}class Ya extends Qa{constructor(){super(...arguments),this.validTypes=["triangle"]}getCenter(t,e){return{x:-e,y:e/1.66}}getSidesCount(){return 3}getSidesData(t,e){return{count:{denominator:2,numerator:3},length:2*e}}}async function Za(t,e=!0){t.checkVersion("3.8.1"),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new Xa,e)}(t,e),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new Ya,e)}(t,e)}class Ka{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(t){Yt(t)||(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=ue(t.speed)),void 0!==t.decay&&(this.decay=ue(t.decay)),void 0!==t.sync&&(this.sync=t.sync))}}class Ja extends Ts{constructor(){super(),this.animation=new Ka,this.direction=Oo.clockwise,this.path=!1,this.value=0}load(t){Yt(t)||(super.load(t),void 0!==t.direction&&(this.direction=t.direction),this.animation.load(t.animation),void 0!==t.path&&(this.path=t.path))}}const tr=2*Math.PI;class er{constructor(t){this.container=t}init(t){const e=t.options.rotate;if(!e)return;t.rotate={enable:e.animation.enable,value:ge(le(e.value)),min:0,max:tr},t.pathRotation=e.path;let i=e.direction;if(i===Oo.random){i=Math.floor(2*ie())>0?Oo.counterClockwise:Oo.clockwise}switch(i){case Oo.counterClockwise:case"counterClockwise":t.rotate.status=Ce.decreasing;break;case Oo.clockwise:t.rotate.status=Ce.increasing}const s=e.animation;s.enable&&(t.rotate.decay=1-le(s.decay),t.rotate.velocity=le(s.speed)/360*this.container.retina.reduceFactor,s.sync||(t.rotate.velocity*=ie())),t.rotation=t.rotate.value}isEnabled(t){const e=t.options.rotate;return!!e&&(!t.destroyed&&!t.spawning&&(!!e.value||e.animation.enable||e.path))}loadOptions(t,...e){t.rotate||(t.rotate=new Ja);for(const i of e)t.rotate.load(i?.rotate)}update(t,e){this.isEnabled(t)&&(t.isRotating=!!t.rotate,t.rotate&&(ci(t,t.rotate,!1,Pe.none,e),t.rotation=t.rotate.value))}}const ir=Math.sqrt(2);class sr{constructor(){this.validTypes=["edge","square"]}draw(t){!function(t){const{context:e,radius:i}=t,s=i/ir,o=2*s;e.rect(-s,-s,o,o)}(t)}getSidesCount(){return 4}}const or=0,nr=0;class ar{constructor(){this.validTypes=["star"]}draw(t){!function(t){const{context:e,particle:i,radius:s}=t,o=i.sides,n=i.starInset??2;e.moveTo(or,nr-s);for(let t=0;t<o;t++)e.rotate(Math.PI/o),e.lineTo(or,nr-s*n),e.rotate(Math.PI/o),e.lineTo(or,nr-s)}(t)}getSidesCount(t){const e=t.shapeData;return Math.round(le(e?.sides??5))}particleInit(t,e){const i=e.shapeData;e.starInset=le(i?.inset??2)}}class rr{constructor(t,e){this._container=t,this._engine=e}init(t){const e=this._container,i=t.options,s=ii(i.stroke,t.id,i.reduceDuplicates);t.strokeWidth=le(s.width)*e.retina.pixelRatio,t.strokeOpacity=le(s.opacity??1),t.strokeAnimation=s.color?.animation;const o=Pi(this._engine,s.color)??t.getFillColor();o&&(t.strokeColor=Vi(o,t.strokeAnimation,e.retina.reduceFactor))}isEnabled(t){const e=t.strokeAnimation,{strokeColor:i}=t;return!t.destroyed&&!t.spawning&&!!e&&(void 0!==i?.h.value&&i.h.enable||void 0!==i?.s.value&&i.s.enable||void 0!==i?.l.value&&i.l.enable)}update(t,e){this.isEnabled(t)&&Wi(t.strokeColor,e)}}async function cr(t,e=!0){t.checkVersion("3.8.1"),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addMover("parallax",(()=>Promise.resolve(new Ma)),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalAttract",(e=>Promise.resolve(new dn(t,e))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalBounce",(t=>Promise.resolve(new xn(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalBubble",(e=>Promise.resolve(new On(e,t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalConnect",(t=>Promise.resolve(new Ln(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalGrab",(e=>Promise.resolve(new qn(e,t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalPause",(t=>Promise.resolve(new Vn(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalPush",(t=>Promise.resolve(new Un(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalRemove",(t=>Promise.resolve(new jn(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalRepulse",(e=>Promise.resolve(new Xn(t,e))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("externalSlow",(t=>Promise.resolve(new Zn(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("particlesAttract",(t=>Promise.resolve(new za(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addInteractor("particlesCollisions",(t=>Promise.resolve(new Ea(t))),e)}(t,!1),await $a(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addEasing(To.easeInQuad,(t=>t**2),!1),await t.addEasing(To.easeOutQuad,(t=>1-(1-t)**2),!1),await t.addEasing(To.easeInOutQuad,(t=>t<.5?2*t**2:1-(-2*t+2)**2/2),!1),await t.refresh(e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new nn,e)}(t,!1),await ya(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new ka,e)}(t,!1),await Za(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new sr,e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addShape(new ar,e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("life",(async t=>Promise.resolve(new _a(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("rotate",(t=>Promise.resolve(new er(t))),e)}(t,!1),await async function(t,e=!0){t.checkVersion("3.8.1"),await t.addParticleUpdater("strokeColor",(e=>Promise.resolve(new rr(e,t))),e)}(t,!1),await sn(t,e)}return e})()));