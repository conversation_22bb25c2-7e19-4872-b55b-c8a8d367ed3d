/*! For license information please see tsparticles.shape.circle.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var r="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var o in r)("object"==typeof exports?exports:e)[o]=r[o]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},r={};function o(e){var n=r[e];if(void 0!==n)return n.exports;var i=r[e]={exports:{}};return t[e](i,i.exports,o),i.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};o.r(n),o.d(n,{loadCircleShape:()=>d});var i=o(303);const a=2*Math.PI,c=0,s=0;class p{constructor(){this.validTypes=["circle"]}draw(e){!function(e){const{context:t,particle:r,radius:o}=e;r.circleRange||(r.circleRange={min:0,max:a});const n=r.circleRange;t.arc(c,s,o,n.min,n.max,!1)}(e)}getSidesCount(){return 12}particleInit(e,t){const r=t.shapeData,o=r?.angle??{max:360,min:0};t.circleRange=(0,i.isObject)(o)?{min:(0,i.degToRad)(o.min),max:(0,i.degToRad)(o.max)}:{min:0,max:(0,i.degToRad)(o)}}}async function d(e,t=!0){e.checkVersion("3.8.1"),await e.addShape(new p,t)}return n})()));