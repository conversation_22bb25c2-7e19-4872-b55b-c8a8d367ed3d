import type { EmittersEngine } from "./EmittersEngine.js";
export declare function loadEmittersPlugin(engine: EmittersEngine, refresh?: boolean): Promise<void>;
export * from "./EmitterContainer.js";
export * from "./EmitterShapeBase.js";
export * from "./EmittersEngine.js";
export * from "./IEmitterShape.js";
export * from "./IEmitterShapeGenerator.js";
export * from "./Enums/EmitterClickMode.js";
export * from "./IRandomPositionData.js";
