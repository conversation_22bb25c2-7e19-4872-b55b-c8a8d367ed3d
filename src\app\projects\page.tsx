'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { ExternalLink, Github, Filter } from 'lucide-react';
import { Tilt } from 'react-parallax-tilt';

gsap.registerPlugin(ScrollTrigger);

interface Project {
  id: number;
  title: string;
  description: string;
  longDescription: string;
  technologies: string[];
  liveUrl: string;
  githubUrl?: string;
  image: string;
  category: string;
  featured: boolean;
}

export default function ProjectsPage() {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const projects: Project[] = [
    {
      id: 1,
      title: 'Route Tracker',
      description: 'Real-time vehicle tracking and movement visualization system',
      longDescription: 'Developed a comprehensive web-based application focused on visualizing and managing vehicle movement, leveraging the power of React and Redux for efficient state management and seamless user experience. Implemented real-time data visualization to track and display vehicle movement, providing users with instant access to critical information.',
      technologies: ['React.js', 'Redux', 'JavaScript', 'CSS3', 'Real-time APIs'],
      liveUrl: 'https://saurabhd.vercel.app/map',
      githubUrl: '#',
      image: '/api/placeholder/600/400',
      category: 'web-app',
      featured: true,
    },
    {
      id: 2,
      title: 'Camping Grounds',
      description: 'Full-stack camping site management platform with secure authentication',
      longDescription: 'Developed "CampingGrounds" website with MongoDB/Mongoose for data, Express.js for backend, and EJS for dynamic views. Ensured secure user authentication for campground creation and author-exclusive deletion rights. Utilized Bootstrap for a visually appealing, responsive interface. Demonstrated proficiency in full-stack web development, prioritizing security and usability.',
      technologies: ['MongoDB', 'Express.js', 'EJS', 'Node.js', 'Bootstrap', 'Authentication'],
      liveUrl: 'https://campinggrounds.onrender.com/',
      githubUrl: '#',
      image: '/api/placeholder/600/400',
      category: 'full-stack',
      featured: true,
    },
  ];

  const categories = [
    { id: 'all', name: 'All Projects' },
    { id: 'web-app', name: 'Web Apps' },
    { id: 'full-stack', name: 'Full Stack' },
    { id: 'frontend', name: 'Frontend' },
    { id: 'backend', name: 'Backend' },
  ];

  useEffect(() => {
    if (selectedFilter === 'all') {
      setFilteredProjects(projects);
    } else {
      setFilteredProjects(projects.filter(project => project.category === selectedFilter));
    }
  }, [selectedFilter]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.fromTo(
        '.project-card',
        { opacity: 0, y: 50, scale: 0.9 },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: '.projects-grid',
            start: 'top 80%',
            toggleActions: 'play none none reverse',
          },
        }
      );
    }, sectionRef);

    return () => ctx.revert();
  }, [filteredProjects]);

  return (
    <div ref={sectionRef} className="min-h-screen pt-20 pb-10 px-4 md:px-10 space-y-8">
      <div className="max-w-[90vw] mx-auto">
        {/* Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold gradient-text font-orbitron mb-6">
            My Projects
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            A showcase of my work, featuring web applications and full-stack solutions 
            built with modern technologies
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedFilter(category.id)}
              className={`glass px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                selectedFilter === category.id
                  ? 'text-neon-blue glow-box'
                  : 'text-gray-300 hover:text-neon-green'
              }`}
            >
              <Filter size={16} className="inline mr-2" />
              {category.name}
            </button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <div className="projects-grid grid md:grid-cols-2 gap-8">
          {filteredProjects.map((project, index) => (
            <Tilt
              key={project.id}
              tiltMaxAngleX={10}
              tiltMaxAngleY={10}
              perspective={1000}
              scale={1.02}
              transitionSpeed={1000}
              className="project-card"
            >
              <motion.div
                className="glass-strong rounded-2xl overflow-hidden hover:glow-box transition-all duration-500 h-full"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.1 }}
              >
                {/* Project Image */}
                <div className="relative h-64 bg-gradient-to-br from-neon-blue/20 to-neon-purple/20 flex items-center justify-center">
                  <div className="text-6xl font-bold text-white/20 font-orbitron">
                    {project.title.split(' ').map(word => word[0]).join('')}
                  </div>
                  {project.featured && (
                    <div className="absolute top-4 right-4 bg-neon-green text-black px-3 py-1 rounded-full text-sm font-semibold">
                      Featured
                    </div>
                  )}
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <h3 className="text-2xl font-bold text-white mb-3 font-space">
                    {project.title}
                  </h3>
                  
                  <p className="text-gray-300 mb-4 leading-relaxed">
                    {project.description}
                  </p>

                  <p className="text-gray-400 text-sm mb-6 leading-relaxed">
                    {project.longDescription}
                  </p>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="glass px-3 py-1 rounded-full text-xs text-neon-blue font-medium"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    <motion.a
                      href={project.liveUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex-1 glass px-4 py-3 rounded-lg text-center text-neon-green hover:text-white hover:glow-box transition-all duration-300 font-medium"
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <ExternalLink size={16} className="inline mr-2" />
                      Live Demo
                    </motion.a>
                    
                    {project.githubUrl && (
                      <motion.a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="glass px-4 py-3 rounded-lg text-neon-blue hover:text-white hover:glow-box transition-all duration-300"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Github size={16} />
                      </motion.a>
                    )}
                  </div>
                </div>
              </motion.div>
            </Tilt>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="glass-strong p-8 rounded-2xl max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-neon-blue mb-4 font-space">
              Want to see more?
            </h3>
            <p className="text-lg text-gray-300 mb-6">
              I'm constantly working on new projects and learning new technologies. 
              Check out my GitHub for more repositories and contributions.
            </p>
            <motion.a
              href="https://github.com/saurabhdahariya"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center glass px-8 py-4 rounded-full text-neon-green hover:text-white hover:glow-box transition-all duration-300 font-semibold"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Github size={20} className="mr-2" />
              View GitHub Profile
            </motion.a>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
