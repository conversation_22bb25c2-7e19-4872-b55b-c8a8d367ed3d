/*! For license information please see tsparticles.shape.text.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("@tsparticles/engine"));else if("function"==typeof define&&define.amd)define(["@tsparticles/engine"],t);else{var o="object"==typeof exports?t(require("@tsparticles/engine")):t(e.window);for(var i in o)("object"==typeof exports?exports:e)[i]=o[i]}}(this,(e=>(()=>{var t={303:t=>{t.exports=e}},o={};function i(e){var n=o[e];if(void 0!==n)return n.exports;var r=o[e]={exports:{}};return t[e](r,r.exports,i),r.exports}i.d=(e,t)=>{for(var o in t)i.o(t,o)&&!i.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};i.r(n),i.d(n,{loadTextShape:()=>c});var r=i(303);const a=2,s=.5;function l(e,t,o,i,n,r){const l={x:-(t.length*o*s),y:o*s},p=o*a;r?e.fillText(t,l.x,l.y+p*n):e.strokeText(t,l.x,l.y+p*n)}class p{constructor(){this.validTypes=["text","character","char","multiline-text"]}draw(e){!function(e){const{context:t,particle:o,radius:i,opacity:n}=e,s=o.shapeData;if(!s)return;const p=s.value;if(void 0===p)return;void 0===o.text&&(o.text=(0,r.itemFromSingleOrMultiple)(p,o.randomIndexData));const c=o.text,d=s.style??"",f=s.weight??"400",u=Math.round(i)*a,x=s.font??"Verdana",y=o.shapeFill,h=c?.split("\n");if(h){t.font=`${d} ${f} ${u}px "${x}"`,t.globalAlpha=n;for(let e=0;e<h.length;e++)l(t,h[e],i,0,e,y);t.globalAlpha=1}}(e)}async init(e){const t=e.actualOptions,{validTypes:o}=this;if(o.find((e=>(0,r.isInArray)(e,t.particles.shape.type)))){const e=o.map((e=>t.particles.shape.options[e])).find((e=>!!e)),i=[];(0,r.executeOnSingleOrMultiple)(e,(e=>{i.push((0,r.loadFont)(e.font,e.weight))})),await Promise.all(i)}}particleInit(e,t){if(!t.shape||!this.validTypes.includes(t.shape))return;const o=t.shapeData;if(void 0===o)return;const i=o.value;void 0!==i&&(t.text=(0,r.itemFromSingleOrMultiple)(i,t.randomIndexData))}}async function c(e,t=!0){e.checkVersion("3.8.1"),await e.addShape(new p,t)}return n})()));