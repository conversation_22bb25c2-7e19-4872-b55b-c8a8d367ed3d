# 🚀 <PERSON><PERSON><PERSON><PERSON> - Modern AI-Powered Portfolio

A sleek, modern portfolio website built with Next.js 14+, featuring AI-powered interactions, smooth animations, and contemporary design principles.

![Portfolio Preview](https://img.shields.io/badge/Status-Live-brightgreen)
![Next.js](https://img.shields.io/badge/Next.js-14+-black)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue)
![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.0+-38bdf8)

## ✨ Features

### 🎬 **Cinematic Splash Animation**
- **Sequence**: "Hello World" → "I am" → "<PERSON><PERSON><PERSON><PERSON>" → "Software Developer"
- **Clean transitions**: Text fades in/out with smooth timing
- **Name morphing**: <PERSON><PERSON><PERSON><PERSON> floats to navbar using Framer Motion layoutId
- **Minimalist design**: Apple-style intro with subtle background blur

### 🤖 **Modern AI Assistant**
- **Always visible** at bottom center with gradient design
- **Wave animations** during voice listening
- **OpenAI GPT-3.5-turbo** integration for intelligent responses
- **Auto-navigation** based on user queries
- **Voice input** with visual feedback and mic toggle
- **Modern UI** with backdrop blur and smooth animations

### 🎨 **Modern UI/UX**
- **Glassmorphism & Neumorphism** effects
- **3D Background** with Vanta.js (Net effect)
- **Custom cursor** with glow trail
- **Real tech logos** instead of text
- **Infinite scrolling marquees**
- **Hover animations** and magnetic effects

### 📱 **Responsive Pages**
- **Home**: Hero with typewriter, tech logos, scroll animations
- **Projects**: 3D tilt cards with filtering
- **Resume**: Timeline layout with download option
- **Contact**: Animated form with validation

### ⚡ **Advanced Animations**
- GSAP + ScrollTrigger for scroll-based animations
- Framer Motion for component transitions
- 3D card effects with react-parallax-tilt
- Gradient text animations
- Floating elements and particles

## 🛠️ Tech Stack

### **Frontend**
- **Next.js 14+** - React framework with App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first styling
- **Framer Motion** - Animation library
- **GSAP** - Advanced animations

### **3D & Visual Effects**
- **Vanta.js** - 3D background effects
- **Three.js** - 3D graphics
- **React Three Fiber** - React Three.js integration
- **React Parallax Tilt** - 3D card effects

### **AI & Interactions**
- **OpenAI API** - GPT-3.5-turbo for AI responses
- **React Hook Form** - Form handling
- **Zod** - Schema validation

### **UI Components**
- **Lucide React** - Modern icons
- **React Fast Marquee** - Infinite scrolling
- **Typewriter Effect** - Typing animations

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/saurabhdahariya/saurabh-portfolio-ai.git
cd saurabh-portfolio-ai
```

2. **Install dependencies**
```bash
npm install
```

3. **Set up environment variables**
```bash
cp .env.local.example .env.local
```

Add your OpenAI API key to `.env.local`:
```env
OPENAI_API_KEY=your_openai_api_key_here
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

4. **Run the development server**
```bash
npm run dev
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 AI Assistant Features

The AI assistant knows everything about Saurabh and can:

- **Answer questions** about projects, skills, experience
- **Auto-navigate** to relevant pages based on queries
- **Provide contact information** and professional details
- **Discuss technical expertise** and project details

### Example Queries:
- "Tell me about your projects"
- "What are your skills?"
- "How can I contact you?"
- "Show me your resume"

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/ai-chat/       # OpenAI API route
│   ├── projects/          # Projects page
│   ├── resume/            # Resume page
│   ├── contact/           # Contact page
│   └── layout.tsx         # Root layout
├── components/
│   ├── ui/                # Reusable UI components
│   │   ├── SplashScreen.tsx
│   │   ├── Navigation.tsx
│   │   ├── AIAssistant.tsx
│   │   ├── CustomCursor.tsx
│   │   ├── TechLogos.tsx
│   │   └── VantaBackground.tsx
│   └── sections/          # Page sections
│       ├── HeroSection.tsx
│       ├── AboutSection.tsx
│       └── TechStackSection.tsx
└── styles/
    └── globals.css        # Global styles & animations
```

## 🎨 Customization

### **Colors & Theme**
Edit `tailwind.config.ts` to customize:
- Neon colors (blue, green, purple, pink)
- Glass effects opacity
- Animation durations

### **Personal Information**
Update your details in:
- `src/app/api/ai-chat/route.ts` - AI assistant knowledge
- Component files for contact info, projects, etc.

### **Projects**
Add your projects in `src/app/projects/page.tsx`:
```typescript
const projects = [
  {
    title: "Your Project",
    description: "Project description",
    technologies: ["React", "Node.js"],
    liveUrl: "https://your-project.com",
    // ...
  }
];
```

## 🚀 Deployment

### **Vercel (Recommended)**
1. Push to GitHub
2. Connect to Vercel
3. Add environment variables
4. Deploy automatically

### **Other Platforms**
- **Netlify**: `npm run build && npm run export`
- **Railway**: Direct GitHub integration
- **AWS/DigitalOcean**: Docker deployment

## 📊 Performance

- **Lighthouse Score**: 95+
- **Core Web Vitals**: Optimized
- **Bundle Size**: Optimized with code splitting
- **SEO**: Meta tags and structured data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 📞 Contact

**Saurabh Dahariya**
- 📧 Email: <EMAIL>
- 📱 Phone: +91 **********
- 🌍 Location: Bengaluru, India
- 💼 GitHub: [@saurabhdahariya](https://github.com/saurabhdahariya)

---

⭐ **Star this repo if you found it helpful!**
