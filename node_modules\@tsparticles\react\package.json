{"name": "@tsparticles/react", "version": "3.0.0", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:ci": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepublishOnly": "pnpm run build"}, "files": ["dist"], "peerDependencies": {"@tsparticles/engine": "^3.0.2", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@tsparticles/engine": "^3.0.2", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "glob": "^10.3.10", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "vite": "^5.0.6", "vite-plugin-dts": "^3.6.4", "vite-plugin-lib-inject-css": "^1.3.0"}, "publishConfig": {"access": "public"}, "gitHead": "06ad7ff6a21833bb7c62c443b2cfb6bbcba58273"}